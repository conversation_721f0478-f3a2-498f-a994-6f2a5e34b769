.no_template_main {
  height: calc(100vh - 293px);
}
.small-text {
  font-size: 14px;
  color: #8f9bb3;
}
.header_greyed_text {
  font-size: 12px;
  color: #8f9bb3;
}
.video_player {
  .expand-icon {
    position: absolute;
    top: 0;
    right: 0;
  }
  &.lg_screen {
    iframe {
      width: 100%;
      height: 350px;
    }
  }
}

.modal-md {
  .modal-footer {
    padding: 1rem 0 0 0 !important;
  }
}

@media (min-width: 992px) {
  .modal-lg {
    max-width: 800px !important;
  }
}

.template-dropZone {
  height: 150px !important;
  padding: 1rem !important;
  text-align: center;
  border-radius: 6px;
  position: relative;
  margin: 0 auto;
  cursor: pointer !important;
}

.template-dropZone input {
  opacity: 0;
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  cursor: pointer !important;
}

::ng-deep .modal-body {
  .ng-dropdown-panel {
    width: 100% !important;
  }
}
