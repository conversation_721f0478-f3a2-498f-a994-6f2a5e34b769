<nb-card class="siteDeviceSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large" #bulkTicketCreation>
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Bulk Tickets Creation</h6>
        <div class="ms-auto">
          <button type="button" class="close" aria-label="Close" (click)="closeModel()">
            <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 my-3 d-flex align-items-center">
        <h5 class="mb-0" *ngIf="currentStep === 1">Select Ticket Type</h5>
        <h5 class="mb-0" *ngIf="currentStep === 2">
          {{ selectedTicketType ? (selectedTicketType === 1 ? 'Select Sites' : 'Select Devices') : '' }}
        </h5>
        <h5 class="mb-0" *ngIf="currentStep === 3">Select Ticket Details</h5>
        <h5 class="mb-0" *ngIf="currentStep === 4">Send Email(s)?</h5>
        <h5 class="mb-0" *ngIf="currentStep === 5">Confirmation</h5>
        <h5 class="mb-0" *ngIf="currentStep === 6">Status</h5>
        <button
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          status="primary"
          size="small"
          type="button"
          (click)="SaveBulkCreateAction()"
          [disabled]="!selectedBulkCreateTickets?.length"
          *ngIf="currentStep === 5"
        >
          Confirm And Create
        </button>
        <button
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          status="primary"
          size="small"
          type="button"
          (click)="proceedNextStep(bulkCreationForm)"
          [disabled]="
            currentStep === 3 || currentStep === 4
              ? !bulkCreationForm.form.valid
              : currentStep === 2
              ? !selectedBulkCreateTickets?.length
              : false
          "
          *ngIf="currentStep !== 5 && currentStep !== 6 && currentStep !== 1"
        >
          Next
        </button>
        <button
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          status="primary"
          size="small"
          type="button"
          (click)="proceedNextStep(bulkCreationForm)"
          [disabled]="!selectedTicketType"
          *ngIf="currentStep === 1"
        >
          Next
        </button>
        <button
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          status="primary"
          size="small"
          type="button"
          (click)="proceedBackStep()"
          [disabled]="loading"
          *ngIf="currentStep !== 6"
        >
          Back
        </button>
        <button
          *ngIf="currentStep !== 6"
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          size="small"
          type="button"
          (click)="closeModel()"
          [disabled]="loading"
        >
          Cancel
        </button>
      </div>
    </div>
    <ng-container></ng-container>
    <div class="row mt-3 bulk-creation-form">
      <ng-container *ngIf="currentStep === 1">
        <div class="col-12 d-flex align-items-center justify-content-start">
          <div>
            <nb-radio-group
              class=""
              name="selectedTicketTypeRadio"
              #selectedTicketTypeRadio="ngModel"
              [(ngModel)]="selectedTicketType"
              (valueChange)="onTicketTypeChange()"
              [required]="currentStep === 1"
            >
              <nb-radio [value]="1">Site - Create tickets by selecting a list of Sites. A ticket will be created for each Site.</nb-radio>
              <nb-radio [value]="2"
                >Device - Create tickets by selecting a list of Devices. A ticket will be created for each Device</nb-radio
              >
            </nb-radio-group>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="currentStep === 2">
        <div class="col-12 ticketFilter appFilter mb-3">
          <sfl-filter
            *ngIf="showCommonFilter"
            [filterDetails]="filterDetails"
            (refreshList)="refreshList($event)"
            (refreshTableHeight)="this.isFilterDisplay = $event"
            [filterAppendPosition]="''"
          ></sfl-filter>
        </div>
        <div class="col-12 ticketFilter appFilter mb-3">
          <ng-container *ngIf="selectedTicketType === 1">
            <div
              id="fixed-table"
              setTableHeight
              [isFilterDisplay]="isFilterDisplay"
              class="col-12 table-responsive table-card-view"
              *ngIf="currentStep === 2"
            >
              <table class="table table-hover table-bordered" aria-describedby="Site List">
                <thead>
                  <tr>
                    <th scope="col" id="select-all" class="text-center">
                      <nb-checkbox
                        id="select-all-{{ selectedTicketType }}"
                        class="sfl-track-checkbox"
                        [(ngModel)]="isMasterSel"
                        (change)="selectDeselectAll()"
                        name="selectAllSites"
                      >
                      </nb-checkbox>
                    </th>
                    <th scope="col" (click)="sort('QeSiteId', sortOptionList['QeSiteId'])" id="siteID">
                      <div class="d-flex justify-content-center align-items-center">
                        Site ID
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['QeSiteId'] === 'desc',
                            'fa-arrow-down': sortOptionList['QeSiteId'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'QeSiteId'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('SiteName', sortOptionList['SiteName'])" id="SiteName">
                      <div class="d-flex justify-content-center align-items-center">
                        Site Name
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['SiteName'] === 'desc',
                            'fa-arrow-down': sortOptionList['SiteName'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'SiteName'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col">Customer (Portfolio)</th>
                    <th scope="col" (click)="sort('ACSize', sortOptionList['ACSize'])">
                      <div class="d-flex justify-content-center align-items-center">
                        AC Size
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['ACSize'] === 'desc',
                            'fa-arrow-down': sortOptionList['ACSize'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'ACSize'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('DCSize', sortOptionList['DCSize'])">
                      <div class="d-flex justify-content-center align-items-center">
                        DC Size
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['DCSize'] === 'desc',
                            'fa-arrow-down': sortOptionList['DCSize'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'DCSize'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('INV', sortOptionList['INV'])">
                      <div class="d-flex justify-content-center align-items-center">
                        #INV
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['INV'] === 'desc',
                            'fa-arrow-down': sortOptionList['INV'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'INV'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('XFMR', sortOptionList['XFMR'])">
                      <div class="d-flex justify-content-center align-items-center">
                        #XFMR
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['XFMR'] === 'desc',
                            'fa-arrow-down': sortOptionList['XFMR'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'XFMR'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('SiteDeviceCount', sortOptionList['SiteDeviceCount'])">
                      <div class="d-flex justify-content-center align-items-center">
                        #Devices
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['SiteDeviceCount'] === 'desc',
                            'fa-arrow-down': sortOptionList['SiteDeviceCount'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'SiteDeviceCount'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('state', sortOptionList['state'])">
                      <div class="d-flex justify-content-center align-items-center">
                        State
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['state'] === 'desc',
                            'fa-arrow-down': sortOptionList['state'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'state'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('RegionName', sortOptionList['RegionName'])">
                      <div class="d-flex justify-content-center align-items-center">
                        Region
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['RegionName'] === 'desc',
                            'fa-arrow-down': sortOptionList['RegionName'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'RegionName'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('SubRegionName', sortOptionList['SubRegionName'])">
                      <div class="d-flex justify-content-center align-items-center">
                        Subregion
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['SubRegionName'] === 'desc',
                            'fa-arrow-down': sortOptionList['SubRegionName'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'SubRegionName'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col">Active</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngIf="siteDeviceList?.length">
                    <tr
                      *ngFor="
                        let siteItem of siteDeviceList
                          | paginate
                            : {
                                itemsPerPage: pageSize,
                                currentPage: currentPage,
                                totalItems: total,
                                id: selectedTicketType
                              }
                      "
                    >
                      <td data-title="Select Site" class="text-center">
                        <nb-checkbox
                          id="select-file+{{ siteItem.id }}"
                          class="sfl-track-checkbox"
                          name="selectSingleSite+{{ siteItem.id }}"
                          (change)="singleSiteDeviceCheckChanged(siteItem)"
                          [(ngModel)]="siteItem.isSelected"
                          [checked]="siteItem.isSelected"
                        >
                        </nb-checkbox>
                      </td>
                      <td data-title="Site ID">{{ siteItem?.qeSiteId }}</td>
                      <td data-title="Site Name">{{ siteItem?.siteName }}</td>
                      <td data-title="Customer (Portfolio)">{{ siteItem?.customerPortfolio }}</td>
                      <td data-title="AC Size">{{ siteItem?.acSize | sflRound | sflNumberWithCommas }}</td>
                      <td data-title="DC Size">{{ siteItem?.dcSize | sflRound | sflNumberWithCommas }}</td>
                      <td data-title="#INV">{{ siteItem?.inv }}</td>
                      <td data-title="#XFMR">{{ siteItem?.totalXfmr }}</td>
                      <td data-title="#Devices">
                        <span> {{ siteItem?.siteDeviceCount }} </span>
                      </td>
                      <td data-title="State">{{ siteItem?.state }}</td>
                      <td data-title="Region">{{ siteItem?.regionName }}</td>
                      <td data-title="Subregion">{{ siteItem?.subRegionName }}</td>
                      <td data-title="Active"><nb-toggle [(checked)]="siteItem.isActive" disabled></nb-toggle></td>
                    </tr>
                  </ng-container>
                  <ng-container *ngIf="!siteDeviceList?.length">
                    <tr>
                      <td colspan="13" class="no-record text-center">No Data Found</td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
          </ng-container>
          <ng-container *ngIf="selectedTicketType === 2">
            <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view" *ngIf="currentStep === 2">
              <table class="table table-hover table-bordered" aria-describedby="Device List">
                <thead>
                  <tr>
                    <th scope="col" id="select-all-{{ selectedTicketType }}" class="text-center">
                      <nb-checkbox
                        id="select-all"
                        class="sfl-track-checkbox"
                        [(ngModel)]="isMasterSel"
                        (change)="selectDeselectAll()"
                        name="selectAllDevices"
                      >
                      </nb-checkbox>
                    </th>
                    <th scope="col" (click)="sort('Site', sortOptionList['Site'])">
                      <div class="d-flex justify-content-center align-items-center">
                        Site Name
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['Site'] === 'desc',
                            'fa-arrow-down': sortOptionList['Site'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'Site'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('DeviceID', sortOptionList['DeviceID'])">
                      <div class="d-flex justify-content-center align-items-center">
                        API ID
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['DeviceID'] === 'desc',
                            'fa-arrow-down': sortOptionList['DeviceID'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'DeviceID'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('DeviceName', sortOptionList['DeviceName'])">
                      <div class="d-flex justify-content-center align-items-center">
                        Device Name
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['DeviceName'] === 'desc',
                            'fa-arrow-down': sortOptionList['DeviceName'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'DeviceName'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col" (click)="sort('DeviceType', sortOptionList['DeviceType'])">
                      <div class="d-flex justify-content-center align-items-center">
                        Device Type
                        <span
                          class="fa cursor-pointer ms-2"
                          [ngClass]="{
                            'fa-arrow-up': sortOptionList['DeviceType'] === 'desc',
                            'fa-arrow-down': sortOptionList['DeviceType'] === 'asc',
                            'icon-selected': filterModel.sortBy === 'DeviceType'
                          }"
                        ></span>
                      </div>
                    </th>
                    <th scope="col">Model</th>
                    <th scope="col">Size</th>
                    <th scope="col">Manufacturer</th>
                    <th scope="col">DC Load(kW)</th>
                    <th scope="col">Serial Number</th>
                    <th scope="col">Active</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngIf="siteDeviceList?.length">
                    <tr
                      *ngFor="
                        let deviceItem of siteDeviceList
                          | paginate
                            : {
                                itemsPerPage: pageSize,
                                currentPage: currentPage,
                                totalItems: total,
                                id: selectedTicketType
                              }
                      "
                    >
                      <td data-title="Select Ticket" class="text-center">
                        <nb-checkbox
                          id="select-file+{{ deviceItem.id }}"
                          class="sfl-track-checkbox"
                          name="selectSingleDevice+{{ deviceItem.id }}"
                          (change)="singleSiteDeviceCheckChanged(deviceItem)"
                          [(ngModel)]="deviceItem.isSelected"
                          [checked]="deviceItem.isSelected"
                        >
                        </nb-checkbox>
                      </td>
                      <td data-title="Site Name">
                        <span>{{ deviceItem?.site }}</span>
                      </td>
                      <td data-title="API ID">
                        <span>{{ deviceItem?.deviceId }}</span>
                      </td>
                      <td data-title="Device Name">
                        <span>{{ deviceItem?.deviceLabel }}</span>
                      </td>
                      <td data-title="Device Type">{{ deviceItem?.deviceType }}</td>
                      <td data-title="Model">{{ deviceItem?.deviceModel }}</td>
                      <td data-title="Size">{{ deviceItem?.size }}</td>
                      <td data-title="Manufacturer">{{ deviceItem.mfg }}</td>
                      <td
                        data-title="DC Load(kW)"
                        [ngClass]="{
                          'missing-dc-load':
                            (deviceItem?.deviceType === 'Inverter' || deviceItem?.deviceType === 'Meter') && deviceItem?.dcLoad === ''
                        }"
                      >
                        {{ deviceItem.dcLoad }}
                      </td>
                      <td data-title="Serial Number">{{ deviceItem.serialNo }}</td>
                      <td data-title="Active">
                        <span><nb-toggle [(ngModel)]="deviceItem.isActive" disabled></nb-toggle></span>
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
          </ng-container>
          <div class="mt-2 d-md-flex align-items-center" *ngIf="siteDeviceList?.length && currentStep === 2">
            <div class="d-flex align-items-center">
              <label class="mb-0">Items per page: </label>
              <ng-select
                class="ms-2"
                [(ngModel)]="pageSize"
                [ngModelOptions]="{ standalone: true }"
                [clearable]="false"
                [searchable]="false"
                (change)="currentPage = 0"
                [id]="selectedTicketType"
              >
                <ng-option value="10">10</ng-option>
                <ng-option value="50">50</ng-option>
                <ng-option value="100">100</ng-option>
              </ng-select>
            </div>
            <strong class="ms-md-3">Total: {{ total }}</strong>
            <div class="ms-md-auto ms-sm-0">
              <pagination-controls (pageChange)="onPageChange($event)" class="paginate" [id]="selectedTicketType"></pagination-controls>
            </div>
          </div>
        </div>
      </ng-container>
      <div class="mt-3 bulk-create-form">
        <form name="bulkCreationForm" #bulkCreationForm="ngForm" aria-labelledby="title" autocomplete="off">
          <ng-container *ngIf="currentStep === 3">
            <div class="col-6">
              <div class="row">
                <div class="col">
                  <div>
                    <label class="label" for="input-Priority">Priority</label>
                    <ng-select
                      name="priority"
                      [items]="priorityList"
                      bindLabel="name"
                      bindValue="id"
                      #priority="ngModel"
                      [(ngModel)]="ticketBulkCreationForm.priority"
                      [required]="currentStep === 3"
                      notFoundText="No Priority Found"
                      placeholder="Select Priority"
                      [clearable]="false"
                    >
                    </ng-select>

                    <div class="error-message-section">
                      <sfl-error-msg
                        [control]="priority"
                        [isFormSubmitted]="bulkCreationForm?.submitted"
                        fieldName="Opened"
                      ></sfl-error-msg>
                    </div>
                  </div>
                </div>
                <div class="col">
                  <div>
                    <label class="label" for="Opened">Opened</label>
                    <input
                      nbInput
                      name="openedDate"
                      #openedDate="ngModel"
                      [(ngModel)]="ticketBulkCreationForm.openedDate"
                      placeholder="Select Opened Date"
                      fullWidth
                      [nbDatepicker]="openedDatePicker"
                      [required]="currentStep === 3"
                      (ngModelChange)="openDateChanged($event)"
                      readonly
                      autocomplete="off"
                    />
                    <nb-datepicker #openedDatePicker></nb-datepicker>
                    <div class="error-message-section">
                      <sfl-error-msg
                        [control]="openedDate"
                        [isFormSubmitted]="bulkCreationForm?.submitted"
                        fieldName="Opened"
                      ></sfl-error-msg>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <div>
                    <label class="label" for="input-Issue">Issue</label>
                    <textarea
                      nbInput
                      rows="8"
                      name="issue"
                      id="issue"
                      #issue="ngModel"
                      [(ngModel)]="ticketBulkCreationForm.issueTxt"
                      [required]="currentStep === 3"
                      placeholder="Issue"
                      maxlength="5120"
                      required
                      fullWidth
                    >
                    </textarea>
                    <div class="error-message-section">
                      <sfl-error-msg [control]="issue" [isFormSubmitted]="bulkCreationForm?.submitted" fieldName="Issue"></sfl-error-msg>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <div>
                    <label class="label" for="input-Production Loss">Production Loss</label>
                    <ng-select
                      name="productionLoss"
                      [items]="productionLossList"
                      (change)="productionLossChange($event)"
                      bindLabel="value"
                      bindValue="id"
                      #productionLoss="ngModel"
                      [(ngModel)]="ticketBulkCreationForm.productionLoss"
                      [required]="currentStep === 3"
                      notFoundText="No Production Loss Found"
                      placeholder="Select Production Loss"
                      [clearable]="false"
                    >
                    </ng-select>
                    <div class="error-message-section">
                      <sfl-error-msg
                        [control]="productionLoss"
                        [isFormSubmitted]="bulkCreationForm?.submitted"
                        fieldName="Production Loss"
                      ></sfl-error-msg>
                    </div>
                  </div>
                </div>
                <div class="col">
                  <div *ngIf="ticketBulkCreationForm.productionLoss === 0">
                    <label class="label" for="input-LossType">Loss Type</label>
                    <ng-select
                      name="lossType"
                      [items]="lossTypeList"
                      bindLabel="name"
                      bindValue="id"
                      #productionLoss="ngModel"
                      [(ngModel)]="ticketBulkCreationForm.lossType"
                      notFoundText="No Loss Type Found"
                      placeholder="Select Loss Type"
                      [clearable]="false"
                    >
                    </ng-select>
                  </div>
                </div>
                <div class="col">
                  <div *ngIf="ticketBulkCreationForm.productionLoss === 0">
                    <label class="label" for="input-Est.kWhLoss">Est. kWh Loss</label>
                    <div class="d-flex align-items-center">
                      <input
                        nbInput
                        name="estLoss"
                        #estLoss="ngModel"
                        [(ngModel)]="ticketBulkCreationForm.estKWHLoss"
                        placeholder="Est. kWh Loss"
                        fullWidth
                        sflValidators
                      />
                      <span class="ms-auto ps-1">kWh</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="currentStep === 4">
            <div>
              <nb-radio-group
                class="d-flex"
                name="isSendEmail"
                id="isSendEmail"
                #isSendEmail="ngModel"
                [required]="currentStep === 4"
                [(ngModel)]="ticketBulkCreationForm.isSendEmail"
              >
                <nb-radio [value]="true">Send Email(s)</nb-radio>
                <nb-radio [value]="false">Do Not Send Email(s)</nb-radio>
              </nb-radio-group>
            </div>
          </ng-container>
          <ng-container *ngIf="currentStep === 5">
            <div>
              <div>
                <p>{{ selectedBulkCreateTickets?.length }} Tickets will be created</p>
                <div>
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>Field Name</th>
                        <th>Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let field of getTableData()">
                        <td>{{ field.fieldName }}</td>
                        <td>{{ field.value }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                Email Notification {{ ticketBulkCreationForm.isSendEmail ? 'WILL' : 'WILL NOT' }} be sent for this update
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="currentStep === 6">
            <div>
              <p>Status</p>
              <p>
                Ticket bulk creation operation
                {{ apiCount === selectedBulkCreateTickets?.length ? 'completed successfully' : 'in progress...' }}
              </p>
              <p>creating {{ selectedBulkCreateTickets?.length }} Tickets</p>
              <p>{{ completedCount }}/{{ selectedBulkCreateTickets?.length }} Tickets Created</p>
              <ng-container *ngIf="failedEntityNumber?.length">
                <p class="fw-bold">Some records are not processed due to InActive Site(s)/Device(s), below is detail.</p>
                <ul class="multi-column-list">
                  <li *ngFor="let entity of failedEntityNumber">
                    {{ entity }}
                  </li>
                </ul>
              </ng-container>
            </div>
            <div class="mt-3">
              <button
                class="linear-mode-button ms-2 button-h-100"
                nbButton
                size="small"
                type="button"
                (click)="onCancel()"
                [disabled]="apiCount !== selectedBulkCreateTickets?.length"
              >
                Done
              </button>
            </div>
          </ng-container>
        </form>
      </div>
    </div>
  </nb-card-body>
</nb-card>
