import { Component, EventEmitter, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { TreeNode } from 'primeng/api';
import { Subject, Subscription } from 'rxjs';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { TicketDeviceModelList, TicketDeviceTypeList } from '../ticket.model';
import { TicketService } from '../ticket.service';
interface CustomTreeNode extends TreeNode {
  id: number;
}
@Component({
  selector: 'sfl-device-model',
  templateUrl: './device-model.component.html',
  styleUrls: ['./device-model.component.scss'],
  encapsulation: ViewEncapsulation?.None
})
export class DeviceModelComponent implements OnInit {
  public onClose: Subject<boolean>;
  @Input() deviceItem;
  @Input() ticketNumber;
  @Input() ticketId;
  @Input() siteId;
  @Input() ticketdetail;
  @Input() currentTicketNumber;
  loading = false;
  isSelectAll = false;
  selectedNodeIds = [];
  treeNodes: CustomTreeNode[];
  subscription: Subscription = new Subscription();
  public event: EventEmitter<any> = new EventEmitter();
  deviceTypeList: TicketDeviceTypeList[] = [];
  deviceModelList: TicketDeviceModelList[] = [];
  selectedDevice: TicketDeviceModelList;
  siteKwac: string;
  deviceItemsArray = [];

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly storageService: StorageService,
    private readonly ticketService: TicketService,
    private readonly alertService: AlertService
  ) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
    this.getAllSiteDeviceById();
    this.deviceItemsArray = JSON.parse(JSON.stringify(this.ticketdetail.ticketDeviceMaps));
  }

  public onConfirm(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  getAllSiteDeviceById() {
    this.loading = true;
    this.subscription.add(
      this.ticketService.getDeviceTypeBySiteId(this.siteId).subscribe({
        next: (res: TicketDeviceTypeList[]) => {
          this.deviceTypeList = res;
          this.deviceTypeList.forEach((deviceType: TicketDeviceTypeList) => {
            deviceType.name = deviceType.name === 'Inverter' ? 'Inverters' : deviceType.name === 'Meter' ? 'Meters' : deviceType.name;
            deviceType.siteDevices.forEach((siteDevice: TicketDeviceModelList) => {
              const match = siteDevice.name?.match(/Active Outage - ([A-Z0-9\-]+)/);
              const extractedTicketNumber = match ? match[1] : null;
              if (extractedTicketNumber && extractedTicketNumber === this.currentTicketNumber) {
                if (siteDevice.name && siteDevice.name.includes('Active Outage -')) {
                  siteDevice.name = siteDevice.name.replace(/- [A-Z0-9\-]+/, '- Current Ticket');
                }
              }
              if (this.ticketdetail.ticketDeviceMaps.some(ticketDevice => ticketDevice.siteDeviceId === siteDevice.id)) {
                siteDevice.alertStatus = this.ticketdetail.ticketDeviceMaps.find(
                  ticketDevice => ticketDevice.siteDeviceId === siteDevice.id
                )?.alertStatus;
              }
            });
          });
          const mapDeviceTypeList = this.prepareArrayAsRequiredToMapTreeNode(this.deviceTypeList);
          this.treeNodes = this.mapToTreeNodes(mapDeviceTypeList);
          if (this.deviceItem && this.deviceItem.length > 0) {
            this.setSelectedDevices();
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  prepareArrayAsRequiredToMapTreeNode(array) {
    const otherCategory = {
      id: 3,
      name: 'Others',
      siteDevices: []
    };

    const modifiedArray = array
      .map(item => {
        if (item.name === 'Inverters' || item.name === 'Meters') {
          return item;
        } else {
          otherCategory.siteDevices.push(...item.siteDevices);
          return null;
        }
      })
      .filter(item => item !== null);

    if (otherCategory.siteDevices.length > 0) {
      modifiedArray.push(otherCategory);
    }

    return modifiedArray;
  }

  mapToTreeNodes(deviceTypeList: TicketDeviceTypeList[]): CustomTreeNode[] {
    // Mapping logic to convert deviceTypeList to treeNodes
    const treeNodes: CustomTreeNode[] = [];

    // Iterate over the deviceTypeList and create tree nodes
    for (const deviceType of deviceTypeList) {
      const childNode: CustomTreeNode[] = [];

      for (const deviceObj of deviceType.siteDevices) {
        const childrenData: CustomTreeNode = {
          data: deviceObj,
          label: deviceObj.name,
          expanded: false,
          id: deviceObj.id,
          key: deviceObj.name
        };

        childNode.push(childrenData);
      }

      const treeNode: CustomTreeNode = {
        data: deviceType,
        label: deviceType.name,
        children: childNode,
        expanded: false,
        id: deviceType.id,
        key: deviceType.name
      };

      treeNodes.push(treeNode);
    }

    return treeNodes;
  }

  setSelectedDevices() {
    const selectedNodes = new Set<TreeNode<any>>(); // Create a Set to store unique nodes

    for (const obj of this.treeNodes) {
      let selectedChildCount = 0; // Counter for selected child nodes

      for (const deviceObj of obj.children) {
        for (const addedDevice of this.deviceItem) {
          if (addedDevice.siteDeviceId === (deviceObj as CustomTreeNode).id) {
            selectedNodes.add(deviceObj);
            selectedChildCount++;
            break;
          }
        }
      }

      if (selectedChildCount > 0 && selectedChildCount < obj.children.length) {
        obj.expanded = true;
        obj.partialSelected = true;
      } else if (selectedChildCount > 0 && selectedChildCount === obj.children.length) {
        selectedNodes.add(obj);
        obj.expanded = true;
      }
    }

    this.selectedNodeIds = Array.from(selectedNodes); // Convert Set to an array
  }

  selectAllChanged() {
    this.isSelectAll = !this.isSelectAll;
    this.selectedNodeIds = [];
    if (this.isSelectAll) {
      // Code for selecting all
      this.treeNodes.forEach(node => this.selectNodeWithChildren(node));
    }
  }

  selectNodeWithChildren(node: TreeNode) {
    this.selectedNodeIds.push(node);

    if (node.children) {
      node.children.forEach(childNode => this.selectNodeWithChildren(childNode));
    }
  }

  sitekwacCalculate() {
    let meterArray = [];
    meterArray = this.deviceItem.filter(obj => obj.deviceTypeId === 2);
    let inverterArray = [];
    inverterArray = this.deviceItem.filter(obj => obj.deviceTypeId === 1);
    if (meterArray.length !== 0 && inverterArray.length !== 0) {
      let sum = 0;
      for (const obj of meterArray) {
        this.ticketdetail.affectedkWac = obj.size + sum;
        sum = this.ticketdetail.affectedkWac;
      }
    } else {
      if (meterArray.length !== 0) {
        let sum = 0;
        for (const obj of meterArray) {
          this.ticketdetail.affectedkWac = obj.size + sum;
          sum = this.ticketdetail.affectedkWac;
        }
      } else if (inverterArray.length !== 0) {
        let sum = 0;
        for (const obj of inverterArray) {
          this.ticketdetail.affectedkWac = obj.size + sum;
          sum = this.ticketdetail.affectedkWac;
        }
      } else {
        this.ticketdetail.affectedkWac = null;
      }
    }
    this.ticketdetail.ticketDeviceMaps = this.deviceItem;
  }

  getSelectedFileIds(): number[] {
    const selectedIds: number[] = [];
    if (this.selectedNodeIds && this.selectedNodeIds.length) {
      this.selectedNodeIds.forEach((node: any) => {
        if (!node.children) {
          selectedIds.push(node.id);
        }
      });
    }
    return selectedIds;
  }

  onAddDevice() {
    this.selectedNodeIds = this.getSelectedFileIds();
    if (this.selectedNodeIds && this.selectedNodeIds.length > 0) {
      this.loading = true;
      this.deviceItem = [];
      for (const id of this.selectedNodeIds) {
        for (const obj of this.deviceTypeList) {
          for (const deviceObj of obj.siteDevices) {
            if (deviceObj && deviceObj.id === id) {
              this.deviceItem.push({
                deviceTypeId: deviceObj.deviceTypeId,
                deviceTypeName: deviceObj.deviceTypeName,
                siteDeviceId: deviceObj.id,
                siteDeviceName: deviceObj.model,
                manufacturer: deviceObj.mfg,
                size: deviceObj.size,
                label: deviceObj.label,
                alertStatus: this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.alertStatus ?? deviceObj.alertStatus,
                acNameplateKW:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.acNameplateKW ?? deviceObj.acNameplateKW,
                comments: this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.comments ?? deviceObj.comments,
                deviceId: this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.deviceId ?? deviceObj.deviceId,
                deviceName: this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.deviceName ?? deviceObj.deviceName,
                deviceOutageId:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.deviceOutageId ?? deviceObj.deviceOutageId,
                endDateTimeUTC:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.endDateTimeUTC ?? deviceObj.endDateTimeUTC,
                isADeviceOutage:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.isADeviceOutage ?? deviceObj.isADeviceOutage,
                isPlannedDowntime:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.isPlannedDowntime ?? deviceObj.isPlannedDowntime,
                isSelected: this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.isSelected ?? deviceObj.isSelected,
                name: this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.name ?? deviceObj.name,
                overrideLosesKW:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.overrideLosesKW ?? deviceObj.overrideLosesKW,
                refTicketNumber:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.refTicketNumber ?? deviceObj.refTicketNumber,
                serialNumber:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.serialNumber ?? deviceObj.serialNumber,
                startDateTimeUTC:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.startDateTimeUTC ?? deviceObj.startDateTimeUTC,
                startDateTimeUTCDate:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.startDateTimeUTCDate ??
                  deviceObj?.startDateTimeUTCDate,
                minStartDateTimeUTCDate:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.startDateTimeUTCDate ??
                  deviceObj?.minStartDateTimeUTCDate,
                maxStartDateTimeUTCDate:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.maxStartDateTimeUTCDate ??
                  deviceObj?.maxStartDateTimeUTCDate,
                minEndDateTimeUTCDate:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.minEndDateTimeUTCDate ??
                  deviceObj?.minEndDateTimeUTCDate,
                maxEndDateTimeUTCDate:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.maxEndDateTimeUTCDate ??
                  deviceObj?.maxEndDateTimeUTCDate,
                endDateTimeUTCDate:
                  this.deviceItemsArray.find(item => item.siteDeviceId === deviceObj.id)?.endDateTimeUTCDate ??
                  deviceObj?.endDateTimeUTCDate
              });
            }
          }
        }
      }
      this.ticketdetail.ticketDeviceMaps = this.deviceItem;
      this.sitekwacCalculate();

      // Emit the event with the updated ticket detail
      this.event.emit(this.ticketdetail);

      // Save the ticket detail to storage
      this.storageService.set('deviceDetail', this.ticketdetail);

      this.loading = false;
      this._bsModalRef.hide();
    } else {
      this.alertService.showWarningToast('Please select at least one device.');
      return;
    }
  }
}
