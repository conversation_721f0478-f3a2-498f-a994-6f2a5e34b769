import { Component, EventEmitter, Input, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { ADD_EDIT_COPY_MODE } from '../pm-non-conformance.model';
import { PmNonConformanceService } from '../pm-non-conformance.service';

@Component({
  selector: 'sfl-nc-component-add-edit',
  templateUrl: './nc-component-add-edit.component.html',
  styleUrls: ['./nc-component-add-edit.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class NcComponentAddEditComponent implements OnInit, OnDestroy {
  public onClose: Subject<boolean>;
  @Input() ncComponentItem;
  @Input() ncComponentMode;
  loading = false;
  subscription: Subscription = new Subscription();
  public event: EventEmitter<any> = new EventEmitter();

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly storageService: StorageService,
    private readonly alertService: AlertService,
    private readonly pmNonConformanceService: PmNonConformanceService
  ) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
    if (this.ncComponentMode === ADD_EDIT_COPY_MODE.COPY) {
      this.ncComponentItem = { ...this.ncComponentItem, evId: 0, componentName: 'Copy of ' + this.ncComponentItem.componentName };
    }
  }

  public onConfirm(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  onAddEditComponent() {
    this.loading = true;
    const payload = {
      id: this.ncComponentItem.evId,
      componentName: this.ncComponentItem.componentName
    };
    this.subscription.add(
      this.pmNonConformanceService.addUpdateNcComponent(payload).subscribe({
        next: (res: any) => {
          this.alertService.showSuccessToast(res.message);
          this.loading = false;
          this.event.emit(true);
          this._bsModalRef.hide();
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
