@media only screen and (min-width: 820px) {
  ::ng-deep .nb-theme-dark {
    .p-datatable {
      .p-datatable-tbody > tr > td,
      .p-datatable-thead > tr > th {
        border: 1px solid #2c3753;
      }
    }
  }
  ::ng-deep .p-datatable {
    .p-datatable-tbody > tr > td,
    .p-datatable-thead > tr > th {
      border: 1px solid #dee2e6;
    }
  }
}

::ng-deep .modal-dialog-right {
  position: fixed;
  margin: auto;
  width: 600px;
  right: 0;
  height: 100%;
}

::ng-deep .nb-theme-dark {
  #fixed-table thead tr:nth-child(1) th {
    background: #192038;
  }
}
