import { Injectable } from '@angular/core';
import { QE_MENU_MODULE_ENUM } from '../enums';
import { JUMP_TO_MENU_LIST, QE_MENU_ENUMS } from '../constants';
import { StorageService } from './storage.service';
import { environment } from '../../../environments/environment';
import { ChildMenuDTOS, MenuDTOS } from '../models/dashboard.model';
import { SITE_ADD_EDIT_SCREEN_TABS_ENUM } from '../models/site.model';
import { SharedCPSDataService } from './shared-cps-data.service';

@Injectable({
  providedIn: 'root'
})
export class HeaderMenuService {
  private headerMenuList: MenuDTOS[] = [];
  constructor(private readonly storageService: StorageService, private readonly sharedCPSDataService: SharedCPSDataService) {
    this.headerMenuList = [
      {
        title: 'Site Info',
        id: 'site-info',
        icon: 'assets/images/Sites-Icon-Small.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.SITE_INFO,
        hasPermission:
          this.storageService.get('permission').Equipments ||
          this.storageService.get('permission').Portfolio ||
          this.storageService.get('permission').Customer ||
          this.storageService.get('permission').Site ||
          this.storageService.get('permission').SiteDevice ||
          this.storageService.get('permission').SiteDashboard,
        defaultRoute: this.storageService.get('permission').SiteDashboard ? 'Dashboard' : 'Sites',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'site-dashboard',
            route: '/entities/site-dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_DASHBOARD,
            hasPermission: this.storageService.get('permission').SiteDashboard,
            subMenu: []
          },
          {
            title: 'Customers',
            id: 'site-customers',
            route: '/entities/customers',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_CUSTOMERS,
            hasPermission: this.storageService.get('permission').Customer,
            subMenu: []
          },
          {
            title: 'Portfolios',
            id: 'site-portfolios',
            route: '/entities/portfolios',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_PORTFOLIOS,
            hasPermission: this.storageService.get('permission').Portfolio,
            subMenu: []
          },
          {
            title: 'Sites',
            id: 'sites-listing',
            route: '/entities/sites',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_SITES,
            hasPermission: this.storageService.get('permission').Site,
            subMenu: []
          },
          {
            title: 'Devices',
            id: 'site-devices',
            route: '/entities/site-device',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_DEVICES,
            hasPermission: this.storageService.get('permission').SiteDevice,
            subMenu: []
          },
          {
            title: 'Equipment',
            id: 'site-quipment',
            route: '/entities/equipment',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_EQUIPMENT,
            hasPermission: this.storageService.get('permission').Equipments,
            subMenu: []
          },
          // Custom Menu
          {
            title: 'Site Details',
            id: 'site-info-custom-site-details',
            route: '/entities/sites/view/:siteId',
            queryParams: { openedTab: SITE_ADD_EDIT_SCREEN_TABS_ENUM.SITE_INFO },
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS,
            hasPermission: this.storageService.get('permission').Site,
            subMenu: []
          },
          {
            title: 'Performance Information',
            id: 'site-info-custom-performance-information',
            route: '/entities/sites/view/:siteId',
            queryParams: { openedTab: SITE_ADD_EDIT_SCREEN_TABS_ENUM.PERFORMANCE_INFO },
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION,
            hasPermission: this.storageService.get('permission').Site,
            subMenu: []
          },
          {
            title: 'Device List',
            id: 'site-info-custom-device-list',
            route: '/entities/sites/view/:siteId',
            queryParams: { openedTab: SITE_ADD_EDIT_SCREEN_TABS_ENUM.AUTOMATION_SITE_DEVICE_LIST },
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST,
            hasPermission: this.storageService.get('permission').Site,
            subMenu: []
          }
        ]
      },
      {
        title: 'PM',
        id: 'pm-section',
        icon: 'assets/images/List.svg',
        hasPermission: true,
        qeMenuKey: QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE,
        defaultRoute: 'Dashboard',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'pm-dashboard',
            route: '/entities/dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_DASHBOARD,
            hasPermission: true,
            subMenu: []
          },
          {
            title: 'Scope',
            id: 'pm-scope',
            route: '/entities/assessments',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_SCOPE,
            hasPermission: this.storageService.get('permission').Assessment,
            subMenu: []
          },
          {
            title: 'Work Orders',
            id: 'pm-work-orders',
            route: '/entities/workorders',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
            hasPermission: this.storageService.get('permission').WO,
            subMenu: []
          },
          {
            title: 'Reports',
            id: 'pm-reports',
            route: '/entities/other-reports',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_REPORTS,
            hasPermission: this.storageService.get('permission').Report,
            subMenu: []
          },
          {
            title: 'Site Audit',
            id: 'pm-site-audit',
            route: '/entities/site-audit-report',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_SITE_AUDIT,
            hasPermission: this.storageService.get('permission').SiteAuditReport,
            subMenu: []
          },
          {
            title: 'Non-Conformance',
            id: 'pm-non-conformance',
            route: '/entities/non-conformance',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE,
            hasPermission: this.storageService.get('permission').NonConformance,
            subMenu: []
          }
        ]
      },
      {
        title: 'CM',
        id: 'cm-section',
        icon: 'assets/images/Group.svg',
        hasPermission:
          this.storageService.get('permission').Tickets ||
          this.storageService.get('permission').CMReports ||
          this.storageService.get('permission').AuditDispatchReport ||
          this.storageService.get('permission').Exclusions ||
          this.storageService.get('permission').BillingReport ||
          this.storageService.get('permission').PerformanceReport ||
          this.storageService.get('permission').TruckRollReport ||
          this.storageService.get('permission').MapReport ||
          this.storageService.get('permission').CMDashboard,
        qeMenuKey: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE,
        defaultRoute: 'All Tickets',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'cm-dashboard',
            route: '/entities/cm-dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_DASHBOARD,
            hasPermission: this.storageService.get('permission').CMDashboard,
            subMenu: []
          },
          {
            title: 'All Tickets',
            id: 'cm-all-tickets',
            route: '/entities/ticket',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
            hasPermission: this.storageService.get('permission').Tickets,
            subMenu: []
          },
          {
            title: 'Ticket Audit Report',
            id: 'cm-ticket-audit-report',
            route: '/entities/cm-reports/audit-dispatch',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT,
            hasPermission: this.storageService.get('permission').AuditDispatchReport,
            subMenu: []
          },
          {
            title: 'Exclusion Report',
            id: 'cm-exclusion-report',
            route: '/entities/cm-reports/exclusion',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT,
            hasPermission: this.storageService.get('permission').Exclusions,
            subMenu: []
          },
          {
            title: 'Billing Report',
            id: 'cm-billing-report',
            route: '/entities/cm-reports/billing',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
            hasPermission: this.storageService.get('permission').BillingReport,
            subMenu: []
          },
          {
            title: 'Truck Roll Report',
            id: 'cm-truck-roll-report',
            route: '/entities/cm-reports/truckroll',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT,
            hasPermission: this.storageService.get('permission').TruckRollReport,
            subMenu: []
          },
          {
            title: 'Map Report',
            id: 'cm-map-report',
            route: environment.env !== 'prod' ? '/entities/cm-reports/map' : null,
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_MAP_REPORT,
            hasPermission: this.storageService.get('permission').MapReport,
            subMenu: []
          },
          {
            title: 'RMA Report',
            id: 'cm-rma-report',
            route: '/entities/cm-reports/rma-report',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_RMA_REPORT,
            hasPermission: this.storageService.get('permission').RmaReport,
            subMenu: []
          }
        ]
      },
      {
        title: 'Availability',
        id: 'availability-section',
        icon: 'assets/images/Availability-icon.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.AVAILABILITY,
        hasPermission:
          this.storageService.get('permission').Availability ||
          this.storageService.get('permission').AvailabilityDataTable ||
          this.storageService.get('permission').AvailabilityExclusion ||
          this.storageService.get('permission').AvailabilityReport,
        defaultRoute: 'Data Table',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'availability-dashboard',
            route: '',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AVB_DASHBOARD,
            hasPermission: this.storageService.get('permission').Availability,
            subMenu: []
          },
          {
            title: 'Reports',
            id: 'availability-report',
            route: '/entities/availability/reports',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AVB_REPORTS,
            hasPermission: this.storageService.get('permission').AvailabilityReport,
            subMenu: []
          },
          {
            title: 'Data Table',
            id: 'availability-data-table',
            route: '/entities/availability/data-table',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AVB_DATA_TABLE,
            hasPermission: this.storageService.get('permission').AvailabilityDataTable,
            subMenu: []
          },
          {
            title: 'Exclusions',
            id: 'availability-exclusion',
            route: '/entities/availability/exclusions',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS,
            hasPermission: this.storageService.get('permission').AvailabilityExclusion,
            subMenu: []
          }
        ]
      },
      {
        title: 'Performance',
        id: 'performance-section',
        icon: 'assets/images/bar-chart.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.PERFORMANCE,
        hasPermission:
          this.storageService.get('permission').Performance ||
          this.storageService.get('permission').DataTable ||
          this.storageService.get('permission').PerformanceReports ||
          this.storageService.get('permission').PerformanceDashboard ||
          this.storageService.get('permission').PerformancePowerChart ||
          this.storageService.get('permission').PerformanceOutage,
        defaultRoute: 'Dashboard',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'performance-dashboard',
            route: '/entities/performance/dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_DASHBOARD,
            hasPermission: this.storageService.get('permission').PerformanceDashboard,
            subMenu: []
          },
          {
            title: 'Power Charts',
            id: 'performance-power-charts',
            route: '/entities/performance/power-chart',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_POWER_CHARTS,
            hasPermission: this.storageService.get('permission').PerformancePowerChart,
            subMenu: []
          },
          {
            title: 'Power Cards',
            id: 'performance-power-cards',
            route: '/entities/performance/power-cards',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_POWER_CARDS,
            hasPermission: this.storageService.get('permission').PerformancePowerCards,
            subMenu: []
          },
          {
            title: 'Reports',
            id: 'performance-reports',
            route: '/entities/performance/report',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_REPORTS,
            hasPermission: this.storageService.get('permission').PerformanceReports,
            subMenu: []
          },
          {
            title: 'Data Table',
            id: 'performance-data-table',
            route: '/entities/performance/data-table',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_DATA_TABLE,
            hasPermission: this.storageService.get('permission').DataTable,
            subMenu: []
          },
          {
            title: 'Alerts',
            id: 'performance-alerts',
            route: '/entities/performance/outage',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_ALERTS,
            hasPermission: this.storageService.get('permission').PerformanceOutage,
            subMenu: []
          }
        ]
      },
      {
        title: 'Safety',
        id: 'safety-section',
        icon: 'assets/images/Shield.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.SAFETY,
        hasPermission:
          this.storageService.get('permission').Safety ||
          this.storageService.get('permission').JHA ||
          this.storageService.get('permission').settings ||
          this.storageService.get('permission').SiteCheckIn,
        defaultRoute: 'JHA',
        subMenu: [
          {
            title: 'JHA',
            id: 'safety-jha',
            route: '/entities/safety/jha',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_JHA,
            hasPermission: this.storageService.get('permission').JHA,
            subMenu: []
          },
          {
            title: 'Site Check-In',
            id: 'safety-site-check-in',
            route: '/entities/safety/site-checkin',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN,
            hasPermission: this.storageService.get('permission').SiteCheckIn,
            subMenu: []
          },
          {
            title: 'Site Audit-JHA',
            id: 'safety-site-audit-jha',
            route: '/entities/safety/site-audit-jha',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA,
            hasPermission: this.storageService.get('permission').SiteAuditJHA,
            subMenu: []
          },
          {
            title: 'Settings',
            id: 'settings-section',
            route: '/entities/safety/settings',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_SETTINGS,
            hasPermission: this.storageService.get('permission').Settings,
            subMenu: [
              {
                title: 'General Info',
                id: 'settings-general-info',
                route: '/entities/safety/settings/general-info',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO,
                hasPermission: this.storageService.get('permission').GeneralInfo,
                subMenu: []
              },
              {
                title: 'Work Type',
                id: 'settings-work-type',
                route: '/entities/safety/settings/work-type',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE,
                hasPermission: this.storageService.get('permission').WorkType,
                subMenu: []
              },
              {
                title: 'Work Step',
                id: 'settings-work-step',
                route: '/entities/safety/settings/workstep',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP,
                hasPermission: this.storageService.get('permission').WorkStep,
                subMenu: []
              },
              {
                title: 'Hazard',
                id: 'settings-hazard',
                route: '/entities/safety/settings/hazard',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_HAZARD,
                hasPermission: this.storageService.get('permission').Hazard,
                subMenu: []
              },
              {
                title: 'Barrier',
                id: 'settings-barrier',
                route: '/entities/safety/settings/barrier',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_BARRIER,
                hasPermission: this.storageService.get('permission').Barrier,
                subMenu: []
              },
              {
                title: 'LOTO',
                id: 'settings-loto',
                route: '/entities/safety/settings/loto',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_LOTO,
                hasPermission: this.storageService.get('permission').LOTO,
                subMenu: []
              }
            ]
          }
        ]
      },
      {
        title: 'Operations',
        id: 'operations-section',
        icon: 'assets/images/operation-report.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.OPERATIONS,
        hasPermission:
          this.storageService.get('permission').OperationsReports ||
          this.storageService.get('permission').OperationsRegionMapping ||
          this.storageService.get('permission').OperationsServices ||
          this.storageService.get('permission').CustomForms,
        defaultRoute: 'operations',
        subMenu: [
          {
            title: 'Reports',
            id: 'Operations-report',
            route: '/entities/operations/operations-reports',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_REPORTS,
            hasPermission: this.storageService.get('permission').OperationsReports,
            subMenu: []
          },
          {
            title: 'Region Mapping',
            id: 'region-mapping',
            route: '/entities/operations/region-mapping',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_REGION_MAPPING,
            hasPermission: this.storageService.get('permission').OperationsRegionMapping,
            subMenu: []
          },
          {
            title: 'Services',
            id: 'services',
            route: '/entities/operations/services',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_SERVICES,
            hasPermission: this.storageService.get('permission').OperationsServices,
            subMenu: []
          },
          {
            title: 'Contracts',
            id: 'contracts',
            route: '/entities/operations/contracts',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_CONTRACTS,
            hasPermission: this.storageService.get('permission').contracts,
            subMenu: []
          },
          {
            title: 'Custom Forms',
            id: 'custom-forms',
            route: '/entities/operations/custom-forms',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS,
            hasPermission: this.storageService.get('permission').CustomForms,
            subMenu: []
          }
        ]
      },
      {
        title: 'Admin',
        id: 'admin-section',
        icon: 'assets/images/user.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.ADMIN,
        hasPermission:
          this.storageService.get('permission').User ||
          this.storageService.get('permission').DataSource ||
          this.storageService.get('permission').apiErrorLog ||
          this.storageService.get('permission').reportSchedule ||
          this.storageService.get('permission').CustomerAPIGateway ||
          this.storageService.get('permission').APIGatewayDashboard ||
          this.storageService.get('permission').EmailLog,
        defaultRoute: 'Users',
        subMenu: [
          {
            title: 'Users',
            id: 'admin-users',
            route: '/entities/admin/users',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_USERS,
            hasPermission: this.storageService.get('permission').User,
            subMenu: []
          },
          {
            title: 'Data Source Mapping',
            id: 'admin-data-source-mapping',
            route: '/entities/admin/dataSource',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING,
            hasPermission: this.storageService.get('permission').DataSource,
            subMenu: []
          },
          {
            title: 'API Error Log',
            id: 'admin-api-error-log',
            route: '/entities/admin/api-error-log',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG,
            hasPermission: this.storageService.get('permission').apiErrorLog,
            subMenu: []
          },
          {
            title: 'Report Scheduler',
            id: 'admin-report-scheduler',
            route: '/entities/admin/report-schedule',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER,
            hasPermission: this.storageService.get('permission').reportSchedule,
            subMenu: []
          },
          {
            title: 'Customer API Gateway',
            id: 'admin-customer-api-gateway',
            route: '/entities/admin/customer-api-gateway',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY,
            hasPermission: this.storageService.get('permission').CustomerAPIGateway,
            subMenu: []
          },
          {
            title: 'API Gateway Dashboard',
            id: 'admin-api-gateway-dashboard',
            route: '/entities/admin/api-gateway-dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD,
            hasPermission: this.storageService.get('permission').APIGatewayDashboard,
            subMenu: []
          },
          {
            title: 'Re-Fetch Scheduler',
            id: 're-fetch-scheduler',
            route: '/entities/admin/refetch-schedule',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER,
            hasPermission: this.storageService.get('permission').reFetchSchedule,
            subMenu: []
          },
          {
            title: 'Email Log',
            id: 'email-log',
            route: '/entities/admin/email-log',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_EMAIL_LOG,
            hasPermission: this.storageService.get('permission').EmailLog,
            subMenu: []
          },
          {
            title: 'Analytics',
            id: 'analytics',
            route: '/entities/admin/analytics',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_ANALYTICS,
            hasPermission: this.storageService.get('permission').Analytics,
            subMenu: []
          }
        ]
      }
    ];
  }

  filteredHeaderMenuList(qeMenuEnums: QE_MENU_ENUMS, routeParams: { [key: string]: any } = {}): MenuDTOS[] {
    const menuList = this.headerMenuList;
    const allowedMap = JUMP_TO_MENU_LIST[qeMenuEnums];

    const replaceRouteParams = (route: string | undefined | null): string => {
      if (!route) return '';

      if (!routeParams || Object.keys(routeParams).length === 0) {
        return route;
      }

      return route.replace(/:([a-zA-Z0-9_]+)/g, (_, key) => {
        const value = routeParams[key];
        return value !== undefined && value !== null ? String(value) : `:${key}`;
      });
    };

    const processSection = (
      section: any,
      allowedEntries: (QE_MENU_MODULE_ENUM | Record<QE_MENU_MODULE_ENUM, QE_MENU_MODULE_ENUM[]>)[]
    ): any | null => {
      if (!allowedEntries) return null;

      const allowedKeys: QE_MENU_MODULE_ENUM[] = [];
      const nestedRules: Partial<Record<QE_MENU_MODULE_ENUM, QE_MENU_MODULE_ENUM[]>> = {};

      for (const entry of allowedEntries) {
        switch (typeof entry) {
          case 'number':
            allowedKeys.push(entry as QE_MENU_MODULE_ENUM);
            break;
          case 'object':
            Object.assign(nestedRules, entry);
            break;
          default:
            break;
        }
      }

      const filteredSubMenu =
        section.subMenu
          ?.map(sub => {
            if (allowedKeys.includes(sub.qeMenuKey)) {
              return { ...sub, route: replaceRouteParams(sub.route), subMenu: [] };
            }
            if (nestedRules[sub.qeMenuKey]) {
              return processSection(sub, nestedRules[sub.qeMenuKey]);
            }
            return null;
          })
          .filter(Boolean) ?? [];

      if (filteredSubMenu.length === 0 && !allowedKeys.includes(section.qeMenuKey)) {
        return null;
      }

      return { ...section, route: replaceRouteParams(section.route), subMenu: filteredSubMenu };
    };

    return menuList
      .map(section => {
        const allowedEntries = allowedMap?.[section.qeMenuKey];
        return processSection(section, allowedEntries);
      })
      .filter(Boolean);
  }
}
