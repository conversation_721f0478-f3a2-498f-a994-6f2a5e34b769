import { TemplateOpenModelForFormConstant } from '../operations/custom-forms/custom-forms.model';
export interface WorkOrderSchedule {
  currentScheduleDate: Date;
  fieldTech: number;
  id: number;
  notes: string;
  resasonForReschedule: number;
  rescheduleBy: string;
  rescheduleReason: string;
  scheduleDate: Date;
  userId: number;
  workOrderId: number;
  expanded: boolean;
  IsRescheduled: boolean;
  IsTentavieMonth: boolean;
  FieldTechIds: number[];
}

export class RescheduleForm {
  id: number;
  workOrderId: number;
  workOrderIds: number[];
  fieldTech: string | number;
  scheduleDate: Date;
  resasonForReschedule: string;
  notes: string;
}

export enum Categories {
  'Corrective Maintenance - Outage',
  'Corrective Maintenance - Emergency',
  'Corrective Maintenance - Client Request',
  'Equipment Access Restriction',
  'Lack of Preparation',
  'Site Access Restriction',
  'Site Conditions - Flooding',
  'Site Conditions - Overgrown',
  'Site Conditions - Snow',
  'Tech Availability',
  'Tech Sick Day / PTO',
  'Thermal Event (Fire/Smoke)',
  'Weather - Rain',
  'Weather - Snow',
  'Weather - Clouds / Low Irradiance'
}

export class AvailableFormsResponse {
  public totalQESTForm: number;
  public listOfQESTForm: AvailableFormsList[];
}

// DTO for individual zone object
interface ZoneDTO {
  zoneId: number;
  zoneName: string;
}

// DTO for individual QEST form object
export interface ModuleTorqueFormDTO {
  qestwoMapId: number;
  zoneId: number;
  formName: string;
  formJson: string | null;
  formStatus: number;
  zoneName: string;
  updatedDate: Date | null;
  updatedBy: number;
  updatedByName: string;
  filePath: string;
  formEditNote: string | null;
  listOfQESTFormMappedImages: any | null; // Adjust type if needed for image object
  templateTypeName: string;
  workOrderId: number;
  qestFormId: number;
}

export interface ModuleTorqueFormsByWorkOrder {
  listOfQESTForm: ListOfQESTForm[];
  totalQESTForm: number;
}

// available form listing for workorders
export interface ListOfQESTForm {
  createdBy: string;
  createdById: number;
  customerName: string;
  equipmentName: string;
  formName: string;
  formNote: string;
  formStatus: number;
  formVersion: number;
  isActive: boolean;
  isCoverPage: boolean;
  isDefaultForm: boolean;
  isDuplicate: boolean;
  qestFormId: number;
  templateId: number;
  templateName: string;
  templateTypeName: string;
  updatedDate: string;
  workOrderId: number;
  workOrderNumber: string;
}

// Main DTO for the response object
export interface AvailableModuleTorqueFormsResponse {
  totalMappedQESTForm: number;
  listofZones: ZoneDTO[];
  listOfMappedQESTForm: ModuleTorqueFormDTO[];
}

export class AvailableFormsList {
  qestFormId: 0 = null;
  formName: string = '';
  templateTypeName: string = '';
  formNote: string = '';
  templateName: string = '';
  customerName: string = '';
  equipmentName: string = '';
  isActive: boolean = true;
  updatedDate: string = '';
  createdBy: string = '';
  createdById: 0 = null;
  formStatus: number = 0;
}
export class SelectInverterList {
  public deviceTypeId: number;
  public qestFormId: number;
  public id: number;
  public size: number;
  public deviceTypeName: string;
  public label: string;
  public mfg: string;
  public model: string;
  public name: string;
  public serialNumber: string;
  public isSelected: boolean = false;
}

export class ViewFormsResponse {
  public totalMappedQESTForm: number;
  public listOfMappedQESTForm: ViewFormsList[];
}

export class GetCompletedFormCount {
  public totalQESTFormForWO: number;
  public summaryReportCount: number;
  public ipmCount: number;
  public tpmFormCount: number;
  public moduleTorqueCount: number;
  public zoneIds: any[];
}
export class DownloadAllViewFormModalForms {
  public listOfQESTWOMapId: number[];
  public workOrderId: number;
}

export class DownloadAllViewFormResponse {
  public entryid: number | null;
  public id: number | null;
  public message: string;
  public status: number;
}

export class ViewFormsList {
  public qestwoMapId: number;
  public siteDeviceId: number;
  public deviceTypeId: number;
  public formStatus: number;
  public workOrderId: number;
  public qestFormId: number;
  public formName: string;
  public formJson: string;
  public deviceName: string;
  public updatedDate: string;
  public filePath: string;
  public TemplateTypeName: string;
}

export enum FormStatus {
  NotStarted = 0,
  InProgress = 1,
  Draft = 2,
  Completed = 3
}

export interface SelectedInverters {
  deviceName: string;
  deviceTypeId: number;
  filePath: string;
  formJson: string;
  formName: string;
  formStatus: number;
  qestFormId: number;
  qestwoMapId: number;
  siteDeviceId: number;
  updatedDate: string;
  workOrderId: number;
}

export interface SelectedZones {
  deviceName: string;
  deviceTypeId: number;
  filePath: string;
  formJson: string;
  formName: string;
  formStatus: number;
  qestFormId: number;
  qestwoMapId: number;
  siteDeviceId: number;
  updatedDate: string;
  workOrderId: number;
}
export const WO_STATUSES = {
  NOT_FOUND: { id: 0, name: 'NotFound' },
  REPORT_COMPLETE: { id: 1, name: 'Report Complete' },
  FIELD_WORK_COMPLETE: { id: 2, name: 'Field Work Complete' },
  FIELD_WORK_PARTIALLY_COMPLETE: { id: 3, name: 'Field Work Partially Complete' },
  PENDING: { id: 4, name: 'Pending' },
  DRAFT: { id: 5, name: 'Draft' },
  REPORT_DRAFTED: { id: 6, name: 'Report Drafted' },
  REPORT_STARTED: { id: 7, name: 'Report Started' },
  CANNOT_COMPLETE: { id: 8, name: 'Cannot Complete' },
  PENDING_RESCHEDULE: { id: 9, name: 'Pending Reschedule' }
};

export interface BulkRescheduleDetails {
  WoStatus: number;
  selectedWoIds: number[];
}

export interface BulkRescheduleResponse {
  customerName: string;
  portfolioName: string;
  siteName: string;
  workOrderNumber: string;
}

export type WOTemplateOpenModelForFormConstantValues =
  (typeof WOTemplateOpenModelForFormConstant)[keyof typeof WOTemplateOpenModelForFormConstant];

export const WOTemplateOpenModelForFormConstant = {
  ...TemplateOpenModelForFormConstant,
  AVAILABLE_FORM: 'availableForm',
  SELECT_INVERTER: 'selectInverter',
  AVAILABLE_MT_FORMS: 'availableMTForms',
  SELECT_ZONE: 'selectZone'
} as const;

export interface UploadedFormsData {
  [WOTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]: ViewFormsList[];
  [WOTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL]: ViewFormsList[];
  [WOTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]: ViewFormsList[];
  [WOTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]: ViewFormsList[];
}

export interface CompletedFormMapId {
  [WOTemplateOpenModelForFormConstant.VIEW_FORMS_MODEL]: number[];
  [WOTemplateOpenModelForFormConstant.VIEW_MT_FORMS_MODEL]: number[];
  [WOTemplateOpenModelForFormConstant.VIEW_SUMMARY_REPORTS_MODEL]: number[];
  [WOTemplateOpenModelForFormConstant.VIEW_TPM_FORMS_MODEL]: number[];
}

export class QESTFillFormRedirection {
  qestwoMapId: number;
  id: number;
  workOrderId: number;
  formId: number;
  assementType: string;
  frequncyType: string;
  pageOpenFrom: string;
  isPmReviewForm: boolean;
  constructor(
    qestwoMapId: number = 0,
    id: number = 0,
    workOrderId: number = 0,
    formId: number = 0,
    assementType: string = '',
    frequncyType: string = '',
    pageOpenFrom: string = '',
    isPmReviewForm: boolean = false
  ) {
    this.qestwoMapId = qestwoMapId ?? 0;
    this.id = id ?? 0;
    this.workOrderId = workOrderId ?? 0;
    this.formId = formId ?? 0;
    this.assementType = assementType ?? '';
    this.frequncyType = frequncyType ?? '';
    this.pageOpenFrom = pageOpenFrom ?? '';
    this.isPmReviewForm = isPmReviewForm ?? false;
  }
}

export interface BulkRescheduleResponse {
  customerName: string;
  portfolioName: string;
  siteName: string;
  workOrderNumber: string;
}

export interface ZoneListDataForSelection {
  qestFormId: number;
  siteId: number;
  zoneId: number;
  zoneName: string;
  isShowZone: boolean;
  isSelected: boolean;
}

export const pmWorkOrderPageFilterKeys = [
  'customerIds',
  'portfolioIds',
  'siteIds',
  'FieldTechIds',
  'reportTypeIds',
  'IsRescheduled',
  'IsTentavieMonth'
];
