export interface DataToBeUpdated {
  id?: number;
  region?: string;
  userName?: string;
  siteCount?: number;
  subRegion?: string;
  county?: string;
  regionName?: string;
  userId?: number;
}

export interface AddRegionRequest {
  id: number;
  userId: number;
  regionName: string;
}

export interface AddSubRegionRequest {
  id: number;
  regionId: number;
  subRegionName: string;
}

export interface AddRegionSubRegionResponse {
  status: number;
  message: string;
  id: string;
  entryid: number;
}
