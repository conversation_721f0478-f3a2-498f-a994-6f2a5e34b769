import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BulkTicketCloseReOpenActionComponent } from './bulk-ticket-close-re-open-action.component';

describe('BulkTicketCloseReOpenActionComponent', () => {
  let component: BulkTicketCloseReOpenActionComponent;
  let fixture: ComponentFixture<BulkTicketCloseReOpenActionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BulkTicketCloseReOpenActionComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BulkTicketCloseReOpenActionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
