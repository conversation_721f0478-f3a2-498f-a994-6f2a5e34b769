import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { CustomDashboardAddEditModalComponent } from './custom-dashboard/custom-dashboard-add-edit-modal/custom-dashboard-add-edit-modal.component';
import { OperationsReportsMenu } from './operation-reports.model';
import { OperationsReportsService } from './operations-reports.service';

@Component({
  selector: 'sfl-operations-reports',
  templateUrl: './operations-reports.component.html',
  styleUrls: ['./operations-reports.component.scss']
})
export class OperationsReportsComponent implements OnInit {
  modalRef: BsModalRef;
  operationsReportsMenu: OperationsReportsMenu[];

  constructor(
    private readonly router: Router,
    private readonly modalService: BsModalService,
    public readonly operationsReportsService: OperationsReportsService
  ) {}

  ngOnInit(): void {
    this.operationsReportsMenu = [
      {
        label: 'PM Completion',
        icon: '',
        routerLink: 'pm-completion-report'
      },
      {
        label: 'Reschedule Pivot',
        icon: '',
        routerLink: 'reschedule-pivot-reports'
      }
    ];

    this.getAllCustomDashboards();

    this.operationsReportsService.$refreshDashboardListingOnUpdate.subscribe(updates => {
      if (updates) this.getAllCustomDashboards();
    });
  }

  getAllCustomDashboards() {
    this.operationsReportsService.getCustomDashboards().subscribe({
      next: response => (this.operationsReportsService.dashboardList = response)
    });
  }

  createCustomDashboard() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right'
    };
    this.modalRef = this.modalService.show(CustomDashboardAddEditModalComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe({
      next: result => {
        if (result) {
          this.getAllCustomDashboards();
        }
      }
    });
  }

  onCustomDashboardClick(dashboard) {
    this.operationsReportsService.selectedDashboard = { ...dashboard };
    this.router.navigateByUrl(`/entities/operations/operations-reports/custom-dashboard/${dashboard.id}`);
  }
}
