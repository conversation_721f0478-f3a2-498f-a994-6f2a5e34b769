<nb-card class="siteSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Sites</h6>
        <div class="ms-auto">
          <button
            *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT]"
            nbButton
            [nbContextMenu]="bulkSitesItems"
            [nbContextMenuTag]="bulkSitesMenuTag"
            status="primary"
            size="small"
            class="linear-mode-button ms-2 button-h-100"
            (click)="onBulkSitesMenuOpen()"
          >
            <em class="fa-solid fa-bars"></em>
          </button>
          <input type="file" #uploadSiteFileInput class="d-none" accept=".xlsx" (change)="uploadSiteFile($event.target.files)" />
          <button
            *ngIf="sites?.length"
            class="linear-mode-button ms-2"
            nbButton
            status="primary"
            size="small"
            type="button"
            (click)="exportData()"
            [disabled]="loading"
          >
            <span class="d-none d-lg-inline-block">Export</span>
            <i class="d-inline-block d-lg-none fa fa-file-export"></i>
          </button>
          <button
            class="linear-mode-button ms-2"
            nbButton
            status="primary"
            size="small"
            [routerLink]="['add']"
            type="button"
            [disabled]="loading"
            *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
          >
            <span class="d-none d-lg-inline-block">Add Site</span>
            <i class="d-inline-block d-lg-none fa-solid fa-plus"></i>
          </button>
          <button nbButton status="basic" type="button" (click)="goBack()" size="small" class="float-end ms-2" *ngIf="showBackButton">
            <span class="d-none d-md-inline-block">Back</span>
            <i class="d-inline-block d-md-none fa-solid fa-arrow-left"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 siteFilter appFilter">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (clearParentList)="sites = []"
          (refreshTableHeight)="this.isFilterDisplay = $event"
          [siteSearch]="siteSearch"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive mt-3 table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Site List">
          <thead>
            <tr>
              <th (click)="sort('QeSiteId', sortOptionList['QeSiteId'])" id="siteID">
                <div class="d-flex align-items-center">
                  Site ID
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['QeSiteId'] === 'desc',
                      'fa-arrow-down': sortOptionList['QeSiteId'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'QeSiteId'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('SiteName', sortOptionList['SiteName'])" id="SiteName">
                <div class="d-flex align-items-center">
                  Site Name
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['SiteName'] === 'desc',
                      'fa-arrow-down': sortOptionList['SiteName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'SiteName'
                    }"
                  ></span>
                </div>
              </th>
              <th id="siteName">Customer (Portfolio)</th>
              <th id="state">Address</th>
              <th id="dcSize">Lockbox Code</th>
              <th class="text-end" id="acSize" (click)="sort('ACSize', sortOptionList['ACSize'])">
                <div class="d-flex align-items-center">
                  AC Size
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['ACSize'] === 'desc',
                      'fa-arrow-down': sortOptionList['ACSize'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'ACSize'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="text-end" (click)="sort('DCSize', sortOptionList['DCSize'])">
                <div class="d-flex align-items-center">
                  DC Size
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['DCSize'] === 'desc',
                      'fa-arrow-down': sortOptionList['DCSize'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'DCSize'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="text-end" (click)="sort('INV', sortOptionList['INV'])">
                <div class="d-flex align-items-center">
                  #INV
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['INV'] === 'desc',
                      'fa-arrow-down': sortOptionList['INV'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'INV'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="text-end" (click)="sort('XFMR', sortOptionList['XFMR'])">
                <div class="d-flex align-items-center">
                  #XFMR
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['XFMR'] === 'desc',
                      'fa-arrow-down': sortOptionList['XFMR'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'XFMR'
                    }"
                  ></span>
                </div>
              </th>
              <th class="text-end action" id="action" (click)="sort('SiteDeviceCount', sortOptionList['SiteDeviceCount'])">
                <div class="d-flex align-items-center">
                  #Devices
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['SiteDeviceCount'] === 'desc',
                      'fa-arrow-down': sortOptionList['SiteDeviceCount'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'SiteDeviceCount'
                    }"
                  ></span>
                </div>
              </th>
              <ng-container *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                <th class="text-end action" id="action" (click)="sort('RegionName', sortOptionList['RegionName'])">
                  <div class="d-flex align-items-center">
                    Region
                    <span
                      class="fa cursor-pointer ms-auto"
                      [ngClass]="{
                        'fa-arrow-up': sortOptionList['RegionName'] === 'desc',
                        'fa-arrow-down': sortOptionList['RegionName'] === 'asc',
                        'icon-selected': filterModel.sortBy === 'RegionName'
                      }"
                    ></span>
                  </div>
                </th>
                <th class="text-end action" id="action" (click)="sort('SubRegionName', sortOptionList['SubRegionName'])">
                  <div class="d-flex align-items-center">
                    Subregion
                    <span
                      class="fa cursor-pointer ms-auto"
                      [ngClass]="{
                        'fa-arrow-up': sortOptionList['SubRegionName'] === 'desc',
                        'fa-arrow-down': sortOptionList['SubRegionName'] === 'asc',
                        'icon-selected': filterModel.sortBy === 'SubRegionName'
                      }"
                    ></span>
                  </div>
                </th>
              </ng-container>
              <th scope="col">Active</th>
              <th class="text-center action" id="action">
                {{ filterModel?.isArchive ? 'Archive' : 'Action' }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let site of sites
                  | paginate
                    : {
                        itemsPerPage: filterModel.itemsCount,
                        currentPage: currentPage,
                        totalItems: total
                      };
                let i = index
              "
            >
              <td data-title="Site ID" class="td-custom-width">{{ site?.qeSiteId }}</td>
              <td data-title="Site Name" class="td-custom-width">
                <a [routerLink]="['../view/' + site.id]">
                  {{ site?.siteName }}
                </a>
              </td>
              <td data-title="Customer (Portfolio)" class="text-truncate td-custom-width">{{ site?.customerPortfolio }}</td>
              <td data-title="Address" class="text-start">
                <span *ngIf="!site?.googleMapLink?.match(regex)">
                  {{
                    site?.address +
                      (site?.city ? ', ' + site?.city : '') +
                      ', ' +
                      site?.stateAbb +
                      (site?.zipCode ? ', ' + site?.zipCode : '')
                  }}</span
                >
                <a *ngIf="site?.googleMapLink?.match(regex)" href="{{ site?.googleMapLink }}" target="_blank" class="cursor-pointer">
                  {{
                    site?.address +
                      (site?.city ? ', ' + site?.city : '') +
                      ', ' +
                      site?.stateAbb +
                      (site?.zipCode ? ', ' + site?.zipCode : '')
                  }}</a
                >
              </td>
              <td data-title="Lockbox Code" class="td-custom-width">{{ site?.lockBoxCode }}</td>
              <td data-title="AC Size" class="text-end">{{ site?.acSize | sflRound | sflNumberWithCommas }}</td>
              <td data-title="DC Size" class="text-end">{{ site?.dcSize | sflRound | sflNumberWithCommas }}</td>
              <td data-title="#INV" class="text-end">
                {{ site?.inv }}
              </td>
              <td data-title="#XFMR" class="text-end">
                {{ site?.totalXfmr }}
              </td>
              <td data-title="#Devices" class="text-end">
                <span *appHasPermission="roleType.CUSTOMER"> {{ site?.siteDeviceCount }} </span>
                <a
                  *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                  class="cursor-pointer"
                  [ngClass]="{
                    'not-allowed': site?.siteDeviceCount === 0 || site?.isArchive
                  }"
                  (click)="gotoDevice(site?.customerId, site?.portfolioId, site?.id, site.isArchive)"
                >
                  {{ site?.siteDeviceCount }}
                </a>
              </td>
              <ng-container *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                <td data-title="Region">{{ site?.regionName }}</td>
                <td data-title="Subregion">{{ site?.subRegionName }}</td>
              </ng-container>
              <td data-title="Active" class="text-center"><nb-toggle [(checked)]="site.isActive" disabled></nb-toggle></td>
              <td data-title="Action" class="text-center">
                <div class="d-md-flex justify-content-center" *ngIf="!site.isArchive && isArchivedModal">
                  <a class="px-2 listgrid-icon" (click)="viewSiteDetails(site.id)">
                    <em class="fa fa-eye" nbTooltip="Click to view site details" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])" class="listgrid-icon px-2" [routerLink]="['../edit/' + site.id]">
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                </div>
                <div class="d-flex justify-content-center" *ngIf="site.isArchive">
                  <nb-toggle
                    (checkedChange)="archiveToggleChange($event, site)"
                    [(checked)]="site.isArchive"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    [nbTooltip]="site.isParentArchive ? 'Portfolio is archived' : 'UnArchive'"
                    [disabled]="site.isParentArchive"
                    status="primary"
                    class="me-3"
                  ></nb-toggle>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="14" *ngIf="!sites?.length" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="sites?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()" appendTo="body">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
