import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { StorageService } from '../../../@shared/services/storage.service';
import { SiteDevice } from '../../site-device/site-device.model';
import { SiteDeviceService } from '../../site-device/site-device.service';
import { ROLE_TYPE } from '../../../@shared/enums';

@Component({
  selector: 'sfl-view-device-details',
  templateUrl: './view-device-details.component.html',
  styleUrls: ['./view-device-details.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ViewDeviceDetailsComponent implements OnInit {
  @Input() deviceId: number;
  device: SiteDevice = new SiteDevice();
  subscription: Subscription = new Subscription();
  title: string = 'View Site Device Info';
  loading = false;
  roleType = ROLE_TYPE;

  constructor(
    public _bsModalRef: BsModalRef,
    public readonly siteDeviceService: SiteDeviceService,
    private readonly router: Router,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.getDeviceDetailsById(this.deviceId);
  }

  getDeviceDetailsById(deviceId) {
    this.loading = true;
    this.subscription.add(
      this.siteDeviceService.getSiteDeviceById(deviceId).subscribe({
        next: (res: SiteDevice) => {
          this.loading = false;
          this.device = res;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  editSite() {
    this.router.navigate(['entities/site-device/edit/', this.deviceId]);
    this._bsModalRef.hide();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
