<nb-card class="dataTableSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="d-flex align-items-center">
      <h6>Power Cards</h6>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 performanceFilter">
        <div class="form-control-group mb-3">
          <div class="row align-items-center">
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <label class="label" for="customer">Customer<span class="ms-1 text-danger">*</span></label>
              <ng-select
                id="data-table-customer-drop-down"
                name="customer"
                [items]="customerList"
                (change)="onCustomerSelect(); setCPSForJumpToMenu()"
                (clear)="onCustomerDeSelect()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.customerId"
                notFoundText="No Customer Found"
                placeholder="Select Customer"
                appendTo="body"
                [loading]="isCustomerLoading"
                [disabled]="isCustomerLoading || loading"
              >
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <label class="label" for="portfolio">Portfolio<span class="ms-1 text-danger">*</span></label>
              <ng-select
                id="power-chart-portfolio-customer-drop-down"
                name="portfolio"
                [multiple]="true"
                [items]="portfolioList"
                (change)="onPortfolioChange(); setCPSForJumpToMenu()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.portfolioIds"
                [closeOnSelect]="false"
                notFoundText="No Portfolio Found"
                placeholder="Select Portfolio"
                [loading]="isPortfolioLoading"
                appendTo="body"
                (search)="onFilter($event, 'filteredPortfolioIds')"
                (close)="filteredPortfolioIds = []"
              >
                <ng-template ng-header-tmp *ngIf="portfolioList && portfolioList.length">
                  <button type="button" (click)="selectAllPortfolio()" class="btn btn-sm btn-primary me-2">Select all</button>
                  <button type="button" (click)="unSelectAllPortfolio()" class="btn btn-sm btn-primary ml-2">Unselect all</button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <label class="label" for="site">Site<span class="ms-1 text-danger">*</span></label>
              <ng-select
                id="power-chart-site-drop-down"
                name="Site"
                [multiple]="true"
                [items]="siteList"
                bindLabel="name"
                bindValue="id"
                (change)="setCPSForJumpToMenu()"
                [(ngModel)]="filterModel.siteIds"
                [closeOnSelect]="false"
                notFoundText="No Site Found"
                placeholder="Select Site"
                [loading]="isSiteLoading"
                appendTo="body"
                (search)="onFilter($event, 'filteredSiteIds')"
                (close)="filteredSiteIds = []"
                [virtualScroll]="true"
              >
                <ng-template ng-header-tmp *ngIf="siteList && siteList.length">
                  <button type="button" (click)="selectAllSite()" class="btn btn-sm btn-primary me-2">Select all</button>
                  <button type="button" (click)="unSelectAllSite()" class="btn btn-sm btn-primary ml-2">Unselect all</button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
              <label class="label" for="sort">Sort<span class="ms-1 text-danger">*</span></label>
              <ng-select
                id="sort"
                name="sort"
                [items]="sortByList"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.sortBy"
                (change)="sortPowerCardsList()"
                notFoundText="No Sort Option Found"
                placeholder="Select Sort"
                [clearable]="false"
                appendTo="body"
                [disabled]="loading"
              >
              </ng-select>
            </div>
            <div class="col-6 col-sm-4 col-md-3 col-lg-2 mb-2 mt-2 mt-sm-4">
              <div class="row">
                <div class="col-7 col-xxl-5 pe-1">
                  <button
                    id="data-table-view-data-btn"
                    class="w-100"
                    nbButton
                    status="primary"
                    size="small"
                    type="button"
                    (click)="viewPowerCardsData()"
                    [disabled]="
                      !filterModel.siteIds?.length ||
                      loading ||
                      !filterModel.customerId ||
                      !filterModel.portfolioIds?.length ||
                      isSiteLoading ||
                      !filterModel.sortBy
                    "
                  >
                    View Data
                  </button>
                </div>
                <div class="col-5 col-xxl-4 ps-1">
                  <button
                    class="w-100"
                    id="data-table-clear-btn"
                    nbButton
                    status="primary"
                    size="small"
                    type="button"
                    [disabled]="loading || isCustomerLoading || isPortfolioLoading || isSiteLoading"
                    (click)="ClearFilter()"
                  >
                    clear
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <!-- Combined Cards (Skeleton + Real Data) -->
      <div class="col-12 col-md-6 col-lg-3 mb-4" *ngFor="let powerCard of powerCardsList">
        <!-- Skeleton Card -->
        <div
          *ngIf="powerCard.isLoading"
          class="card shadow-sm h-100"
          [ngClass]="currentTheme === 'dark' ? 'bg-dark-theme text-white' : 'bg-light-theme'"
        >
          <div class="card-body">
            <div class="mb-3">
              <div class="d-flex justify-content-between align-items-center">
                <div class="skeleton-text skeleton-title"></div>
                <div class="skeleton-badge"></div>
              </div>
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <div class="skeleton-text skeleton-badge"></div>
                  <div class="skeleton-text skeleton-badge"></div>
                </div>
                <div class="text-end mb-2">
                  <div class="skeleton-icon"></div>
                </div>
              </div>
            </div>
            <div class="min-h-80">
              <div class="skeleton-dots">
                <span class="skeleton-dot" *ngFor="let dot of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"></span>
              </div>
            </div>
          </div>
        </div>

        <!-- Real Data Card -->
        <div
          *ngIf="!powerCard.isLoading"
          class="card shadow-sm h-100"
          [ngClass]="currentTheme === 'dark' ? 'bg-dark-theme text-white' : 'bg-light-theme'"
        >
          <div class="card-body">
            <div class="mb-3">
              <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title">{{ powerCard.siteName }}</h5>
                <span
                  *ngIf="powerCard.alertCount > 0"
                  class="cursor-pointer badge bg-danger"
                  [ngClass]="powerCard.alertCount === 0 ? 'badge bg-secondary' : 'badge bg-danger'"
                  (click)="showAlertDetails(powerCard, alertDetailsTemplate)"
                  nbTooltip="Alert Details"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                >
                  {{ powerCard.alertCount }} Alerts
                </span>
                <span *ngIf="powerCard.alertCount === 0" class="badge bg-secondary"> {{ powerCard.alertCount }} Alerts </span>
              </div>
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="card-subtitle fw-bold">{{ powerCard.acSize | number : '1.0-0' }} kW</h6>
                  <p class="small mb-0">AC Size</p>
                </div>
                <div class="text-end mb-2">
                  <em
                    class="fa fa-chart-line fs-2 cursor-pointer"
                    [ngClass]="currentTheme === 'dark' ? 'text-white' : 'text-dark'"
                    nbTooltip="View Chart"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="showSiteChart(powerCard, chartTemplate)"
                  ></em>
                </div>
              </div>
            </div>
            <div class="min-h-80">
              <ng-container *ngFor="let device of powerCard.deviceData">
                <span
                  class="status-dot"
                  [ngClass]="{ 'bg-danger': device.isDeviceAlert, 'bg-success': !device.isDeviceAlert }"
                  [nbTooltip]="
                    device.deviceName +
                    ' - ' +
                    (device.isDeviceAlert ? 0 : device.binDataSum) +
                    ' kW' +
                    (device.binDateTime ? ' (' + device.binDateTime + ')' : '')
                  "
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                ></span>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>

<!-- Alert Details Modal Template -->
<ng-template #alertDetailsTemplate>
  <div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
    <div class="modal-header justify-content-between align-items-center">
      <h5 class="mb-0">Alert Details</h5>
      <button type="button" class="close" aria-label="Close" (click)="closeAlertModal()">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>

    <div class="modal-body alert-details-body">
      <div *ngIf="selectedAlertDetails && selectedAlertDetails.length > 0">
        <div class="row">
          <div class="col-md-4" *ngFor="let alert of selectedAlertDetails">
            <div class="alert-item">
              <p>
                {{ alert.deviceName }} - <strong>{{ alert.binData }} kW</strong> ({{ alert.binDateTime }})
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<!-- Chart Modal Template -->
<ng-template #chartTemplate>
  <div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
    <div class="modal-header justify-content-between align-items-center">
      <h5 class="mb-0">Power Chart</h5>
      <button type="button" class="close" aria-label="Close" (click)="closeChartModal()">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>

    <div class="modal-body">
      <div echarts [options]="selectedSiteChart" [theme]="currentTheme" class="demo-chart vh-85" *ngIf="selectedSiteChart"></div>
      <div class="d-flex justify-content-center align-items-center vh-85" *ngIf="!selectedSiteChart">
        <div class="text-center">
          <nb-spinner size="large" status="primary"></nb-spinner>
          <p class="mt-3 text-muted">Loading chart data...</p>
        </div>
      </div>
    </div>
  </div>
</ng-template>
