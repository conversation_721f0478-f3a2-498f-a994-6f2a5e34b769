import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiUrl } from '../../../@shared/constants';
import { AddRegionRequest, AddRegionSubRegionResponse, AddSubRegionRequest } from './region-mapping.model';
@Injectable({
  providedIn: 'root'
})
export class RegionMappingService {
  constructor(private readonly http: HttpClient) {}

  createRegion(data: AddRegionRequest): Observable<AddRegionSubRegionResponse> {
    return this.http.post<AddRegionSubRegionResponse>(ApiUrl.CREATE_REGION, data);
  }

  updateRegion(data: AddRegionRequest): Observable<AddRegionSubRegionResponse> {
    return this.http.put<AddRegionSubRegionResponse>(ApiUrl.UPDATE_REGION, data);
  }

  createSubRegion(data: AddSubRegionRequest): Observable<AddRegionSubRegionResponse> {
    return this.http.post<AddRegionSubRegionResponse>(ApiUrl.CREATE_SUB_REGION, data);
  }

  updateSubRegion(data: AddRegionRequest): Observable<AddRegionSubRegionResponse> {
    return this.http.put<AddRegionSubRegionResponse>(ApiUrl.UPDATE_SUB_REGION, data);
  }

  updateCounty(data: any): Observable<any> {
    return this.http.put<any>(ApiUrl.UPDATE_COUNTY, data);
  }

  getRegionDetailsById(id: number): Observable<any> {
    return this.http.get<any>(`${ApiUrl.GET_REGION_DETAILS_BY_ID}`, {
      params: {
        id
      }
    });
  }

  getSubRegionDetailsById(id: number): Observable<any> {
    return this.http.get<any>(`${ApiUrl.GET_SUB_REGION_DETAILS_BY_ID}`, {
      params: {
        id
      }
    });
  }

  getCountyDetailsById(id: number): Observable<any> {
    return this.http.get<any>(`${ApiUrl.GET_COUNTY_DETAILS_BY_ID}`, {
      params: {
        id
      }
    });
  }

  getRegionsList(data: any): Observable<any> {
    return this.http.post<any>(ApiUrl.GET_REGIONS_LIST, data);
  }

  getSubRegionsList(data: any): Observable<any> {
    return this.http.post<any>(ApiUrl.GET_SUB_REGIONS_LIST, data);
  }

  getCountiesList(data: any): Observable<any> {
    return this.http.post<any>(ApiUrl.GET_COUNTIES_LIST, data);
  }

  deleteRegion(id: number): Observable<any> {
    return this.http.delete<any>(`${ApiUrl.DELETE_REGION}${id}`);
  }

  deleteSubRegion(id: number): Observable<any> {
    return this.http.delete<any>(`${ApiUrl.DELETE_SUB_REGION}${id}`);
  }

  getRegionDropdownList(): Observable<any> {
    return this.http.get<any>(ApiUrl.GET_REGION_DROPDOWN_LIST);
  }

  getSubRegionDropdownList(): Observable<any> {
    return this.http.get<any>(ApiUrl.GET_SUB_REGION_DROPDOWN_LIST);
  }
}
