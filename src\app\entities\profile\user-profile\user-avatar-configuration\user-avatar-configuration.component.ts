import { Component, Input, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ImageCroppedEvent } from 'ngx-image-cropper';
import { Subject } from 'rxjs';
import { appInitialsBgColors } from '../../../../@shared/models/user.model';
import { ReportService } from '../../../report/report.service';

@Component({
  selector: 'sfl-user-avatar-configuration',
  templateUrl: './user-avatar-configuration.component.html',
  styleUrls: ['./user-avatar-configuration.component.scss']
})
export class UserAvatarConfigurationComponent implements OnInit {
  @Input() profileImage: string;
  @Input() profileBackgroundColor: string;
  @Input() initials: string;
  @Input() isImagesChanges: boolean;
  isFileUploaded: boolean = false;
  selectedFile: File;
  initialsBgColors = appInitialsBgColors;
  public onClose: Subject<{ selectedBgColor: string; photoFile: any }>;
  avatarParams = {
    selectedBgColor: '',
    photoFile: null,
    imageSrc: null
  };
  constructor(public _bsModalRef: BsModalRef, private readonly reportService: ReportService) {}

  ngOnInit(): void {
    this.onClose = new Subject();
    if (!this.isImagesChanges && this.profileBackgroundColor) {
      this.updateSelectedColor();
    } else {
      this.initialsBgColors.forEach((color, i) => {
        color.isSelected = i === 0;
      });
    }
  }

  public onConfirm(): void {
    if (this.avatarParams.selectedBgColor) {
      this.avatarParams.photoFile = null;
    } else {
      this.avatarParams.selectedBgColor = '';
      this.getFileForUpload();
    }
    this.onClose.next(this.avatarParams);
    this._bsModalRef.hide();
  }

  getSelectedColor(index: number) {
    this.initialsBgColors.forEach((color, i) => {
      color.isSelected = i === index;
    });
    this.profileBackgroundColor = this.initialsBgColors[index].bgColor;
    this.avatarParams.selectedBgColor = this.initialsBgColors[index].bgColor;
  }

  updateSelectedColor() {
    appInitialsBgColors.forEach(colorObj => {
      colorObj.isSelected = colorObj.bgColor === this.profileBackgroundColor;
    });
  }

  getInitialsColorForBgColor(bgColor: string) {
    const matchedColor = appInitialsBgColors.find(item => item.bgColor === bgColor);
    if (matchedColor) {
      return matchedColor.color;
    }
    return '#fff';
  }

  changeImage() {
    this.isFileUploaded = false;
    this.selectedFile = null;
    this.avatarParams.photoFile = null;
  }

  imageCropped(event: ImageCroppedEvent) {
    this.avatarParams.imageSrc = event.base64;
  }

  getFileForUpload() {
    if (this.selectedFile) {
      const imageBlob = this.reportService.getDataURItoBlob(this.avatarParams.imageSrc);
      const imageName = this.selectedFile.name;
      const imageFile = new File([imageBlob], imageName, { type: this.selectedFile.type });
      this.avatarParams.photoFile = imageFile;
    }
  }

  getUploadedFiles(event) {
    if (event) {
      this.isFileUploaded = true;
      this.avatarParams.photoFile = event;
      this.selectedFile = event.target.files[0];
    }
  }
}
