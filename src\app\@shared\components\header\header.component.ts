import { DOCUMENT } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, Inject, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Data, NavigationEnd, Router } from '@angular/router';
import { NbMediaBreakpointsService, NbMenuService, NbPopoverDirective, NbThemeService } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { MenuItem } from 'primeng/api';
import { Subject, Subscription } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { QEAnalyticsGatheringService } from '../../../entities/qe-analytics/services/qe-analytics-gathering.service';
import { SiteCheckInService } from '../../../entities/safety/site-checkin/site-checkin.service';
import { AppConstants } from '../../constants/app.constant';
import { QE_MENU_MODULE_ENUM } from '../../enums/qe-menu.enum';
import { ChildMenuDTOS, MenuDTOS } from '../../models/dashboard.model';
import { AllNotificationModal, NotificationActionSummary, User } from '../../models/user.model';
import { CommonService } from '../../services/common.service';
import { NotificationService } from '../../services/notification.service';
import { StorageService } from '../../services/storage.service';
import { ChunkUploadProgressComponent } from '../chunk-upload-progress/chunk-upload-progress.component';
import { NotificationDetailsScreenComponent } from '../notification-details-screen/notification-details-screen.component';
import { SiteCheckinOutComponent } from '../site-checkin-out/site-checkin-out.component';
import { checkAuthorisations } from '../../utils';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../../enums';
import { QE_MENU_ENUMS } from '../../constants';
import { JumpToMenuConfig, QEMenuConfigs } from '../../models/header-menu.model';
import { HeaderMenuService } from '../../services';
import { SharedCPSDataService } from '../../services/shared-cps-data.service';

@Component({
  selector: 'qesolar-header',
  styleUrls: ['./header.component.scss'],
  templateUrl: './header.component.html'
})
export class HeaderComponent implements OnInit, OnDestroy {
  private readonly destroy$: Subject<void> = new Subject<void>();
  userPictureOnly = false;
  user: User;
  menu: MenuDTOS[];
  jumpToMenuConfig: JumpToMenuConfig = new JumpToMenuConfig();
  isActiveTab: string;
  isActiveSubTab: string;
  subMenu: ChildMenuDTOS[];
  showSidebar = false;
  themes = [
    {
      value: 'default',
      name: 'Light'
    },
    {
      value: 'dark',
      name: 'Dark'
    },
    {
      value: 'cosmic',
      name: 'Cosmic'
    },
    {
      value: 'corporate',
      name: 'Corporate'
    }
  ];
  currentTheme = 'dark';
  darkThemeActive = true;
  menuItems: MenuItem[] = [];
  modalRef: BsModalRef;
  subscription: Subscription = new Subscription();
  NotificationLoading = false;
  isAnyCheckboxChecked = false;
  @ViewChild('myDropdown') myDropdown: ElementRef;
  dateTimeFormat = AppConstants.dateTimeFormat;
  notificationsList: AllNotificationModal[] = [];
  notificationActions: NotificationActionSummary[] = [];
  selectedUnreadNotifications: AllNotificationModal[] = [];
  total: number;
  pageSize = 10;
  currentPage = 1;
  unreadNotificationCount: number;
  loggedUser;
  profileRandomBgColor = '';
  notificationParams = {
    triggerId: null,
    userId: null,
    showUnreadOnly: false,
    sortBy: 'notificationDate',
    direction: 'desc',
    page: 0,
    itemsCount: 10
  };

  userMenu = [
    { title: 'Profile', link: '/entities/profile', id: 'user-profile' },
    { title: 'Change Password', link: '/entities/profile/change-password', id: 'user-change-password' },
    { title: 'Log out', link: '/auth/login', id: 'user-logout' }
  ];
  environment = environment.env;
  chunkFileUpload = false;
  roleType = ROLE_TYPE;
  authorityRoleString = AUTHORITY_ROLE_STRING;
  checkAuthorisationsFn = checkAuthorisations;
  qeMenuEnums = QE_MENU_ENUMS;

  constructor(
    private readonly menuService: NbMenuService,
    private readonly themeService: NbThemeService,
    private readonly breakpointService: NbMediaBreakpointsService,
    private readonly router: Router,
    private readonly notificationService: NotificationService,
    private readonly route: ActivatedRoute,
    private readonly storageService: StorageService,
    private readonly modalService: BsModalService,
    @Inject(DOCUMENT) private document: Document,
    private readonly commonService: CommonService,
    private readonly siteCheckInService: SiteCheckInService,
    private readonly qeAnalyticsGatheringService: QEAnalyticsGatheringService,
    private readonly cdf: ChangeDetectorRef,
    private readonly headerMenuService: HeaderMenuService,
    private readonly sharedCPSDataService: SharedCPSDataService
  ) {
    this.notificationService.loggedUser$.subscribe(user => {
      this.loggedUser = user;
    });
    this.getUpdatedUnreadCount();
    this.subscription.add(
      this.notificationService.notificationCount$.subscribe({
        next: (data: number) => {
          this.unreadNotificationCount = data;
        },
        error: e => {
          this.NotificationLoading = false;
        }
      })
    );
    this.notificationParams = {
      triggerId: null,
      userId: this.loggedUser.userId,
      showUnreadOnly: true,
      sortBy: 'notificationDate',
      direction: 'desc',
      page: 0,
      itemsCount: 10
    };
    let theme = this.storageService.get('theme');
    theme = theme ? theme : 'dark';
    this.darkThemeActive = theme === 'dark' ? true : false;
    this.changeTheme();
    this.menu = this.headerMenuService.filteredHeaderMenuList(this.qeMenuEnums.QE_HEADER_MENU);
    if (this.router.url === '/entities/profile' || this.router.url === '/entities/profile/change-password') {
      this.isActiveTab = null;
      this.subMenu = [];
    } else {
      const activeTab = this.getActiveTab(this.router.url);
      this.isActiveTab = activeTab.title;
      this.subMenu = activeTab.subMenu;
    }
  }

  ngOnInit() {
    this.currentTheme = this.themeService.currentTheme;
    this.user = this.storageService.get('user');
    const { xl } = this.breakpointService.getBreakpointsMap();
    this.themeService
      .onMediaQueryChange()
      .pipe(
        map(([, currentBreakpoint]) => currentBreakpoint.width < xl),
        takeUntil(this.destroy$)
      )
      .subscribe((isLessThanXl: boolean) => (this.userPictureOnly = isLessThanXl));

    this.themeService
      .onThemeChange()
      .pipe(
        map(({ name }) => name),
        takeUntil(this.destroy$)
      )
      .subscribe(themeName => (this.currentTheme = themeName));
    this.menuService.onItemClick().subscribe(event => {
      this.onItemSelection(event.item.title);
    });
    this.profileRandomBgColor = this.notificationService.getRandomColor();

    this.commonService.isChunkUploadInProgress$.subscribe(uploading => {
      this.chunkFileUpload = uploading;
    });
    this.siteCheckInService.userObjectChangedInStorage$.subscribe(updated => {
      const activeTab = this.getActiveTab(this.router.url);
      this.isActiveTab = activeTab.title;
      this.subMenu = activeTab.subMenu;
      if (updated) {
        this.user = this.storageService.get('user');
      }
    });
    this.setActiveRoute();

    this.subscription.add(
      this.sharedCPSDataService
        .checkDisableJumpToMenu()
        .pipe(takeUntil(this.destroy$))
        .subscribe(disableJumpToMenu => {
          this.jumpToMenuConfig.disable = disableJumpToMenu;
        })
    );
  }

  setActiveRoute(): void {
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        const activeTab = this.getActiveTab(event.url);
        this.isActiveTab = activeTab.title;
        this.subMenu = activeTab.subMenu;
        this.cdf.detectChanges();
      }
      this.getActivatedRouteDataEvent();
    });
  }

  onJumpToMenuClick(): void {
    this.sharedCPSDataService.setClonedSharedCPSDataAndResetSharedCPSData();
  }

  onJumpToMenuPopoverOpen(jumpToMenuPopover: NbPopoverDirective): void {
    if (jumpToMenuPopover && !jumpToMenuPopover.isShown) {
      this.jumpToMenuConfig.search = '';
      const routeParams = { ...this.sharedCPSDataService.getSharedCPSDataForCommonFilter(true, false) };
      const jumpToMenuList = this.headerMenuService.filteredHeaderMenuList(this.jumpToMenuConfig.qeMenuEnum, routeParams);
      this.jumpToMenuConfig.filteredJumpToMenuList = this.jumpToMenuConfig.jumpToMenuList = jumpToMenuList;
    }
  }

  getActivatedRouteDataEvent(): void {
    let currentRoute = this.route;
    while (currentRoute.firstChild) {
      currentRoute = currentRoute.firstChild;
    }
    const currentRouteData = currentRoute.snapshot.data;
    this.setJumpToMenuConfig(currentRouteData);
  }

  setJumpToMenuConfig(currentRouteData: Data): void {
    const qeMenuConfigs = currentRouteData['qeMenuConfigs'] as QEMenuConfigs;
    const isValidConfig = qeMenuConfigs && qeMenuConfigs?.isVisible && qeMenuConfigs?.qeMenuEnum;

    this.jumpToMenuConfig = isValidConfig
      ? new JumpToMenuConfig(
          qeMenuConfigs.qeMenuEnum,
          this.headerMenuService.filteredHeaderMenuList(qeMenuConfigs.qeMenuEnum),
          qeMenuConfigs.isVisible
        )
      : new JumpToMenuConfig();

    if (isValidConfig && this.jumpToMenuConfig.isVisible) {
      this.sharedCPSDataService.changeSharedCPSDataForJumpToMenuDisableStatus(qeMenuConfigs.qeMenuEnum);
    }

    this.cdf.detectChanges();
  }

  onJumptoMenuSearchChanged(): void {
    const searchText = this.jumpToMenuConfig.search.toLowerCase();

    const filterRecursively = <T extends MenuDTOS | ChildMenuDTOS>(menuList: T[] = []): T[] => {
      return menuList
        .map(menu => {
          if (!menu.hasPermission) {
            return null;
          }

          const filteredSubMenu = filterRecursively(menu.subMenu || []);

          if (filteredSubMenu.length > 0 || menu.title.toLowerCase().includes(searchText)) {
            return { ...menu, subMenu: filteredSubMenu };
          }
          return null;
        })
        .filter((item): item is T => item !== null);
    };

    this.jumpToMenuConfig.filteredJumpToMenuList = filterRecursively(this.jumpToMenuConfig.jumpToMenuList);
  }

  onItemSelection(title) {
    if (title === 'Log out') {
      this.darkThemeActive = true;
      this.changeTheme(false);
      this.notificationService.clearCustomerCache().subscribe(res => {
        this.storageService.clearAll();
        window.location.reload();
      });
    }
  }

  changeTheme(changeval = true) {
    const themeName = this.darkThemeActive ? 'dark' : 'default';
    this.storageService.set('theme', themeName);
    this.themeService.changeTheme(themeName);

    let themeLink = this.document.getElementById('app-theme') as HTMLLinkElement;
    if (themeLink) {
      themeLink.href = (this.darkThemeActive ? 'lara-dark-blue' : 'lara-light-blue') + '.css';
    }
  }

  navigateHome() {
    // if require, need to update the routing url based on the logged in user's role and permissions as needed
    this.router.navigate(['/entities/dashboard']);
    this.isActiveTab = 'PM';
    this.subMenu = this.menu[1].subMenu;
    this.defaultFirstRoute(this.menu[1]);
    // this.menuService.navigateHome();
    return false;
  }

  getFirstLetter(name: string) {
    return name.slice(0, 1);
  }

  getActiveTab(route: string) {
    for (const i of this.menu) {
      for (const j of i.subMenu) {
        if (j.route && j.route === '/entities/other-reports' && route.includes('/entities/reports')) {
          return i;
        }
        if (j.route && (j.route === route || route.includes(j.route))) {
          return i;
        }
      }
    }
    return this.menu[0];
  }

  getClass(route: string) {
    const activeURL = this.router.url;
    return activeURL.includes(route) ? 'activeSubTab' : null;
  }

  defaultFirstRoute(obj: MenuDTOS) {
    if (obj.subMenu && obj.subMenu.length) {
      if (obj.defaultRoute) {
        const menu = obj.subMenu.find(x => x.title === obj.defaultRoute);
        if (menu && menu.hasPermission && menu.route) {
          this.router.navigate([menu.route], { relativeTo: this.route });
          return;
        }
      }
      for (const i of obj.subMenu) {
        if (i.hasPermission && i.route) {
          this.router.navigate([i.route], { relativeTo: this.route });
          break;
        }
      }
    }
  }

  openSidebar() {
    this.menuItems = [];
    this.menu.forEach(menu => {
      const menuItem: MenuItem = {};
      if (menu.hasPermission) {
        menuItem.label = menu.title;
        if (menu.subMenu.length) {
          menuItem.items = [];
          menu.subMenu.forEach(subMenu => {
            const subMenuItem: MenuItem = {};
            if (subMenu.hasPermission) {
              subMenuItem.label = subMenu.title;
              if (subMenu.subMenu.length) {
                subMenuItem.items = [];
                subMenu.subMenu.forEach(subToSubMenu => {
                  const subToSubMenuItem: MenuItem = {};
                  if (subToSubMenu.hasPermission) {
                    subToSubMenuItem.label = subToSubMenu.title;
                    subToSubMenuItem.routerLink = subToSubMenu.route;
                    subToSubMenuItem.command = () => {
                      this.isActiveTab = menu.title;
                      this.subMenu = menu.subMenu;
                      this.showSidebar = !this.showSidebar;
                    };
                  }
                  subMenuItem.items.push(subToSubMenuItem);
                });
              } else {
                subMenuItem.routerLink = subMenu.route;
                subMenuItem.command = () => {
                  this.isActiveTab = menu.title;
                  this.subMenu = menu.subMenu;
                  this.showSidebar = !this.showSidebar;
                };
              }
            }
            menuItem.items.push(subMenuItem);
          });
        }
      }
      this.menuItems.push(menuItem);
    });
    this.userMenu.forEach(item => {
      const menuItem: MenuItem = {};
      menuItem.label = item.title;
      menuItem.routerLink = item.link;
      menuItem.command = () => {
        this.showSidebar = !this.showSidebar;
        this.onItemSelection(menuItem.label);
      };
      this.menuItems.push(menuItem);
    });
    this.showSidebar = !this.showSidebar;
  }

  openNotificationPopUp() {
    this.notificationParams.showUnreadOnly = true;
    const getListWithDefaultParam = {
      ...this.notificationParams,
      page: 0,
      itemsCount: 10
    };
    this.getAllNotification(getListWithDefaultParam);
  }

  refreshListOnToggleChange() {
    this.currentPage = 1;
    this.pageSize = 10;
    this.notificationParams.itemsCount = 10;
    const getListWithDefaultParam = {
      ...this.notificationParams,
      page: 0,
      itemsCount: 10
    };
    this.getAllNotification(getListWithDefaultParam);
  }

  getAllNotification(params = this.notificationParams) {
    this.NotificationLoading = true;
    this.subscription.add(
      this.notificationService.getAllNotification(params).subscribe({
        next: res => {
          this.notificationsList = res.notifications.map(items => {
            return {
              ...items,
              isUnread: !items.isRead,
              isMoreDetails: false
            };
          });
          this.isAnyUnreadNotification();
          this.total = res.total;
          this.selectedUnreadNotifications = this.notificationsList.filter(item => item.isUnread);
          this.getUpdatedUnreadCount();
          this.NotificationLoading = false;
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  getUpdatedUnreadCount() {
    this.subscription.add(
      this.notificationService.getUpdatedNotificationCount(this.loggedUser.userId).subscribe({
        next: res => {
          this.unreadNotificationCount = res;
          this.notificationService.notificationCount$.next(res);
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  isAnyUnreadNotification() {
    this.isAnyCheckboxChecked = this.notificationsList.some(item => item.isUnread);
  }

  updateCheckboxStatus(userNotificationId: number, isRead: boolean) {
    this.isAnyUnreadNotification();
    const readUnreadParams = {
      userNotificationId: [userNotificationId],
      isRead: !isRead,
      isReadFromWeb: true
    };
    this.markSelectedAsReadUnRead(readUnreadParams);
  }

  markSelectedAsRead() {
    this.NotificationLoading = true;
    this.selectedUnreadNotifications = this.notificationsList.filter(item => item.isUnread);
    const notificationId = this.selectedUnreadNotifications.map(item => item.userNotificationId);
    const readUnreadParams = {
      userNotificationId: notificationId,
      isRead: true,
      isReadFromWeb: true
    };
    this.markSelectedAsReadUnRead(readUnreadParams, true);
  }

  markSelectedAsReadUnRead(readUnreadParams, isPageReset = false) {
    this.subscription.add(
      this.notificationService.markAsReadUnRead(readUnreadParams).subscribe({
        next: res => {
          this.NotificationLoading = false;
          this.selectedUnreadNotifications = [];
          if (isPageReset) {
            this.notificationParams.page = 0;
            this.currentPage = 1;
          }
          this.getAllNotification();
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  navigateToEntity(notification: AllNotificationModal) {
    if (!notification.isDeleted) {
      const readUnreadParams = {
        userNotificationId: [notification.userNotificationId],
        isRead: true,
        isReadFromWeb: true
      };

      const navigateToWorkOrder = () => {
        const { assessmentType: assementType, frequencyType, entityId: id } = notification;
        this.router.navigate(['/entities/workorders/add'], {
          queryParams: { id, assementType, frequencyType }
        });
      };

      const navigateToTicketDetail = () => {
        this.router.navigateByUrl(`/entities/ticket/detail/view/${notification.ticketNumber}`);
      };

      const navigateToViewSiteCheckInCheckOut = () => {
        this.router.navigateByUrl(`/entities/safety/site-checkin/detail/view/${notification.entityId}`);
      };

      const navigateToAlert = () => {
        const url = `/entities/performance/outage?customerId=${notification.customerId}&siteId=${notification.siteId}&portfolioId=${notification.portfolioId}&date=${notification.notificationDate}`;
        this.router.navigateByUrl(url);
      };

      const markNotificationAsRead = () => {
        this.subscription.add(
          this.notificationService.markAsReadUnRead(readUnreadParams).subscribe({
            next: res => {
              this.NotificationLoading = false;
              this.selectedUnreadNotifications = [];
              this.getAllNotification();
            },
            error: err => {
              this.NotificationLoading = false;
            }
          })
        );
      };

      if (!notification.isRead) {
        markNotificationAsRead();
      }

      if (notification.triggerId === 1 || notification.triggerId === 15) {
        navigateToWorkOrder();
      } else if (notification.triggerId === 16 || notification.triggerId === 17) {
        navigateToViewSiteCheckInCheckOut();
      } else if (notification.triggerId === 18 || notification.triggerId === 20) {
        navigateToAlert();
      } else {
        navigateToTicketDetail();
      }
      this.closeNotificationDropdown();
    }
  }

  expandView() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-dialog notification-dialog'
    };
    this.modalRef = this.modalService.show(NotificationDetailsScreenComponent, ngModalOptions);
  }

  getInitials(name: string | undefined): string {
    if (name) {
      const nameArray: string[] = name.split(' ');
      const firstLetterOfName = nameArray[0]?.charAt(0);
      const secondLetterOfName = nameArray[nameArray.length - 1]?.charAt(0);
      if (nameArray.length > 1) {
        return `${firstLetterOfName || ''}${secondLetterOfName || ''}`.toUpperCase();
      } else {
        return (firstLetterOfName || '').toUpperCase();
      }
    }
    return '';
  }

  onChangeSize() {
    this.currentPage = 0;
    this.notificationParams.page = 0;
    this.notificationParams.itemsCount = Number(this.pageSize);
    this.getAllNotification();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.notificationParams.page = this.currentPage - 1;
    this.getAllNotification();
  }

  closeNotificationDropdown() {
    const dropdownElement = this.myDropdown.nativeElement;
    if (dropdownElement) {
      dropdownElement.classList.remove('show');
    }
  }

  toggleMoreDetails(auditActionId: number, isMoreDetails) {
    this.NotificationLoading = true;
    const clickedNotification = this.notificationsList.find(item => item.auditActionId === auditActionId);
    if (isMoreDetails) {
      this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
      this.notificationActions = [];
      return;
    }
    if (clickedNotification && clickedNotification.auditActionId) {
      this.subscription.add(
        this.notificationService.getMoreDetailsOfNotification(clickedNotification).subscribe({
          next: res => {
            if (res && res.length) {
              const actionSummary = JSON.parse(res);
              for (const j of actionSummary) {
                j.Value = JSON.parse(j.Value);
              }
              this.notificationActions = actionSummary;
              this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
            } else {
              this.notificationActions = [];
              this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
            }
          },
          error: err => {
            this.NotificationLoading = false;
          }
        })
      );
    } else {
      this.notificationActions = [];
      this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
    }
  }

  toggleMoreDetailsIcon(auditActionId, clickedNotification) {
    clickedNotification.isMoreDetails = !clickedNotification.isMoreDetails;
    this.notificationsList.forEach(item => {
      if (item.auditActionId !== auditActionId) {
        item.isMoreDetails = false;
      }
    });
    this.NotificationLoading = false;
  }

  getInitialsColorForBgColor(bgColor: string) {
    return this.notificationService.getInitialsColorForBgColor(bgColor);
  }

  showChunkuploadProgress() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {},
      class: 'modal-lg'
    };
    this.modalRef = this.modalService.show(ChunkUploadProgressComponent, ngModalOptions);
  }

  getLocation(): void {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        position => {
          this.qeAnalyticsGatheringService.captureQEAnalyticsItemEnum(QE_MENU_MODULE_ENUM.SITE_CHECK_IN, QE_MENU_MODULE_ENUM.OTHERS);
          const ngModalOptions: ModalOptions = {
            backdrop: 'static',
            keyboard: false,
            animated: true,
            class: 'modal-md',
            initialState: { position }
          };
          this.modalRef = this.modalService.show(SiteCheckinOutComponent, ngModalOptions);
        },
        error => {
          if (error.code === error.PERMISSION_DENIED) {
            console.error('User has blocked location access.');
          } else if (error.code === error.POSITION_UNAVAILABLE) {
            console.error('Location information is unavailable.');
          } else if (error.code === error.TIMEOUT) {
            console.error('The request to get the user location timed out.');
          } else {
            console.error('An unknown error occurred:', error.message);
          }
          // Notify the user about the denial and guide them to enable permissions
          alert('Location access problem. Please enable location .');
        },
        {
          timeout: 10000, // Timeout after 10 seconds
          enableHighAccuracy: true
        }
      );
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscription.unsubscribe();
  }
}
