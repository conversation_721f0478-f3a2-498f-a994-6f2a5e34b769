import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, Input, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { fabric } from 'fabric';
import jsPDF from 'jspdf';
import * as pdfjsLib from 'pdfjs-dist';
import { Subject, lastValueFrom } from 'rxjs';
import { CustomFormService } from '../custom-form.service';

@Component({
  selector: 'sfl-preview-form-template-modal',
  templateUrl: './preview-form-template-modal.component.html',
  styleUrls: ['./preview-form-template-modal.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PreviewFormTemplateModalComponent implements OnInit {
  public onClose: Subject<any>;
  @ViewChild('pdfContainer') pdfContainer: ElementRef;
  @Input() isForm = false;
  loading = false;
  convertedPDF: SafeResourceUrl | null;
  formOrTemplateDetails: any;
  parsedTemplateJSON: any = {};
  pdfFile: any;
  canvasDetails: any = {};
  fields: any[] = [];
  totalNumberOfFields = 1;

  constructor(
    private readonly http: HttpClient,
    private readonly sanitizer: DomSanitizer,
    private readonly customFormService: CustomFormService
  ) {}

  ngOnInit() {
    if (this.isForm) {
      this.getUploadedFormPDF();
    } else {
      this.getTemplateDetails();
    }
    this.onClose = new Subject();
  }

  getTemplateDetails() {
    this.loading = true;
    this.customFormService.getTemplateDetailsById(this.customFormService.formOrTemplateId).subscribe({
      next: response => {
        this.formOrTemplateDetails = response;
        this.parsedTemplateJSON = JSON.parse(this.formOrTemplateDetails.templateJson);

        this.customFormService.templateType = this.formOrTemplateDetails.templateType;
        this.customFormService.templateName = this.formOrTemplateDetails.templateName;

        this.renderPDF();
      },
      error: () => (this.loading = false)
    });
  }

  async renderPDF() {
    const container = this.pdfContainer.nativeElement,
      pdfData = this.formOrTemplateDetails?.filePath
        ? await lastValueFrom(this.http.get(this.formOrTemplateDetails?.filePath, { responseType: 'arraybuffer' }))
        : '';

    pdfjsLib.GlobalWorkerOptions.workerSrc = '/assets/js/pdf.worker.js';
    pdfjsLib.getDocument(pdfData).promise.then(document => {
      this.pdfFile = document;

      for (let i = 1; i <= this.pdfFile.numPages; i++) {
        this.convertPDFToCanvas(i, container, i === this.pdfFile.numPages);
      }
    });
  }

  getViewportScale(page: any): number {
    const viewport = page.getViewport({ scale: 1 }),
      containerWidth = this.pdfContainer.nativeElement.clientWidth;

    return containerWidth / viewport.width;
  }

  convertPDFToCanvas(pageNumber, container, isLastPage: boolean) {
    this.pdfFile.getPage(pageNumber).then(page => {
      const canvas = document.createElement('canvas'),
        mainDivWidth = document.getElementsByClassName('modal-body')[0]['offsetWidth'],
        mainDivHeight = mainDivWidth * 1.414;

      canvas.setAttribute('id', 'canvas' + pageNumber);
      container.appendChild(canvas);
      const ctx = canvas.getContext('2d');

      var scales = { 1: 3.2, 2: 4 },
        defaultScale = 4,
        scale = scales[window.devicePixelRatio] || defaultScale,
        viewport = page.getViewport({ scale: scale }),
        displayWidth = 0.5;

      canvas.height = viewport.height;
      canvas.width = viewport.width;

      canvas.style.width = `${(viewport.width * displayWidth) / scale}px`;
      canvas.style.height = `${(viewport.height * displayWidth) / scale}px`;
      const renderContext = {
        canvasContext: ctx,
        viewport: viewport
      };

      page.render(renderContext).promise.then(() => {
        const bg = canvas.toDataURL('image/png'),
          fabricCanvas = new fabric.Canvas('canvas' + pageNumber);
        fabricCanvas.setHeight(100 * 1.414);
        fabricCanvas.setWidth(100);

        fabric.Image.fromURL(bg, canvasBackgroundImage => {
          if (Object.keys(this.parsedTemplateJSON)?.length !== 0) {
            for (const key in this.parsedTemplateJSON) {
              if ('canvas' + pageNumber === key) {
                const canvasObjects = this.parsedTemplateJSON[key];
                if (canvasObjects.length) {
                  canvasObjects.forEach(canvasObject => {
                    this.fields.push(canvasObject);
                    this.totalNumberOfFields++;
                  });

                  this.preProcessForObjectsRendering(canvasObjects).then(processedJSON => {
                    fabricCanvas.loadFromJSON(processedJSON, () => {
                      const currentWidth = 100,
                        currentHeight = 100 * 1.414,
                        scaleX = mainDivWidth / currentWidth,
                        scaleY = mainDivHeight / currentHeight;

                      fabricCanvas.setWidth(mainDivWidth);
                      fabricCanvas.setHeight(mainDivHeight);
                      fabricCanvas.getObjects().forEach(object => {
                        object.scaleX *= scaleX;

                        object.scaleY *= scaleY;

                        object.left *= scaleX;
                        object.top *= scaleY;

                        object.setCoords();
                      });
                      fabricCanvas.setBackgroundImage(canvasBackgroundImage, fabricCanvas.renderAll.bind(fabricCanvas), {
                        originX: 'left',
                        originY: 'top',
                        scaleX: fabricCanvas.width / viewport.width,
                        scaleY: fabricCanvas.height / viewport.height
                      });
                    });
                  });
                }
              } else {
                fabricCanvas.setWidth(mainDivWidth);
                fabricCanvas.setHeight(mainDivHeight);
                fabricCanvas.setBackgroundImage(canvasBackgroundImage, fabricCanvas.renderAll.bind(fabricCanvas), {
                  originX: 'left',
                  originY: 'top',
                  scaleX: fabricCanvas.width / viewport.width,
                  scaleY: fabricCanvas.height / viewport.height
                });
              }
            }
          } else {
            fabricCanvas.setWidth(mainDivWidth);
            fabricCanvas.setHeight(mainDivHeight);
            fabricCanvas.setBackgroundImage(canvasBackgroundImage, fabricCanvas.renderAll.bind(fabricCanvas), {
              originX: 'left',
              originY: 'top',
              scaleX: fabricCanvas.width / viewport.width,
              scaleY: fabricCanvas.height / viewport.height
            });
          }
        });
        fabricCanvas.enableRetinaScaling = true;
        fabricCanvas.renderAll();

        Object.assign(this.canvasDetails, {
          ['canvas' + pageNumber]: fabricCanvas
        });

        setTimeout(() => {
          if (isLastPage) {
            this.convertCanvasToPDF();
          }
        }, 200);
      });
    });
  }

  convertCanvasToPDF() {
    const pdfDocument = new jsPDF('p', 'pt', 'a4', true),
      keys = Object.keys(this.canvasDetails);

    let pageNumber = 0,
      sortedKeys = Object.keys(this.canvasDetails).sort(),
      sortedObj = {};

    sortedKeys.forEach(key => {
      sortedObj[key] = this.canvasDetails[key];
    });

    for (const key in sortedObj) {
      if (keys[0] === key) {
        this.loading = true;
      }

      sortedObj[key].discardActiveObject().renderAll();

      if (pageNumber > 0) {
        pdfDocument.addPage();
      }

      const canvas = sortedObj[key].getElement(),
        imageData = canvas.toDataURL('image/png');

      pdfDocument.addImage(
        imageData,
        'PNG',
        0,
        0,
        pdfDocument.internal.pageSize.getWidth(),
        pdfDocument.internal.pageSize.getHeight(),
        '',
        'SLOW'
      );

      pageNumber++;
    }
    const pdfBlob = pdfDocument.output('blob'),
      pdfURL = URL.createObjectURL(pdfBlob);

    this.convertedPDF = this.sanitizer.bypassSecurityTrustResourceUrl(pdfURL + '#toolbar=0&navpanes=0&scrollbar=0&view=FitH');

    setTimeout(() => {
      this.loading = false;
    }, 100);
  }

  async preProcessForObjectsRendering(objects) {
    const promiseArray = objects.flatMap(object => (object.type === 'image' ? this.processImageBeforeRendering(object) : '')),
      results = await Promise.all(promiseArray);

    return { objects };
  }

  processImageBeforeRendering(imageObject) {
    return new Promise((resolve, reject) => {
      fabric.util.loadImage(imageObject.src, (img, error) => {
        if (error) {
          reject(error);
          return;
        }

        const elWidth = img.naturalWidth || img.width,
          elHeight = img.naturalHeight || img.height,
          scaleX = ((imageObject.scaleX || 1) * imageObject.width) / elWidth,
          scaleY = ((imageObject.scaleY || 1) * imageObject.height) / elHeight;

        imageObject.width = elWidth;
        imageObject.height = elHeight;
        imageObject.scaleX = scaleX;
        imageObject.scaleY = scaleY;

        resolve(imageObject.src);
      });
    });
  }

  getUploadedFormPDF() {
    this.loading = true;
    this.customFormService.downloadQESTFrom(this.customFormService.formOrTemplateId).subscribe({
      next: (res: Blob) => {
        const pdfURL = URL.createObjectURL(res);
        this.convertedPDF = this.sanitizer.bypassSecurityTrustResourceUrl(pdfURL + '#toolbar=0&navpanes=0&scrollbar=0&view=FitH');
        this.loading = false;
      },
      error: _e => {
        this.loading = false;
      }
    });
  }

  onModalClose() {
    this.customFormService.modalRef.hide();
    this.onClose.next(true);
  }

  get nativeWindow() {
    return window;
  }
}
