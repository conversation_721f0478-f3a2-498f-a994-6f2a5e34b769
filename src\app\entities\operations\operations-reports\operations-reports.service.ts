import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { ApiUrl } from '../../../@shared/constants';
import {
  DashboardData,
  FilterModel,
  PMCompletionResponse,
  PMTableData,
  PivotApiRequestModal,
  ReschedulePivotResponseModal
} from './operation-reports.model';
@Injectable({
  providedIn: 'root'
})
export class OperationsReportsService {
  selectedDashboard: DashboardData;
  dashboardList: DashboardData[];
  $refreshDashboardListingOnUpdate: Subject<boolean> = new Subject();

  constructor(private readonly http: HttpClient) {}

  viewPMCompletionCharts(obj: FilterModel): Observable<PMCompletionResponse[]> {
    return this.http.post<PMCompletionResponse[]>(ApiUrl.GET_PM_COMPLETION_CHART, obj);
  }

  exportPMCompletionPDF(ids): Observable<Blob> {
    return this.http.post<Blob>(ApiUrl.EXPORT_COMPLETION_PDF, ids, { responseType: 'blob' as 'json' });
  }

  viewReschedulePivotCharts(obj: PivotApiRequestModal): Observable<ReschedulePivotResponseModal> {
    return this.http.post<ReschedulePivotResponseModal>(ApiUrl.GET_PIVOT_CHART, obj);
  }

  exportReschedulePivotPDF(ids): Observable<Blob> {
    return this.http.post<Blob>(ApiUrl.EXPORT_PIVOT_PDF, ids, { responseType: 'blob' as 'json' });
  }

  createCustomDashboard(data): Observable<any> {
    return this.http.post<any>(ApiUrl.CREATE_CUSTOM_DASHBOARD, data);
  }

  updateCustomDashboard(data): Observable<any> {
    return this.http.put<any>(ApiUrl.CREATE_CUSTOM_DASHBOARD, data);
  }

  deleteCustomDashboard(dashboardId: number): Observable<any> {
    return this.http.delete<any>(`${ApiUrl.DELETE_CUSTOM_DASHBOARD}/${dashboardId}`);
  }

  getCustomDashboardById(dashboardId: number): Observable<any> {
    return this.http.get<any>(`${ApiUrl.GET_CUSTOM_DASHBOARD_BY_ID}/${dashboardId}`);
  }

  getCustomDashboards(): Observable<any> {
    return this.http.post<any>(ApiUrl.GET_ALL_CUSTOM_DASHBOARDS, {});
  }

  getPmWorkOrders(filter: FilterModel): Observable<PMTableData> {
    return this.http.post<PMTableData>(ApiUrl.GET_PM_WORKORDERS, filter);
  }
}
