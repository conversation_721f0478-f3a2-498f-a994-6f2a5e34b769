<div class="modal-content" id="activitylogticket" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header modal-fixed-header">
    <div class="d-flex align-items-center w-100">
      <h6>{{ ncComponentMode + ' COMPONENT' }}</h6>
      <div class="ms-auto">
        <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
          <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
        </button>
      </div>
    </div>
  </div>
  <form
    name="ncComponentItemForm"
    #ncComponentItemForm="ngForm"
    aria-labelledby="title"
    autocomplete="off"
    class="issue-form"
    (ngSubmit)="ncComponentItemForm?.form?.valid && onAddEditComponent()"
  >
    <div class="modal-body">
      <div class="form-group">
        <label class="label" for="componentStr">Component <span class="ms-1 text-danger">*</span></label>
        <input
          nbInput
          fullWidth
          name="componentStr"
          id="input-componentStr"
          #componentStr="ngModel"
          [(ngModel)]="ncComponentItem.componentName"
          maxlength="51"
          placeholder="Component"
          required
        />
        <sfl-error-msg [control]="componentStr" [isFormSubmitted]="ncComponentItemForm?.submitted" fieldName="Component"></sfl-error-msg>
      </div>
    </div>
    <div class="modal-footer">
      <button nbButton type="button" status="basic" size="medium" (click)="onCancel()">Cancel</button>
      <button nbButton status="primary" size="medium" type="submit" id="closeSubmit">Save</button>
    </div>
  </form>
</div>
