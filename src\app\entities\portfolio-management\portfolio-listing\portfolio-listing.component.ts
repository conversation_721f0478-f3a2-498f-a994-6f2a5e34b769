import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { PORTFOLIOFILTERLIST, Portfolio, PortfolioFilterData } from '../../../@shared/models/portfolio';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerSiteInfoArchiveComponent } from '../../customer-management/customer-add-edit/customer-site-info-archive/customer-site-info-archive.component';
import { AppliedFilter } from '../../site-device/site-device.model';
import { PortfolioService } from '../portfolio.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';
import { SiteService } from '../../site-management/site.service';
import { NbMenuService } from '@nebular/theme';
import { BULK_SITE_ACTIONS_ENUMS, BulkSiteAction } from '../../../@shared/models/site.model';
import { API_CALL_FROM_SECTION_ENUM } from '../../../@shared/enums/api-call-from-section.enum';

@Component({
  selector: 'qesolar-portfolio-listing',
  templateUrl: './portfolio-listing.component.html',
  styleUrls: ['./portfolio-listing.component.scss']
})
export class PortfolioListingComponent implements OnInit, OnDestroy {
  @ViewChild('uploadSiteUpdateFileInput') uploadSiteUpdateFileInput;
  portfolios: Portfolio[] = [];
  subscription: Subscription = new Subscription();
  modalRef: BsModalRef;
  loading = false;
  currentPage = 1;
  total: number;
  pageSize = AppConstants.rowsPerPage;
  disabled = false;
  viewPage = FILTER_PAGE_NAME.SITE_INFO_PORTFOLIO_LISTING;
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  filterList = PORTFOLIOFILTERLIST;
  viewFilterSection = 'portfolioFilterSection';
  sortOptionList = {
    CustomerName: 'asc',
    PortfolioName: 'asc'
  };
  filterDetails: FilterDetails = new FilterDetails();
  isArchivedModal = true;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;
  bulkSitesItems: BulkSiteAction[] = [new BulkSiteAction(BULK_SITE_ACTIONS_ENUMS.UPLOAD_SITES_UPDATES)];
  bulkSitesMenuTag = 'bulkSitesMenuForPortfolio';

  constructor(
    private readonly portfolioService: PortfolioService,
    private readonly commonService: CommonService,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService,
    private readonly router: Router,
    private readonly storageService: StorageService,
    private readonly nbMenuService: NbMenuService,
    private readonly siteService: SiteService
  ) {}

  ngOnInit() {
    this.subscription.add(
      this.nbMenuService.onItemClick().subscribe(event => {
        if (event.tag === this.bulkSitesMenuTag) {
          this.onBulkSiteSelection(event.item as BulkSiteAction);
        }
      })
    );
    this.initFilterDetails();
    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = 'PortfolioName';
    const filter = this.storageService.get(this.viewPage);
    const filterSection = this.storageService.get(this.viewFilterSection),
      sharedFilter = this.storageService.get(AppConstants.SHARED_FILTER_KEY);

    this.isFilterDisplay = filterSection;
    if (filter) {
      this.filterModel = filter;
      if (this.filterModel.direction && this.filterModel.sortBy) {
        this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
      }
      if (this.filterModel.page) {
        this.currentPage = this.filterModel.page + 1;
      }
      if (this.filterModel.itemsCount) {
        this.pageSize = this.filterModel.itemsCount;
      }
    }

    this.filterModel.customerIds = sharedFilter?.customerIds.length ? sharedFilter.customerIds : this.filterModel.customerIds || [];
    this.filterModel.portfolioIds = [];
    this.filterModel.siteIds = [];
    const pageFilterKeys = ['customerIds', 'searchValue', 'isActive', 'isArchive'];
    if (this.storageService.shouldCallListApi(filter, {}, {}, sharedFilter, pageFilterKeys)) {
      this.getAllPortfolioList();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.SEARCH_BOX.show = true;
    filterItem.SHOW_STATUS.show = true;
    filterItem.SHOW_ARCHIVED.show = true;
    this.filterDetails.default_sort = 'PortfolioName';
    this.filterDetails.filter_item = filterItem;
  }

  // sorting
  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllPortfolioList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllPortfolioList();
  }

  gotoCustomer(id) {
    this.router.navigate(['entities/customers/detail/' + id]);
  }

  getAllPortfolioList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.portfolioService.getAllPortfoliosByfilter(this.filterModel).subscribe({
        next: (data: PortfolioFilterData) => {
          this.allportfolioList(data);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  allportfolioList(data: PortfolioFilterData) {
    this.portfolios = data.portfolios;
    this.total = data.totalPortfolio;
    this.loading = false;
  }

  // Go to site
  gotoSite(customerId, portfolioId, noOfSites, isArchive) {
    if (noOfSites > 0 && !isArchive) {
      this.router.navigate(['/entities/sites/'], { queryParams: { customerId, portfolioId, isArchive, isSiteCount: true } });
    }
  }

  // Pagesize Change
  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllPortfolioList();
  }

  exportData() {
    this.loading = true;
    const filterModel = JSON.parse(JSON.stringify(this.filterModel));
    filterModel.itemsCount = this.total;
    filterModel.page = 0;
    filterModel.isExport = true;
    this.subscription.add(
      this.portfolioService.getAllPortfoliosByfilter(filterModel).subscribe({
        next: (data: PortfolioFilterData) => {
          const tittle = 'Portfolios';
          const rows: any = [
            ['Customer', 'Portfolio', 'Number of Sites', 'kWdc', 'Active/InActive', 'Portfolio Lead', 'Analyst', 'Manager']
          ];
          for (const i of data.portfolios) {
            const tempData = [
              i.customerName,
              i.name,
              i.noOfSite ? i.noOfSite : 0,
              i.sumDcSize,
              i.isActive ? 'Active' : 'InActive',
              i.portfolioManagerName ? i.portfolioManagerName : '-',
              i.analystUserName ? i.analystUserName : '-',
              i.commercialAssetsManagerName ? i.commercialAssetsManagerName : '-'
            ];
            rows.push(tempData);
          }
          this.commonService.exportExcel(rows, tittle);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onBulkSitesMenuOpen(): void {
    const exportSiteMenuItem = new BulkSiteAction(BULK_SITE_ACTIONS_ENUMS.EXPORT_SITES);
    const uploadSiteUpdatesMenuItem = new BulkSiteAction(BULK_SITE_ACTIONS_ENUMS.UPLOAD_SITES_UPDATES);
    const menuItems = [uploadSiteUpdatesMenuItem];

    if (
      (this.filterModel.customerIds && this.filterModel.customerIds.length > 0) ||
      (this.filterModel.search && this.filterModel.search.length > 0)
    ) {
      menuItems.push(exportSiteMenuItem);
    }
    this.bulkSitesItems = menuItems;
  }

  onBulkSiteSelection(item: BulkSiteAction): void {
    if (item.id === BULK_SITE_ACTIONS_ENUMS.EXPORT_SITES) {
      this.exportSitesTemplate();
    } else if (item.id === BULK_SITE_ACTIONS_ENUMS.UPLOAD_SITES_UPDATES) {
      this.uploadSitesUpdate();
    }
  }

  exportSitesTemplate(): void {
    this.loading = true;
    this.siteService.exportSitesTemplate({ ...this.filterModel, isCallFrom: API_CALL_FROM_SECTION_ENUM.PORTFOLIOS }).subscribe({
      next: (res: string) => {
        const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
        link.download = `Site_Template.xlsx`;
        link.click();
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  uploadSitesUpdate(): void {
    this.uploadSiteUpdateFileInput.nativeElement.click();
  }

  uploadSiteUpdateFile(files: File[]): void {
    if (files.length > 0) {
      this.loading = true;
      const formData: FormData = new FormData();
      formData.append('customerIds', this.filterModel?.customerIds?.toString());
      formData.append('search', this.filterModel?.search?.toString());
      formData.append('uploadedFile', files[0] as File);
      formData.append('isCallFrom', API_CALL_FROM_SECTION_ENUM.PORTFOLIOS.toString());
      this.subscription.add(
        this.siteService.uploadSiteUpdateFile(formData).subscribe({
          next: (res: { message: string; file: string }) => {
            if (res.file) {
              const fileBlob = this.commonService.createObjectFromBased64(res.file, 'application/vnd.ms-excel');
              const link = this.commonService.createObject(fileBlob, 'application/vnd.ms-excel');
              link.download = `Site_Update_Error.xlsx`;
              link.click();
              this.alertService.showErrorToast(res.message);
            } else {
              this.alertService.showSuccessToast(res.message);
            }
            this.loading = false;
            this.uploadSiteUpdateFileInput && this.uploadSiteUpdateFileInput?.nativeElement
              ? (this.uploadSiteUpdateFileInput.nativeElement.value = '')
              : null;
            this.getAllPortfolioList();
          },
          error: () => {
            this.loading = false;
            this.uploadSiteUpdateFileInput && this.uploadSiteUpdateFileInput?.nativeElement
              ? (this.uploadSiteUpdateFileInput.nativeElement.value = '')
              : null;
          }
        })
      );
    }
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllPortfolioList(true, filterParams);
  }

  archiveToggleChange(event, portfolio) {
    this.isArchivedModal = false;
    if (event === false) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-full-dialog',
        initialState: {
          portfolioId: portfolio.id,
          archiveModalFrom: 'portfolio',
          isFromListOrView: true
        }
      };
      this.modalRef = this.modalService.show(CustomerSiteInfoArchiveComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.getAllPortfolioList();
        } else {
          const index = this.portfolios.findIndex(item => item.id === portfolio.id);
          this.portfolios[index].isArchive = true;
        }
        this.isArchivedModal = true;
      });
    }
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
    this.subscription.unsubscribe();
  }
}
