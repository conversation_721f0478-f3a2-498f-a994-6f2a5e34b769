import { DatePipe } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewEncapsulation } from '@angular/core';
import { NbThemeService } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { concatMap, finalize, from, Subscription } from 'rxjs';
import { AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { AutomationSitePortfolio } from '../../availability/reports/report.model';
import { CustomerService } from '../../customer-management/customer.service';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { SiteService } from '../../site-management/site.service';
import { PowerChartService } from '../power-chart/power-chart.service';
import { AlertDetails, PowerCardItem, PowerCardsFilter, PowerCardsResponse } from './power-cards-model';
import { PowerCardsService } from './power-cards.service';

@Component({
  selector: 'sfl-power-cards',
  templateUrl: './power-cards.component.html',
  styleUrls: ['./power-cards.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PowerCardsComponent implements OnInit {
  loading = false;
  subscription: Subscription = new Subscription();
  customerList: Dropdown[];
  portfolioList: Dropdown[];
  siteList: Dropdown[];
  sortByList: Dropdown[] = [
    { id: 1, name: 'Name' },
    { id: 2, name: 'Size' },
    { id: 3, name: 'Alerts' }
  ];
  filterModel: PowerCardsFilter = new PowerCardsFilter();
  isCustomerLoading = true;
  isSiteLoading = false;
  isPortfolioLoading = false;

  modalRef: BsModalRef;
  currentTheme = 'dark';
  filteredPortfolioIds: number[] = [];
  filteredSiteIds: number[] = [];
  selectedAlertDetails: AlertDetails[] = [];
  viewPage = 'performancePowerCardPage';
  selectedSiteChart: any = null;
  isFullView = false;
  powerCardsList: PowerCardItem[] = []; // Combined array of skeleton and real power cards

  constructor(
    private readonly portfolioService: PortfolioService,
    private readonly customerService: CustomerService,
    private readonly siteService: SiteService,
    private readonly powerCardsService: PowerCardsService,
    private readonly modalService: BsModalService,
    private readonly datePipe: DatePipe,
    private readonly themeService: NbThemeService,
    private readonly powerChartService: PowerChartService,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    let filter = this.storageService.get(this.viewPage);
    this.filterModel = filter ? filter : this.filterModel;
    const localFilterData = this.storageService.get('userDefaultFilter'),
      shared = this.storageService.get(AppConstants.SHARED_FILTER_KEY),
      defaultFilterData = this.storageService.get('user').userFilterSelection;
    const activeFilterData = shared || localFilterData || defaultFilterData;
    this.filterModel.portfolioIds = activeFilterData.portfolioIds || [];
    this.filterModel.siteIds = activeFilterData.siteIds || [];
    this.themeService.onThemeChange().subscribe(themeName => {
      this.currentTheme = themeName.name;
    });
    this.getAllCustomer();
    this.getAllPortfolioByCustomer(activeFilterData.siteIds || []);
    this.getAllSiteByPortfolio(true, activeFilterData.siteIds || []);
  }

  trackByFunction(index: number, element: any) {
    return element ? index : null;
  }

  getAllCustomer() {
    this.isCustomerLoading = true;
    this.subscription.add(
      this.customerService.getAllCustomer().subscribe({
        next: (res: Dropdown[]) => {
          this.customerList = res.filter(item => item.isActive);
          this.loading = false;
          this.isCustomerLoading = false;
        },
        error: e => {
          this.loading = false;
          this.isCustomerLoading = false;
        }
      })
    );
  }

  onCustomerSelect() {
    this.onCustomerDeSelect();
    if (this.filterModel.customerId) {
      this.getAllPortfolioByCustomer();
    }
  }

  ClearFilter() {
    this.siteList = [];
    this.portfolioList = [];
    this.filterModel = new PowerCardsFilter();
    this.powerCardsList = [];
    this.getAllSiteByPortfolio();
  }

  onCustomerDeSelect() {
    this.portfolioList = [];
    this.siteList = [];
    this.filterModel.portfolioIds = null;
    this.filterModel.siteIds = [];
  }

  getAllPortfolioByCustomer(siteIds: number[] = []) {
    this.isPortfolioLoading = true;
    if (this.filterModel.customerId) {
      const data: AutomationSitePortfolio = new AutomationSitePortfolio();
      data.customerIds.push(this.filterModel.customerId);
      data.isAvailabilityCheck = false;
      this.siteService.automationSiteList = [];
      this.subscription.add(
        this.portfolioService.getAllAutomationPortfoliosByCustomerId(data).subscribe({
          next: (res: Dropdown[]) => {
            this.portfolioList = res;
            this.loading = false;
            this.isPortfolioLoading = false;
            this.getAllSiteByPortfolio(true, siteIds);
          },
          error: _e => {
            this.loading = false;
            this.isPortfolioLoading = false;
          }
        })
      );
    } else {
      const data: AutomationSitePortfolio = new AutomationSitePortfolio();
      data.isAvailabilityCheck = false;
      data.portfolioIds = [];
      this.subscription.add(
        this.portfolioService.getAllAutomationPortfoliosByCustomerId(data).subscribe({
          next: (res: Dropdown[]) => {
            this.portfolioList = res.filter(item => item.isActive);
            this.loading = false;
            this.isPortfolioLoading = false;
            this.getAllSiteByPortfolio(true, siteIds);
          },
          error: _e => {
            this.loading = false;
            this.isPortfolioLoading = false;
          }
        })
      );
    }
  }

  onPortfolioChange() {
    this.siteList = [];
    this.filterModel.siteIds = [];
    setTimeout(() => {
      if (this.filterModel.portfolioIds.length) {
        this.getAllSiteByPortfolio();
      } else {
        this.filterModel.siteIds = [];
        this.isSiteLoading = false;
        this.getAllSiteByPortfolio();
      }
    }, 500);
  }

  getAllSiteByPortfolio(onLoad = false, siteIdsList = []) {
    this.isSiteLoading = true;
    const data: AutomationSitePortfolio = new AutomationSitePortfolio();
    data.isAvailabilityCheck = false;
    data.portfolioIds = this.filterModel.portfolioIds || [];
    this.subscription.add(
      this.siteService.getAllAutomationSiteById(data, true, true).subscribe({
        next: (res: Dropdown[]) => {
          this.siteList = res;

          if (onLoad) {
            this.filterModel.siteIds = this.siteList.filter(obj => siteIdsList.includes(obj.id)).map(element => element.id);
          } else if (this.filterModel.portfolioIds && this.filterModel.portfolioIds.length > 0) {
            this.filterModel.siteIds = this.siteList.map(element => element.id);
          } else {
            this.filterModel.siteIds = [];
          }
          this.isSiteLoading = false;
        },
        error: _e => {
          this.isSiteLoading = false;
        }
      })
    );
  }

  onFilter(event: any, forField: string) {
    if (event.term) {
      this[forField] = event.items?.map(element => element.id);
    } else {
      this[forField] = [];
    }
  }

  selectAllPortfolio() {
    if (this.filteredPortfolioIds.length) {
      this.filterModel.portfolioIds = [
        ...new Set([...this.filterModel.portfolioIds, ...JSON.parse(JSON.stringify(this.filteredPortfolioIds))])
      ];
    } else {
      this.filterModel.portfolioIds = [];
      for (const i of this.portfolioList) {
        this.filterModel.portfolioIds.push(i.id);
      }
    }
    this.getAllSiteByPortfolio();
  }

  unSelectAllPortfolio() {
    if (this.filteredPortfolioIds.length) {
      this.filterModel.portfolioIds = this.filterModel.portfolioIds.filter(x => !this.filteredPortfolioIds.includes(x));
    } else {
      this.filterModel.portfolioIds = [];
      this.filterModel.siteIds = [];
    }
    this.isSiteLoading = true;

    if (this.filterModel.portfolioIds.length) {
      this.isSiteLoading = false;
    } else {
      this.siteList = [];
      this.filterModel.siteIds = [];
      this.isSiteLoading = false;
    }
    this.getAllSiteByPortfolio();
  }

  selectAllSite(allSiteToggle = false) {
    if (allSiteToggle || !this.filteredSiteIds.length) {
      this.filterModel.siteIds = [];
      for (const i of this.siteList) {
        this.filterModel.siteIds.push(i.id);
      }
    } else {
      this.filterModel.siteIds = [...new Set([...this.filterModel.siteIds, ...JSON.parse(JSON.stringify(this.filteredSiteIds))])];
    }
  }

  unSelectAllSite() {
    if (this.filteredSiteIds.length) {
      this.filterModel.siteIds = this.filterModel.siteIds.filter(x => !this.filteredSiteIds.includes(x));
    } else {
      this.filterModel.siteIds = [];
    }
  }

  /**
   * Handle sort dropdown change - sort existing data client-side
   */
  onSortChange() {
    if (this.powerCardsList.length > 0 && this.filterModel.sortBy) {
      this.sortPowerCardsList();
    }
  }

  /**
   * Fetch power cards data based on the selected filters
   */
  viewPowerCardsData() {
    if (!this.filterModel.siteIds?.length) {
      return;
    }
    this.loading = true;
    this.powerCardsList = [];

    // Limit to maximum 150 sites
    const maxSites = 150;
    const limitedSiteIds = this.filterModel.siteIds.slice(0, maxSites);

    // Show alert if user selected more than 150 sites
    if (this.filterModel.siteIds.length > maxSites) {
      this.alertService.showWarningToast(`Only showing first ${maxSites} sites out of ${this.filterModel.siteIds.length} selected sites.`);
    }

    // Create initial combined array with skeleton cards for each selected site
    this.powerCardsList = limitedSiteIds.map(siteId => {
      const site = this.siteList.find(s => s.id === siteId);
      return {
        siteId: siteId,
        siteName: site?.name || 'Loading...',
        isLoading: true,
        acSize: 0,
        dcSize: 0,
        alertCount: 0,
        deviceData: [],
        alertDetails: []
      } as PowerCardItem;
    });

    const singleSiteFilters = limitedSiteIds.map(siteId => ({
      siteIds: [siteId],
      startDate: this.datePipe.transform(new Date(), AppConstants.fullDateFormat),
      endDate: this.datePipe.transform(new Date(), AppConstants.fullDateFormat)
    }));

    this.subscription.add(
      from(singleSiteFilters)
        .pipe(
          concatMap(filter =>
            this.powerCardsService.viewPowerCardsData(filter).pipe(
              // Pass the siteId along with the response for identification
              concatMap(res => [{ response: res, siteId: filter.siteIds[0] }])
            )
          ),
          finalize(() => {
            // Remove any remaining skeleton cards that didn't get data
            this.powerCardsList = this.powerCardsList.filter(card => !card.isLoading);
            this.loading = false;
            this.sortPowerCardsList();
          })
        )
        .subscribe({
          next: (data: { response: PowerCardsResponse[]; siteId: number }) => {
            const { response: res, siteId } = data;

            if (res && res.length > 0) {
              const siteData = res[0];
              const alertDevices = siteData.deviceData?.filter(device => device.isDeviceAlert) || [];
              const alertCount = alertDevices.length;
              const alertDetails = alertDevices.map(device => ({
                deviceName: device.deviceName,
                binData: device.binData,
                binDateTime: device.binDateTime
              }));

              // Replace skeleton card with real data in powerCardsList array
              const cardIndex = this.powerCardsList.findIndex(card => card.siteId === siteData.siteId);
              if (cardIndex !== -1) {
                this.powerCardsList[cardIndex] = {
                  ...siteData,
                  alertCount,
                  alertDetails,
                  isLoading: false
                } as PowerCardItem;
              }
            } else {
              // Handle empty response - remove skeleton card for sites with no data
              const cardIndex = this.powerCardsList.findIndex(card => card.siteId === siteId && card.isLoading);
              if (cardIndex !== -1) {
                this.powerCardsList.splice(cardIndex, 1);
              }
            }

            if (this.filterModel.sortBy) {
              this.sortPowerCardsList();
            }
            this.storageService.set(this.viewPage, this.filterModel);
            this.storageService.updateSharedFilters(this.filterModel);
          },
          error: _e => {
            this.loading = false;
          }
        })
    );
  }

  /**
   * Sort all cards (both loading and loaded) based on the selected sort option
   */
  sortPowerCardsList() {
    if (!this.powerCardsList.length || !this.filterModel.sortBy) {
      return;
    }

    switch (this.filterModel.sortBy) {
      case 1:
        this.powerCardsList.sort((a, b) => {
          return (a.siteName || '').localeCompare(b.siteName || '');
        });
        break;
      case 2:
        this.powerCardsList.sort((a, b) => {
          if (a.isLoading && !b.isLoading) return 1;
          if (!a.isLoading && b.isLoading) return -1;
          if (a.isLoading && b.isLoading) return 0;
          return (b.acSize || 0) - (a.acSize || 0);
        });
        break;
      case 3:
        this.powerCardsList.sort((a, b) => {
          if (a.isLoading && !b.isLoading) return 1;
          if (!a.isLoading && b.isLoading) return -1;
          if (a.isLoading && b.isLoading) return 0;
          return (b.alertCount || 0) - (a.alertCount || 0);
        });
        break;
      default:
        break;
    }
  }

  showAlertDetails(powerCard: PowerCardItem, template: TemplateRef<any>) {
    if (!powerCard || powerCard.alertCount === 0) {
      return;
    }
    this.selectedAlertDetails = powerCard.alertDetails;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg'
    };
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  closeAlertModal() {
    this.modalRef?.hide();
    this.selectedAlertDetails = [];
  }

  showSiteChart(site: PowerCardItem, template: TemplateRef<any>) {
    if (!site || !site.siteId) {
      return;
    }
    this.selectedSiteChart = null;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg'
    };
    this.modalRef = this.modalService.show(template, ngModalOptions);

    const powerChartFilterModel = {
      portfolioIds: this.filterModel.portfolioIds,
      siteIds: [site.siteId],
      duration: 'Specific Date',
      startDate: this.datePipe.transform(new Date(), AppConstants.fullDateFormat),
      endDate: this.datePipe.transform(new Date(), AppConstants.fullDateFormat)
    };

    this.subscription.add(
      this.powerCardsService.getSiteSunriseSunsetChartsInfo(powerChartFilterModel).subscribe({
        next: sunriseSunsetData => {
          const powerChartSiteSunriseSunsetData = this.powerChartService.setPowerChartSiteSunriseSunsetData(
            powerChartFilterModel,
            sunriseSunsetData
          );

          this.powerCardsService.getChartsData(powerChartFilterModel).subscribe({
            next: deviceData => {
              this.selectedSiteChart = this.powerCardsService.generateSingleSiteChart(
                deviceData.powerChartData,
                powerChartSiteSunriseSunsetData,
                site.siteName,
                false
              );
            },
            error: error => {
              this.alertService.showErrorToast('Failed to load chart data. Please try again later.');
            }
          });
        },
        error: error => {
          this.alertService.showErrorToast('Error loading sunrise/sunset data. Please try again later.');
        }
      })
    );
  }

  closeChartModal() {
    this.selectedSiteChart = null;
    this.modalRef?.hide();
  }
}
