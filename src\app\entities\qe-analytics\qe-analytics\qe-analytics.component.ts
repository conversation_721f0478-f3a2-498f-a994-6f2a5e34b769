import { DatePipe } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FILTER_SECTION_ENUM, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { StorageService } from '../../../@shared/services/storage.service';
import { QE_ANALYTICS_HEAT_MAP_COLOR_ENUM, QE_ANALYTICS_HEAT_MAP_COLOR_MAP } from '../models/qe-analytics.enum';
import {
  HeatMapColor,
  ProcessedTableResCategories,
  QEAnalyticsRes,
  QEAnalyticsTableRes,
  QEMenuModuleType
} from '../models/qe-analytics.model';
import { QEAnalyticsService } from '../services/qe-analytics.service';

@Component({
  selector: 'sfl-qe-analytics',
  templateUrl: './qe-analytics.component.html',
  styleUrls: ['./qe-analytics.component.scss']
})
export class QEAnalyticsComponent implements OnInit, OnDestroy {
  loading = false;
  currentPage = 1;
  isFilterDisplay = false;
  isApiErrorFilter = true;
  filterModel: CommonFilter = new CommonFilter();
  subscription: Subscription = new Subscription();
  filterDetails: FilterDetails = new FilterDetails();
  viewPage = FILTER_PAGE_NAME.ADMIN_QE_ANALYTICS;
  viewFilterSection = 'qeAnalyticsFilterSection';
  qeAnalyticsTableRes: QEAnalyticsTableRes[] = [];
  processedTableResCategories: ProcessedTableResCategories[] = [];
  maxItems = 0;
  isMenuModuleFilterApplied = false;

  constructor(
    private readonly storageService: StorageService,
    private readonly qeAnalyticsService: QEAnalyticsService,
    public datepipe: DatePipe
  ) {}

  ngOnInit(): void {
    const passwordProtected = this.storageService.get(AppConstants.qeAnalyticsPasswordAuthKey);
    if (passwordProtected) {
      this.storageService.clear(AppConstants.qeAnalyticsPasswordAuthKey);
    }

    const filter = this.storageService.get(this.viewPage);
    const filterSection = this.storageService.get(this.viewFilterSection);
    this.isFilterDisplay = filterSection;
    if (filter) {
      this.filterModel = filter;
    }

    this.initFilterDetails();

    const pageFilterKeys = ['userTypeIds', 'qeMenuModuleIds', 'dateDuration', 'durationStartDate', ' durationEndDate', 'userIds'];
    if (this.storageService.shouldCallListApi(filter, {}, {}, {}, pageFilterKeys)) {
      this.getQEAnalyticsResponse();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    this.filterDetails.filterSectionEnum = FILTER_SECTION_ENUM.ADMIN_QE_ANALYTICS;
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.USER_TYPE.show = true;
    filterItem.USER_TYPE.multi = true;
    filterItem.USER.show = true;
    filterItem.QE_MODULES.show = true;
    filterItem.QE_DATE_DURATION.show = true;

    this.filterDetails.filter_item = filterItem;
  }

  get maxItemsArray(): number[] {
    return Array.from({ length: this.maxItems }, (_, i) => i);
  }

  private getHeatMapColor(menuClickedPercentage: number): HeatMapColor {
    const clamped = Math.max(0, Math.min(100, menuClickedPercentage));
    const nearestStep = (Math.floor(clamped / 5) * 5) as QE_ANALYTICS_HEAT_MAP_COLOR_ENUM;
    return QE_ANALYTICS_HEAT_MAP_COLOR_MAP[nearestStep];
  }

  private flattenMenuItems(item: QEAnalyticsTableRes, menuLevelOrder = 1): QEAnalyticsTableRes[] {
    const result: QEAnalyticsTableRes[] = [];
    const itemWithMenuLevelOrder = { ...item, menuLevelOrder };

    result.push(menuLevelOrder === 1 || item.childrens?.length > 0 ? itemWithMenuLevelOrder : itemWithMenuLevelOrder);

    if (item.childrens?.length > 0) {
      item.childrens.forEach(child => {
        result.push(...this.flattenMenuItems(child, menuLevelOrder + 1));
      });
    }
    return result;
  }

  private processMenuItem(
    item: QEAnalyticsTableRes,
    qeMenuModuleIds: number[],
    parentMenuNames: string[],
    isFilterApplied: boolean
  ): QEAnalyticsTableRes {
    return {
      ...item,
      heatMapColor: this.getHeatMapColor(item.menuClickedPercentage),
      isFilterApplied: isFilterApplied && (qeMenuModuleIds.includes(item.menuId) || parentMenuNames.includes(item.menuName))
    };
  }

  private parseQEAnalyticsTableResponse(
    qeAnalyticsTableRes: QEAnalyticsTableRes[],
    qeMenuModuleIds: number[],
    parentMenuNames: string[],
    isFilterApplied: boolean
  ): void {
    this.processedTableResCategories = qeAnalyticsTableRes
      .map(processedItem => {
        const processedTableResItems = processedItem.childrens
          .flatMap(item => this.flattenMenuItems(item))
          .map(item => this.processMenuItem(item, qeMenuModuleIds, parentMenuNames, isFilterApplied));

        return {
          ...this.processMenuItem(processedItem, qeMenuModuleIds, parentMenuNames, isFilterApplied),
          processedTableResItems
        };
      })
      .map(processedItem => ({
        ...processedItem,
        isFilterApplied: isFilterApplied && processedItem.processedTableResItems.some(item => item.isFilterApplied)
      }));

    this.maxItems = Math.max(...this.processedTableResCategories.map(cat => cat.processedTableResItems.length));
  }

  private getParentNamesByMenuIds(qeMenuModuleList: QEMenuModuleType[], menuIds: number[]): string[] {
    const result = new Set<string>();
    const visitedIds = new Set<number>();

    const findParentRecursively = (menuItem: QEMenuModuleType): void => {
      if (!menuItem || visitedIds.has(menuItem.menuId)) return;

      visitedIds.add(menuItem.menuId);
      result.add(menuItem.parentName);

      if (menuItem.parentName && menuItem.menuLevelOrder >= 1) {
        const parent = qeMenuModuleList.find(
          item => item.menuName === menuItem.parentName && item.menuLevelOrder >= 1 && item.menuId !== menuItem.menuId
        );
        if (parent) findParentRecursively(parent);
      }
    };

    menuIds.forEach(id => {
      const menuItem = qeMenuModuleList.find(item => item.menuId === id);
      if (menuItem) findParentRecursively(menuItem);
    });

    return Array.from(result);
  }

  parseQEAnalyticsResponse(response: QEAnalyticsRes, qeMenuModuleIds: number[]): void {
    const qeMenuModuleList = this.qeAnalyticsService.setQEMenuModuleListWithEnumMapping();
    const totalMenuModuleName = qeMenuModuleList.map(item => item.menuName);
    const parentMenuNames = this.getParentNamesByMenuIds(qeMenuModuleList, qeMenuModuleIds);
    const isFilterApplied = qeMenuModuleIds?.length > 0 && qeMenuModuleIds.length !== totalMenuModuleName.length;
    this.isMenuModuleFilterApplied = isFilterApplied;
    this.parseQEAnalyticsTableResponse(response.qeAnalyticsTableRes, qeMenuModuleIds, parentMenuNames, isFilterApplied);
    this.loading = false;
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getQEAnalyticsResponse(true, filterParams);
  }

  getQEAnalyticsResponse(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    const filterModel = Object.assign({}, this.filterModel);
    filterModel.durationEndDate = this.datepipe.transform(filterModel.durationEndDate, 'yyyy-MM-dd') as unknown as Date;
    filterModel.durationStartDate = this.datepipe.transform(filterModel.durationStartDate, 'yyyy-MM-dd') as unknown as Date;
    this.subscription.add(
      this.qeAnalyticsService.getQEAnalyticsResponse(filterModel).subscribe({
        next: (response: QEAnalyticsRes) => {
          this.parseQEAnalyticsResponse(response, filterModel.qeMenuModuleIds);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.storageService.clear(AppConstants.qeAnalyticsPasswordAuthKey);
  }
}
