.truck-roll-gallery-container {
  border: 3px solid #53596a;
  border-radius: 5px;
  padding: 1rem;

  .truck-roll-ul {
    list-style: none;
    padding: 0px;
    .basic-info-li {
      border-bottom: 1px solid #53596a;
      padding: 0.75rem;
    }
  }

  .empty-tr {
    td {
      height: 32px;
    }
  }
  .truck-roll-gallery {
    height: 23rem;

    .img-container {
      height: 195px;
      width: 195px;
      img {
        aspect-ratio: 1/1;
        object-position: center;
        object-fit: cover;
      }
    }
  }

  .tech-name {
    min-width: fit-content;
  }

  .img-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 3px;
    box-sizing: border-box;
    text-align: center;
    border-radius: 5px;
  }
}

::ng-deep modal-container .img-view-section {
  .btnClose {
    top: 0 !important;
  }
  .image-viwer {
    overflow: hidden;
    div {
      overflow: hidden;
    }
  }
  max-width: 60% !important;
  .carousel-inner {
    overflow: hidden !important;
    .item {
      text-align: center;
      .img-slide {
        width: unset;
        max-width: 100%;
        vertical-align: middle;
        max-height: 95vh;
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .truck-roll-gallery-container {
    .truck-roll-gallery {
      .img-container {
        height: 175px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .truck-roll-gallery-container {
    .truck-roll-gallery {
      .img-container {
        height: 175px;
      }
    }
  }
}

@media screen and (max-width: 576px) {
  .truck-roll-gallery-container {
    .truck-roll-gallery {
      .img-container {
        width: 100%;
        height: fit-content;
      }
    }
  }
}
