import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';

@Component({
  selector: 'sfl-completion-forecast-chart',
  templateUrl: './completion-forecast-chart.component.html',
  styleUrls: ['./completion-forecast-chart.component.scss']
})
export class CompletionForecastChartComponent implements OnInit, OnChanges {
  @Input() forecastChart: any = {};
  @Input() selectedPortfolioName: string;
  @Input() assessmentType: string;
  chartsData: any;
  currentTheme = 'dark';
  constructor() {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.forecastChart && changes.forecastChart.currentValue) {
      const data = {
        title: {
          text: `${this.selectedPortfolioName}- PM Completion Forecast`,
          left: 'center',
          top: 45,
          textStyle: {
            fontSize: '0.9375rem'
          }
        },
        color: ['#b7d6a3', '#4472C4', '#ed7d31', '#7f7f7f', '#a349a4'],
        grid: {
          y: 100,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: [],
          type: 'scroll',
          bottom: 10
        },
        toolbox: {
          show: true,
          orient: 'horizontal',
          feature: {
            dataZoom: { show: true },
            dataView: {
              show: true,
              readOnly: true,
              optionToContent: opt => {
                let series = opt.series;
                let axisData = opt.xAxis[0]['data'];

                let table = '<table id="site-performance-table" class="table table-bordered"><thead><tr>' + '<th>Month</th>';
                for (let i = 0, l = series.length; i < l; i++) {
                  table += `<th>${series[i].name}</th>`;
                }
                table += '</tr></thead><tbody style="color:#000000">';
                for (let i = 0, l = series[0].data.length; i < l; i++) {
                  table += '<tr>';
                  table += `<td style="color:#000000">${axisData[i]}</td>`;
                  for (let j = 0, m = series.length; j < m; j++) {
                    table += `<td style="text-align:right; color:#000000;">${series[j].data[i].toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}</td>`;
                  }
                  table += '</tr>';
                }
                table += '</thead></table>';
                return table;
              }
            },
            restore: {},
            saveAsImage: {}
          },
          top: 7,
          right: 40
        },
        xAxis: [
          {
            type: 'category'
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: `# of ${this.assessmentType}`,
            nameTextStyle: {
              fontWeight: 'bolder',
              align: 'right',
              padding: [0, 6, 0, 0]
            }
          },
          {
            type: 'value',
            name: `# of ${this.assessmentType} Completion %`,
            position: 'right',
            align: 'center',
            axisLabel: {
              formatter: '{value}%'
            },
            min: 0,
            max: 100
          }
        ],
        series: []
      };
      data.legend.data = this.forecastChart['dataOf'];
      data.xAxis[0]['data'] = this.forecastChart['labels'];

      this.forecastChart['eChartObjects']?.forEach((item, index) => {
        data.series.push({
          ...item,
          type: item.name === '%Complete' ? 'line' : 'bar',
          stack: item.name === '%Complete' ? 'Total' : item.name !== `Completed ${this.assessmentType}s` ? 'Ad' : 'none',
          yAxisIndex: item.name === '%Complete' ? 1 : 0,
          areaStyle: item.name === '%Complete' ? { origin: 'start', opacity: 0.3 } : {},
          emphasis: {
            focus: 'series'
          },
          itemStyle: item.name === '%Complete' ? { z: 1 } : { z: 2 },
          lineStyle: item.name === '%Complete' ? { width: 0, type: 'solid' } : {},
          zlevel: item.name === '%Complete' ? 0 : 1,
          barGap: 0,
          tooltip: {
            valueFormatter: function (value: any) {
              return (
                (value as number).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' Svs'
              );
            }
          }
        });

        if (this.forecastChart['eChartObjects']?.length - 1 === index) this.chartsData = data;
      });
    }
  }

  ngOnInit(): void {}
}
