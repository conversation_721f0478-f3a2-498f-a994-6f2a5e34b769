import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { ApiUrl } from '../../@shared/constants';
import { MessageVM } from '../../@shared/models/messageVM.model';
import { NcComponentListing, PmActionRecommendationListResponse, PmIssueObservationListResponse } from './pm-non-conformance.model';

@Injectable({
  providedIn: 'root'
})
export class PmNonConformanceService {
  searchNcComponentSubject = new Subject<string>();
  searchIssueSubject = new Subject<string>();
  searchActionSubject = new Subject<string>();
  constructor(private readonly http: HttpClient) {}

  getNcComponentsList(reqParams): Observable<NcComponentListing[]> {
    return this.http.post<NcComponentListing[]>(`${ApiUrl.GET_NC_COMPONENTS_LIST}`, reqParams);
  }

  getNcIssueObservationList(reqParams): Observable<PmIssueObservationListResponse[]> {
    return this.http.post<PmIssueObservationListResponse[]>(`${ApiUrl.GET_NC_ISSUE_LIST}`, reqParams);
  }

  addUpdateIssueObservation(obj): Observable<MessageVM> {
    return this.http.post<MessageVM>(ApiUrl.ADD_UPDATE_NC_ISSUE, obj);
  }

  deleteNcIssueObservationById(id: number): Observable<MessageVM> {
    return this.http.delete<MessageVM>(`${ApiUrl.DELETE_NC_ISSUE}${id}`);
  }

  activeInactiveNcIssueObservationById(id: number): Observable<MessageVM> {
    return this.http.put<MessageVM>(`${ApiUrl.ACTIVE_INACTIVE_NC_ISSUE}${id}`, {});
  }

  addUpdateNcActionRecommendation(obj): Observable<MessageVM> {
    return this.http.post<MessageVM>(ApiUrl.ADD_UPDATE_NC_ACTION_RECOMMENDATION, obj);
  }

  getNcActionRecommendations(reqParams): Observable<PmActionRecommendationListResponse[]> {
    return this.http.post<PmActionRecommendationListResponse[]>(`${ApiUrl.GET_NC_ACTION_RECOMMENDATIONS}`, reqParams);
  }

  activeInactiveNcActionRecommendationById(id: number): Observable<MessageVM> {
    return this.http.put<MessageVM>(`${ApiUrl.ACTIVE_INACTIVE_ACTION_RECOMMENDATION}${id}`, {});
  }

  deleteNcActionRecommendationById(id: number): Observable<MessageVM> {
    return this.http.delete<MessageVM>(`${ApiUrl.DELETE_NC_ACTION_RECOMMENDATION}${id}`);
  }

  activeInactiveNcComponentById(id: number): Observable<MessageVM> {
    return this.http.put<MessageVM>(`${ApiUrl.ACTIVE_INACTIVE__NC_COMPONENT}${id}`, {});
  }

  deleteNcComponentById(id: number): Observable<MessageVM> {
    return this.http.delete<MessageVM>(`${ApiUrl.DELETE_NC_COMPONENT}${id}`);
  }

  addUpdateNcComponent(obj): Observable<MessageVM> {
    return this.http.post<MessageVM>(ApiUrl.ADD_UPDATE_NC_COMPONENT, obj);
  }
}
