import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { QE_MENU_MODULE_ENUM, QE_MENU_MODULE_NAME_ENUM } from '../../../@shared/enums/qe-menu.enum';
import { QEMenuInteractionEventObj, QEMenuModuleType } from '../models/qe-analytics.model';
import { QEAnalyticsService } from './qe-analytics.service';
import { QE_MENU_INTERACTION_EVENT_TYPE_ENUM, QE_MENU_MODULE_INTERACTION_EVENT_TYPE_NAME } from '../models/qe-analytics.enum';
import { ActivatedRouteSnapshot } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsGatheringService {
  private qeMenuModuleList: QEMenuModuleType[] = [];
  private apiCallQueue: QEMenuInteractionEventObj[] = [];
  private isProcessingQueue: boolean = false;

  constructor(private readonly qeAnalyticsService: QEAnalyticsService) {}

  private captureQEMenuAnalytics(qeMenuModuleItem: QEMenuInteractionEventObj): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.qeAnalyticsService.captureQEMenuAnalytics(qeMenuModuleItem).subscribe({
        next: () => resolve(),
        error: err => reject(err)
      });
    });
  }

  private async processApiCallQueue(): Promise<void> {
    while (this.apiCallQueue.length > 0) {
      const requestObj = this.apiCallQueue.shift();
      if (requestObj) {
        try {
          await this.captureQEMenuAnalytics(requestObj);
        } catch (error) {
          console.error(error);
        }
      }
    }
    this.isProcessingQueue = false;
  }

  private makeAPICallForMenuItem(qeMenuInteractionEventItem: QEMenuInteractionEventObj): void {
    console.log(qeMenuInteractionEventItem);
    if (environment.env !== 'local') {
      this.apiCallQueue.push(qeMenuInteractionEventItem);

      if (!this.isProcessingQueue) {
        this.isProcessingQueue = true;
        this.processApiCallQueue();
      }
    }
  }

  private makeClickEventOnMenuItemForAPICall(
    qeMenuModuleItem: QEMenuModuleType,
    interactionEventType: QE_MENU_INTERACTION_EVENT_TYPE_ENUM,
    randomUniqueKey: string
  ): void {
    if (qeMenuModuleItem) {
      const interactionEventName = QE_MENU_MODULE_INTERACTION_EVENT_TYPE_NAME[interactionEventType];

      switch (interactionEventType) {
        case QE_MENU_INTERACTION_EVENT_TYPE_ENUM.QE_MENU_CLICK:
          const menuClickRequestObj: QEMenuInteractionEventObj = {
            ...qeMenuModuleItem,
            interactionEventName,
            interactionEventType,
            interactionEventTime: new Date().toISOString(),
            randomUniqueKey
          };
          this.makeAPICallForMenuItem(menuClickRequestObj);
          break;
        case QE_MENU_INTERACTION_EVENT_TYPE_ENUM.QE_MENU_VISIT_START_END:
          const menuVisitStartRequestObj: QEMenuInteractionEventObj = {
            ...qeMenuModuleItem,
            interactionEventName,
            interactionEventType,
            interactionEventTime: new Date().toISOString(),
            interactionStartTime: new Date().toISOString(),
            interactionEndTime: new Date().toISOString(),
            randomUniqueKey
          };
          this.makeAPICallForMenuItem(menuVisitStartRequestObj);
          break;
        case QE_MENU_INTERACTION_EVENT_TYPE_ENUM.QE_MENU_DATA_REFRESH:
          const menuVisitEndRequestObj: QEMenuInteractionEventObj = {
            ...qeMenuModuleItem,
            interactionEventName,
            interactionEventType,
            interactionEventTime: new Date().toISOString(),
            randomUniqueKey
          };
          this.makeAPICallForMenuItem(menuVisitEndRequestObj);
          break;
        default:
          break;
      }
    }
  }

  setQEMenuModuleList(): void {
    const qeMenuModuleList =
      this.qeMenuModuleList && this.qeMenuModuleList.length > 0
        ? this.qeMenuModuleList
        : this.qeAnalyticsService.setQEMenuModuleListWithEnumMapping();
    this.qeMenuModuleList = qeMenuModuleList;
  }

  makeClickEventOnMenuItem(
    menuItemEnum: QE_MENU_MODULE_ENUM,
    parentItemEnum: QE_MENU_MODULE_ENUM,
    menuItemName: string,
    parentItemName: string,
    interactionEventType: QE_MENU_INTERACTION_EVENT_TYPE_ENUM,
    randomUniqueKey: string
  ): void {
    const qeMenuModuleItem = this.qeMenuModuleList.find(
      item =>
        (item.menuUniqueId === menuItemEnum || item.menuName === menuItemName) &&
        (item.menuParentId === parentItemEnum || item.parentName === parentItemName)
    );

    this.makeClickEventOnMenuItemForAPICall(qeMenuModuleItem, interactionEventType, randomUniqueKey);
  }

  captureQEAnalyticsItemEnum(
    qeAnalyticsItemEnum: QE_MENU_MODULE_ENUM,
    qeAnalyticsParentItemEnum: QE_MENU_MODULE_ENUM,
    interactionEventType: QE_MENU_INTERACTION_EVENT_TYPE_ENUM,
    randomUniqueKey: string
  ): void {
    this.setQEMenuModuleList();
    const qeAnalyticsItemTitle = QE_MENU_MODULE_NAME_ENUM[qeAnalyticsItemEnum];
    const qeAnalyticsParentItemTitle = QE_MENU_MODULE_NAME_ENUM[qeAnalyticsParentItemEnum];
    this.makeClickEventOnMenuItem(
      qeAnalyticsItemEnum,
      qeAnalyticsParentItemEnum,
      qeAnalyticsItemTitle,
      qeAnalyticsParentItemTitle,
      interactionEventType,
      randomUniqueKey
    );
  }

  onCaptureQEAnalyticsItemEmit(route: ActivatedRouteSnapshot, qeMenuInteractionEventType: QE_MENU_INTERACTION_EVENT_TYPE_ENUM): void {
    const qeAnalyticsMenuItemEnum = route.data['qeAnalyticsMenuItem'] || QE_MENU_MODULE_ENUM.PM_DASHBOARD;
    const qeAnalyticsParentItemEnum = route.data['qeAnalyticsParentItem'] || QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE;
    this.captureQEAnalyticsItemEnum(qeAnalyticsMenuItemEnum, qeAnalyticsParentItemEnum, qeMenuInteractionEventType, this.randomUniqueKey);
  }
}
