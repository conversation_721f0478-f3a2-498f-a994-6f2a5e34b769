import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { QE_MENU_MODULE_ENUM, QE_MENU_MODULE_NAME_ENUM } from '../../../@shared/enums/qe-menu.enum';
import { QEMenuModuleType } from '../models/qe-analytics.model';
import { QEAnalyticsService } from './qe-analytics.service';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsGatheringService {
  private qeMenuModuleList: QEMenuModuleType[] = [];
  private apiCallQueue: QEMenuModuleType[] = [];
  private isProcessingQueue: boolean = false;

  constructor(private readonly qeAnalyticsService: QEAnalyticsService) {}

  private captureQEMenuAnalytics(qeMenuModuleItem: QEMenuModuleType): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.qeAnalyticsService.captureQEMenuAnalytics(qeMenuModuleItem).subscribe({
        next: () => resolve(),
        error: err => reject(err)
      });
    });
  }

  private async processApiCallQueue(): Promise<void> {
    while (this.apiCallQueue.length > 0) {
      const requestObj = this.apiCallQueue.shift();
      if (requestObj) {
        try {
          await this.captureQEMenuAnalytics(requestObj);
        } catch (error) {
          console.error(error);
        }
      }
    }
    this.isProcessingQueue = false;
  }

  setQEMenuModuleList(): void {
    const qeMenuModuleList =
      this.qeMenuModuleList && this.qeMenuModuleList.length > 0
        ? this.qeMenuModuleList
        : this.qeAnalyticsService.setQEMenuModuleListWithEnumMapping();
    this.qeMenuModuleList = qeMenuModuleList;
  }

  makeClickEventOnMenuItem(
    menuItemEnum: QE_MENU_MODULE_ENUM,
    parentItemEnum: QE_MENU_MODULE_ENUM,
    menuItemName: string,
    parentItemName: string
  ): void {
    const qeMenuModuleItem = this.qeMenuModuleList.find(
      item =>
        (item.menuUniqueId === menuItemEnum || item.menuName === menuItemName) &&
        (item.menuParentId === parentItemEnum || item.parentName === parentItemName)
    );
    if (qeMenuModuleItem && environment.env !== 'local') {
      this.apiCallQueue.push(qeMenuModuleItem);

      if (!this.isProcessingQueue) {
        this.isProcessingQueue = true;
        this.processApiCallQueue();
      }
    }
  }

  captureQEAnalyticsItemEnum(qeAnalyticsItemEnum: QE_MENU_MODULE_ENUM, qeAnalyticsParentItemEnum: QE_MENU_MODULE_ENUM): void {
    this.setQEMenuModuleList();
    const qeAnalyticsItemTitle = QE_MENU_MODULE_NAME_ENUM[qeAnalyticsItemEnum];
    const qeAnalyticsParentItemTitle = QE_MENU_MODULE_NAME_ENUM[qeAnalyticsParentItemEnum];
    this.makeClickEventOnMenuItem(qeAnalyticsItemEnum, qeAnalyticsParentItemEnum, qeAnalyticsItemTitle, qeAnalyticsParentItemTitle);
  }
}
