import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit, TemplateRef, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription, forkJoin } from 'rxjs';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { APP_ROUTES, AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { AllReportDropdown } from '../../../@shared/models/report.model';
import { TowDecimalPipe } from '../../../@shared/pipes/decimal.pipe';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerService } from '../../customer-management/customer.service';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { SiteService } from '../../site-management/site.service';
import { GridViewReportComponent } from './grid-view-report/grid-view-report.component';
import { FilterModel, OutagePortFolioTabularData, OutageSiteTabularData, OutageTickets } from './model/outage.model';
import { OutageService } from './outage.service';

@Component({
  selector: 'sfl-outage',
  templateUrl: './outage.component.html',
  styleUrls: ['./outage.component.scss'],
  providers: [TowDecimalPipe],
  encapsulation: ViewEncapsulation.None
})
export class OutageComponent implements OnInit {
  filterModel: FilterModel = new FilterModel();
  copyFilterModel: FilterModel = new FilterModel();
  showDownloadButton = false;
  loading = false;
  portfolioLoading = false;
  siteListLoading = false;
  isFullView = false;
  outageStatusList: Dropdown[] = [
    {
      id: 1,
      name: 'Active'
    },
    {
      id: 2,
      name: 'Ticketed'
    },
    {
      id: 3,
      name: 'Resolved'
    }
  ];
  severityList: Dropdown[] = [
    {
      id: 1,
      name: '|'
    },
    {
      id: 2,
      name: '||'
    },
    {
      id: 3,
      name: '|||'
    }
  ];
  deviceTypeList: Dropdown[] = [
    {
      id: 1,
      name: 'Inverter'
    },
    {
      id: 2,
      name: 'Meter'
    }
  ];
  subscription: Subscription = new Subscription();
  customerList: Dropdown[] = [];
  portfolioList: Dropdown[] = [];
  siteList: Dropdown[] = [];
  tickets: OutageTickets[] = [];
  portfolioOutageData: OutagePortFolioTabularData[] = [];
  siteOutageData: OutageSiteTabularData[] = [];
  modalRef: BsModalRef;
  selectedList = {
    customer: { id: null, name: null },
    portfolio: { id: null, name: null },
    site: { id: null, name: null }
  };
  nextButton = { text: null, for: null, id: null };
  previousButton = { text: null, for: null, id: null };
  viewPage = 'performanceOutagePage';
  dateFormat = AppConstants.dateTimeFormat;
  showBreadcrumb = true;
  isMasterSel = false;
  selectedAlertId: number;
  selectedDeviceName = '';
  monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

  constructor(
    private readonly portfolioService: PortfolioService,
    public datePipe: DatePipe,
    private readonly cdf: ChangeDetectorRef,
    private readonly outageService: OutageService,
    private readonly siteService: SiteService,
    private readonly customerService: CustomerService,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly alertService: AlertService,
    private readonly router: Router,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit() {
    let filter: FilterModel = this.storageService.get(this.viewPage);
    if (filter) {
      filter.date = filter.date ? new Date(filter.date) : new Date();
    }
    this.filterModel = filter ? filter : this.filterModel;
    let model: FilterModel = { ...this.filterModel };
    model.date = this.datePipe.transform(this.filterModel.date, AppConstants.fullDateFormat);
    const apiArray = [this.customerService.getAllCustomer()];
    const tempObj = ['customerList'];
    this.route.queryParams.subscribe(params => {
      this.processParams(params);
    });
    this.loading = true;
    if (filter) {
      if (this.filterModel.customerId) {
        const data: AllReportDropdown = new AllReportDropdown();
        data.ids = [this.filterModel.customerId];
        apiArray.push(this.portfolioService.getAllReportPortfoliosByCustomerId(data));
        tempObj.push('portfolioList');
      }
      if (this.filterModel.portfolioId) {
        const data: AllReportDropdown = new AllReportDropdown();
        data.customerIds = [this.filterModel.customerId];
        data.ids = [this.filterModel.portfolioId];
        apiArray.push(this.siteService.getAllReportSitesByPortfolioId(data));
        tempObj.push('siteList');
      }
      if (this.filterModel.siteId) {
        apiArray.push(this.outageService.getOutageSiteReportByFilter(model));
        tempObj.push('siteOutageData');
      } else {
        apiArray.push(this.outageService.getOutagePortfolioReportByFilter(model));
        tempObj.push('portfolioOutageData');
      }
      this.getAllLists(apiArray, tempObj);
    } else {
      apiArray.push(this.outageService.getOutagePortfolioReportByFilter(model));
      tempObj.push('portfolioOutageData');
      this.getAllLists(apiArray, tempObj);
    }
  }

  processParams(params: any): void {
    if (params) {
      this.filterModel.customerId = params.customerId ? Number(params.customerId) : null;
      this.filterModel.portfolioId = params.portfolioId ? Number(params.portfolioId) : null;
      this.filterModel.siteId = params.siteId ? Number(params.siteId) : null;
      this.filterModel.date = params.date ? new Date(params.date) : new Date();
    }
  }

  getAllLists(apiArray: any, mapResultList: string[]) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          if (value === 'customerList' || value === 'portfolioList' || value === 'siteList') {
            this[value] = res[index].filter(item => item.isActive);
          } else {
            this[value] = res[index];
          }
        }
        setTimeout(() => {
          if (mapResultList.indexOf('portfolioList') > -1) {
            this.onCustomerSelect({ id: this.filterModel.customerId }, this.filterModel.portfolioId ? false : true, true);
          }
          if (mapResultList.indexOf('siteList') > -1) {
            this.onPortfolioSelect({ id: this.filterModel.portfolioId }, this.filterModel.siteId ? false : true, true);
          }
          if (this.filterModel.siteId) {
            this.onSiteSelectOrDeSelect({ id: this.filterModel.siteId }, true, true);
          }
          this.loading = false;
        }, 0);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getOutageReport() {
    if (this.filterModel.customerId) {
      const data = this.customerList.find(x => x.id === this.filterModel.customerId);
      this.selectedList.customer.id = data.id;
      this.selectedList.customer.name = data.name;
    } else {
      this.selectedList.customer.id = null;
      this.selectedList.customer.name = null;
    }
    if (this.filterModel.portfolioId) {
      const data = this.portfolioList.find(x => x.id === this.filterModel.portfolioId);
      this.selectedList.portfolio.id = data.id;
      this.selectedList.portfolio.name = data.name.split(' (')[0];
    } else {
      this.selectedList.portfolio.id = null;
      this.selectedList.portfolio.name = null;
    }
    if (this.filterModel.siteId) {
      const data = this.siteList.find(x => x.id === this.filterModel.siteId);
      this.selectedList.site.id = data.id;
      this.selectedList.site.name = data.name.split(' (')[0];
    } else {
      this.selectedList.site.id = null;
      this.selectedList.site.name = null;
    }
    let model: FilterModel = { ...this.filterModel };
    model.date = this.datePipe.transform(this.filterModel.date, AppConstants.fullDateFormat);
    if (!this.filterModel.siteId) {
      this.getOutagePortfolioTabularData(model);
    } else {
      this.getOutageSiteTabularData(model);
    }
  }

  getOutagePortfolioTabularData(getOutageReportParams: FilterModel) {
    this.loading = true;
    this.subscription.add(
      this.outageService.getOutagePortfolioReportByFilter(getOutageReportParams).subscribe({
        next: (res: OutagePortFolioTabularData[]) => {
          this.portfolioOutageData = res;
          this.siteOutageData = [];
          this.loading = false;
          this.storageService.set(this.viewPage, getOutageReportParams);
          this.showBreadcrumb = true;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getOutageSiteTabularData(getOutageReportParams: FilterModel) {
    this.loading = true;
    this.subscription.add(
      this.outageService.getOutageSiteReportByFilter(getOutageReportParams).subscribe({
        next: (res: OutageSiteTabularData[]) => {
          this.siteOutageData = res;
          this.portfolioOutageData = [];
          this.loading = false;
          this.storageService.set(this.viewPage, getOutageReportParams);
          this.showBreadcrumb = true;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getAllCustomer() {
    this.loading = true;
    this.subscription.add(
      this.customerService.getAllCustomer().subscribe({
        next: (res: Dropdown[]) => {
          this.customerList = res.filter(item => item.isActive);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onSiteSelectOrDeSelect(event = null, viewData = true, isFirstLoad = false) {
    if (this.siteList.length) {
      if (!event) {
        const index = this.portfolioList.findIndex(x => x.id === this.selectedList.portfolio.id);
        this.previousButton = { text: null, for: null, id: null };
        this.nextButton = { text: null, for: null, id: null };
        if (this.portfolioList[index + 1]) {
          this.nextButton = {
            text: this.portfolioList[index + 1].name.split(' (')[0],
            for: 'portfolio',
            id: this.portfolioList[index + 1].id
          };
        }
        if (index > 0) {
          this.previousButton = {
            text: this.portfolioList[index - 1].name.split(' (')[0],
            for: 'portfolio',
            id: this.portfolioList[index - 1].id
          };
        }
      } else {
        const index = this.siteList.findIndex(x => x.id === event.id);
        this.previousButton = { text: null, for: null, id: null };
        this.nextButton = { text: null, for: null, id: null };
        if (this.siteList[index + 1]) {
          this.nextButton = {
            text: this.siteList[index + 1].name.split(' (')[0],
            for: 'site',
            id: this.siteList[index + 1].id
          };
        }
        if (index > 0) {
          this.previousButton = {
            text: this.siteList[index - 1].name.split(' (')[0],
            for: 'site',
            id: this.siteList[index - 1].id
          };
        }
        if (event && !event.name) {
          event['name'] = this.siteList[index].name;
        }
      }
      this.selectedList.site.id = event ? event.id : null;
      this.selectedList.site.name = event ? event.name.split(' (')[0] : null;
      if (viewData) {
        this.getOutageReport();
      }
    }
  }

  ClearFilter() {
    this.siteList = [];
    this.portfolioList = [];
    this.selectedList = {
      customer: { id: null, name: null },
      portfolio: { id: null, name: null },
      site: { id: null, name: null }
    };
    this.siteOutageData = [];
    this.portfolioOutageData = [];
    this.filterModel = new FilterModel();
    this.storageService.set(this.viewPage, this.filterModel);
  }

  onPortfolioDeSelect(clear = true, needRemove = true) {
    if (needRemove) {
      this.siteList = [];
      this.filterModel.siteId = null;
      this.selectedList.site = { id: null, name: null };
    }
    if (clear) {
      this.previousButton = { text: null, for: null, id: null };
      this.nextButton = { text: null, for: null, id: null };

      const index = this.customerList.findIndex(x => x.id === this.filterModel.customerId);
      this.previousButton = { text: null, for: null, id: null };
      this.nextButton = { text: null, for: null, id: null };
      if (this.customerList[index + 1]) {
        this.nextButton = {
          text: this.customerList[index + 1].name,
          for: 'customer',
          id: this.customerList[index + 1].id
        };
      }
      if (index > 0) {
        this.previousButton = {
          text: this.customerList[index - 1].name,
          for: 'customer',
          id: this.customerList[index - 1].id
        };
      }
    }
  }

  onPortfolioSelect(event = null, viewData = true, isFirstLoad = false) {
    if (this.portfolioList.length) {
      if (!isFirstLoad) {
        this.onPortfolioDeSelect(false, viewData);
      }
      if (event) {
        const index = this.portfolioList.findIndex(x => x.id === event.id);
        this.previousButton = { text: null, for: null, id: null };
        this.nextButton = { text: null, for: null, id: null };
        if (this.portfolioList[index + 1]) {
          this.nextButton = {
            text: this.portfolioList[index + 1].name.split(' (')[0],
            for: 'portfolio',
            id: this.portfolioList[index + 1].id
          };
        }
        if (index > 0) {
          this.previousButton = {
            text: this.portfolioList[index - 1].name.split(' (')[0],
            for: 'portfolio',
            id: this.portfolioList[index - 1].id
          };
        }
        if (this.filterModel.portfolioId) {
          if (!isFirstLoad) {
            this.getAllSiteByPortfolio();
          }
          if (viewData) {
            if (event && !event.name) {
              event['name'] = this.portfolioList[index].name;
            }
            this.selectedList.portfolio.id = event ? event.id : null;
            this.selectedList.portfolio.name = event ? event.name.split(' (')[0] : null;
          }
        }
        if (viewData) {
          this.getOutageReport();
        }
      }
    }
  }

  getAllSiteByPortfolio() {
    this.siteListLoading = true;
    const data: AllReportDropdown = new AllReportDropdown();
    if (this.filterModel.customerId) {
      data.customerIds.push(this.filterModel.customerId);
    }
    if (this.filterModel.portfolioId) {
      data.ids.push(this.filterModel.portfolioId);
    }
    this.subscription.add(
      this.siteService.getAllReportSitesByPortfolioId(data).subscribe({
        next: (res: Dropdown[]) => {
          this.siteList = res;
          this.siteListLoading = false;
        },
        error: _e => {
          this.siteListLoading = false;
        }
      })
    );
  }

  onCustomerDeSelect(clear = true, needRemove = true) {
    if (needRemove) {
      this.portfolioList = [];
      this.siteList = [];
      this.filterModel.portfolioId = null;
      this.filterModel.siteId = null;
      this.selectedList.portfolio = { id: null, name: null };
      this.selectedList.site = { id: null, name: null };
    }
    if (clear) {
      this.previousButton = { text: null, for: null, id: null };
      this.nextButton = { text: null, for: null, id: null };
    }
  }

  onCustomerSelect(event = null, viewData = true, isFirstLoad = false) {
    if (!isFirstLoad) {
      this.onCustomerDeSelect(false, viewData);
    }
    if (event) {
      const index = this.customerList.findIndex(x => x.id === event.id);
      this.previousButton = { text: null, for: null, id: null };
      this.nextButton = { text: null, for: null, id: null };
      if (this.customerList[index + 1]) {
        this.nextButton = {
          text: this.customerList[index + 1].name,
          for: 'customer',
          id: this.customerList[index + 1].id
        };
      }
      if (index > 0) {
        this.previousButton = {
          text: this.customerList[index - 1].name,
          for: 'customer',
          id: this.customerList[index - 1].id
        };
      }
      if (this.filterModel.customerId) {
        if (!isFirstLoad) {
          this.getAllPortfolioByCustomer();
        }
        if (viewData) {
          if (event && !event.name) {
            event['name'] = this.customerList[index].name;
          }
          this.selectedList.customer.id = event ? event.id : null;
          this.selectedList.customer.name = event ? event.name : null;
        }
      }
      if (viewData) {
        this.getOutageReport();
      }
    }
  }

  getAllPortfolioByCustomer() {
    this.portfolioLoading = true;
    const data: AllReportDropdown = new AllReportDropdown();
    if (this.filterModel.customerId) {
      data.ids.push(this.filterModel.customerId);
    }
    this.subscription.add(
      this.portfolioService.getAllReportPortfoliosByCustomerId(data).subscribe({
        next: (res: Dropdown[]) => {
          this.portfolioList = res;
          this.portfolioLoading = false;
        },
        error: _e => {
          this.portfolioLoading = false;
        }
      })
    );
  }

  switchRecord(from, to) {
    for (let i = 0; i < this[from].length; i++) {
      this[this[to][i]] = JSON.parse(JSON.stringify(this[this[from][i]]));
    }
  }

  selectValue(obj: OutagePortFolioTabularData, id: number, goTo: string = null) {
    if (!obj && !id) {
      this.filterModel.customerId = null;
      this.filterModel.portfolioId = null;
      this.filterModel.siteId = null;
      this.copyFilterModel = { ...this.filterModel };
      this.getOutageReport();
      this.selectedList = {
        customer: { id: null, name: null },
        portfolio: { id: null, name: null },
        site: { id: null, name: null }
      };
    } else {
      if (!this.filterModel.customerId || goTo === 'customer') {
        this.filterModel.customerId = id ? id : obj.customerId;
        this.copyFilterModel = { ...this.filterModel };
        const data = this.customerList.find(x => x.id === this.filterModel.customerId);
        this.onCustomerSelect(data, true, false);
        this.selectedList.portfolio.id = null;
        this.selectedList.portfolio.name = null;
        this.selectedList.site.id = null;
        this.selectedList.site.name = null;
      } else if (!this.filterModel.portfolioId || goTo === 'portfolio') {
        this.filterModel.portfolioId = id ? id : obj.portfolioId;
        this.copyFilterModel = { ...this.filterModel };
        const data = this.portfolioList.find(x => x.id === this.filterModel.portfolioId);
        this.onPortfolioSelect(data, true, false);
        this.selectedList.site.id = null;
        this.selectedList.site.name = null;
      } else {
        this.filterModel.siteId = id ? id : obj.siteId;
        this.copyFilterModel = { ...this.filterModel };
        const data = this.siteList.find(x => x.id === this.filterModel.siteId);
        this.onSiteSelectOrDeSelect(data, true, false);
      }
    }
  }

  getColorValue(value: string, data: number) {
    if ((value === 'Active' && data > 0) || (value === '' && data === 1)) {
      return '#f22929';
    } else if ((value === 'Ticketed' && data > 0) || (value === '' && data === 2)) {
      return '#edcb42';
    } else if ((value === 'Resolved' && data > 0) || (value === '' && data === 3)) {
      return '#1eeb6c';
    } else {
      return 'transparent';
    }
  }

  getStatusValue(value: number) {
    if (value === 1) {
      return 'Active';
    } else if (value === 2) {
      return 'Ticketed';
    } else if (value === 3) {
      return 'Resolved';
    } else {
      return '';
    }
  }

  compressView() {
    this.isFullView = false;
    this.modalRef.hide();
  }

  expandView(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog'
    };
    this.isFullView = true;
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  exportData() {
    this.loading = true;
    let model: FilterModel = { ...this.filterModel };
    model.date = this.datePipe.transform(this.filterModel.date, AppConstants.fullDateFormat);
    this.subscription.add(
      this.outageService.exportOutageAlertData(model).subscribe({
        next: (res: Blob) => {
          if (res) {
            this.downloadFile(res, model);
          }
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  downloadFile(res: Blob, model) {
    const closeDate = model.date ? new Date(model.date) : new Date();
    const fileName = this.monthNames[closeDate.getMonth()];
    const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
    link.download = this.getPortfolioName(model.portfolioId)
      ? `${this.getPortfolioName(model.portfolioId)}_${fileName} ${closeDate.getFullYear()}.xlsx`
      : `${this.getCustomerName(model.customerId)}_${fileName} ${closeDate.getFullYear()}.xlsx`;
    link.click();
    this.loading = false;
    this.getCustomerName(model.customerId);
  }

  getCustomerName(customerId) {
    const selectedCustomer = this.customerList.find(folio => folio.id === customerId);
    return selectedCustomer ? selectedCustomer.name : '';
  }

  getPortfolioName(portfolioId) {
    const selectedPortfolio = this.portfolioList.find(cus => cus.id === portfolioId);
    return selectedPortfolio ? selectedPortfolio.name : '';
  }

  showGridReportView() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog',
      initialState: {
        filterModel: { ...this.filterModel }
      }
    };
    this.commonService.setAutoLogoutValue(false);
    this.commonService.encryptUserId().subscribe({
      next: response => {
        this.storageService.set(AppConstants.encryptUserId, response);
        this.modalRef = this.modalService.show(GridViewReportComponent, ngModalOptions);
      },
      error: _e => {
        this.modalRef = this.modalService.show(GridViewReportComponent, ngModalOptions);
      }
    });
  }

  goToTicketListing() {
    const filterModel = new CommonFilter();
    filterModel.customerIds = [this.filterModel.customerId];
    filterModel.portfolioIds = [this.filterModel.portfolioId];
    filterModel.siteIds = [this.filterModel.siteId];
    filterModel.statusIds = [1, 2];
    this.storageService.set('ticketsListingPage', filterModel);
    this.router.navigateByUrl(`${APP_ROUTES.TICKET}/list`);
  }

  getTickets(alertId: number, deviceName: string, template: TemplateRef<any>) {
    this.loading = true;
    this.selectedAlertId = alertId;
    this.selectedDeviceName = deviceName;
    this.outageService.getTicketsOfDevices(this.selectedAlertId).subscribe({
      next: response => {
        this.tickets = response;

        this.loading = false;
        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          class: 'modal-full'
        };
        this.modalRef = this.modalService.show(template, ngModalOptions);
      },
      error: () => (this.loading = false)
    });
  }

  selectDeselectAll() {
    this.tickets.forEach(ticket => {
      ticket.isAttached = this.isMasterSel;
    });
  }

  getSelectedValue() {
    return this.tickets.length ? this.tickets.every(ticket => ticket.isAttached) : false;
  }

  submitTicketsForOutage() {
    this.loading = true;
    let data = {
      outageId: this.selectedAlertId,
      outageTickets: this.tickets.filter(ticket => ticket.isAttached)
    };

    this.outageService.addUpdateTicketsForOutage(data).subscribe({
      next: () => {
        this.getOutageReport();
        this.modalRef.hide();
        this.alertService.showSuccessToast('Ticket(s) mapped successfully to alert.');
      },
      error: () => (this.loading = false)
    });
  }

  onModalClose() {
    this.modalRef.hide();
    this.isMasterSel = false;
    this.tickets = [];
  }
}
