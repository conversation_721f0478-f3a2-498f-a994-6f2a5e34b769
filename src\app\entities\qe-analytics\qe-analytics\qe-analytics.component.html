<nb-card class="qe-analytics-spinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Analytics</h6>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row">
      <div class="col-12 qe-analytics-filter appFilter mb-3">
        <sfl-filter
          [filterDetails]="filterDetails"
          [isApiErrorFilter]="isApiErrorFilter"
          (refreshList)="refreshList($event)"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div
        id="fixed-table"
        setTableHeight
        [isFilterDisplay]="isFilterDisplay"
        [hasPagination]="false"
        class="col-12 table-responsive table-card-view"
      >
        <ng-container *ngIf="processedTableResCategories && processedTableResCategories.length > 0; else noDataFound">
          <table class="table table-bordered" aria-describedby="Analytics">
            <tbody>
              <tr>
                <ng-container *ngFor="let processedItem of processedTableResCategories">
                  <td
                    class="table-cell-width"
                    [style.backgroundColor]="processedItem?.heatMapColor?.hexBGColor"
                    [style.color]="processedItem?.heatMapColor?.hexTextColor"
                    [style.fontWeight]="processedItem?.childrens && processedItem?.childrens.length > 0 ? '600' : 'normal'"
                    [style.opacity]="!processedItem?.isFilterApplied && isMenuModuleFilterApplied ? '15%' : '100%'"
                  >
                    <span> {{ processedItem.menuName }}</span>
                    <ng-container *ngIf="processedItem.menuClickedPercentage">
                      <span class="float-end"> {{ processedItem.menuClickedPercentage }}% </span>
                    </ng-container>
                  </td>
                </ng-container>
              </tr>
              <ng-container *ngFor="let row of maxItemsArray">
                <tr>
                  <ng-container *ngFor="let processedItem of processedTableResCategories; let i = index">
                    <td
                      class="table-cell-width"
                      [style.backgroundColor]="processedItem.processedTableResItems[row]?.heatMapColor?.hexBGColor"
                      [style.color]="processedItem.processedTableResItems[row]?.heatMapColor?.hexTextColor"
                      [style.fontWeight]="
                        processedItem.processedTableResItems[row]?.childrens &&
                        processedItem?.processedTableResItems[row].childrens.length > 0
                          ? '600'
                          : 'normal'
                      "
                      [style.opacity]="
                        !processedItem?.processedTableResItems[row]?.isFilterApplied && isMenuModuleFilterApplied ? '15%' : '100%'
                      "
                    >
                      <ng-container *ngIf="processedItem.processedTableResItems[row]">
                        <span [style.marginLeft.px]="16 * processedItem.processedTableResItems[row].menuLevelOrder">
                          {{ processedItem.processedTableResItems[row].menuName }}
                        </span>
                        <ng-container *ngIf="processedItem.processedTableResItems[row].menuClickedPercentage">
                          <span class="float-end"> {{ processedItem.processedTableResItems[row].menuClickedPercentage }}% </span>
                        </ng-container>
                      </ng-container>
                    </td>
                  </ng-container>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </ng-container>
        <ng-template #noDataFound>
          <div class="d-flex justify-content-center align-items-center h-100">No data found</div>
        </ng-template>
      </div>
    </div>
  </nb-card-body>
</nb-card>
