<div class="modal-content" id="activitylogticket" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header modal-fixed-header">
    <div class="d-flex align-items-center w-100">
      <h6>{{ actionMode + ' ACTION/RECOMMENDATION' }}</h6>
      <div class="ms-auto">
        <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
          <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
        </button>
      </div>
    </div>
  </div>
  <form
    name="actionItemForm"
    #actionItemForm="ngForm"
    aria-labelledby="title"
    autocomplete="off"
    class="action-form"
    (ngSubmit)="actionItemForm?.form?.valid && onAddEditAction()"
  >
    <div class="modal-body">
      <div class="row">
        <div class="col-12 mb-2">
          <label class="label" for="selectedIssueObservation"> Issue/Observation <span class="ms-1 text-danger">*</span> </label>
          <div>
            <ng-select
              class="model-dd"
              name="selectedIssueObservation"
              id="selectedIssueObservation"
              [items]="issueObservationList"
              bindLabel="issueObservation"
              bindValue="id"
              notFoundText="No issueObservation Found"
              placeholder="Select issueObservation"
              #selectedIssueObservation="ngModel"
              [(ngModel)]="actionItem.issueObservationId"
              required
            >
            </ng-select>
            <div class="error-message-section">
              <sfl-error-msg
                [control]="selectedIssueObservation"
                [isFormSubmitted]="actionItemForm?.submitted"
                fieldName="Issue/Observation"
              ></sfl-error-msg>
            </div>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="label" for="actionsRecommendation">Actions/Recommendation <span class="ms-1 text-danger">*</span></label>
        <textarea
          nbInput
          fullWidth
          name="actionsRecommendation"
          id="input-actionsRecommendation"
          #actionsRecommendation="ngModel"
          [(ngModel)]="actionItem.actionRecommendation"
          maxlength="5120"
          required
        >
        </textarea>
        <sfl-error-msg
          [control]="actionsRecommendation"
          [isFormSubmitted]="actionItemForm?.submitted"
          fieldName="Actions/Recommendation"
        ></sfl-error-msg>
      </div>
    </div>
    <div class="modal-footer">
      <button nbButton type="button" status="basic" size="medium" (click)="onCancel()">Cancel</button>
      <button nbButton status="primary" size="medium" type="submit" id="closeSubmit">Save</button>
    </div>
  </form>
</div>
