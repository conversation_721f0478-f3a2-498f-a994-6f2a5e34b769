<div [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-accordion>
    <nb-accordion-item class="border-bottom mb-2">
      <nb-accordion-item-header class="accordion_head">
        Component
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          id="ncIssueLog"
          class="addStyle"
          (click)="$event.stopPropagation(); openNcComponentPanel(null, ADD_EDIT_COPY_MODE.ADD)"
        >
          Add New
        </button>
      </nb-accordion-item-header>
      <nb-accordion-item-body>
        <div class="row">
          <div class="col-sm-4 col-md-2">
            <input
              nbInput
              id="search-by-Issue"
              type="text"
              fullWidth
              [(ngModel)]="ncComponentSearchText"
              (ngModelChange)="onNcComponentSearchChange($event)"
              placeholder="Search"
            />
          </div>
        </div>
        <div class="row">
          <div id="fixed-table" setTableHeight class="col-12 table-responsive mt-3 table-card-view">
            <table class="table table-hover table-bordered">
              <thead>
                <tr>
                  <th scope="col" (click)="sortComponent('name', sortCmpOptionList['name'])">
                    <div class="d-flex justify-content-center align-items-center">
                      Component Name
                      <span
                        class="fa cursor-pointer ms-2"
                        [ngClass]="{
                          'fa-arrow-up': sortCmpOptionList['name'] === 'desc',
                          'fa-arrow-down': sortCmpOptionList['name'] === 'asc',
                          'icon-selected': componentFilterModel.sortBy === 'name'
                        }"
                      ></span>
                    </div>
                  </th>
                  <th scope="col" class="text-center">Active</th>
                  <th scope="col" class="text-center action" id="action">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let ncComponent of ncComponentList; let i = index">
                  <td data-title="Component" class="text-center">{{ ncComponent?.name }}</td>
                  <td data-title="Active" class="text-center">
                    <nb-toggle
                      (checkedChange)="onActiveInactive($event, ncComponent, ACCORDION_ITEMS.COMPONENT)"
                      [(ngModel)]="ncComponent.isActive"
                    ></nb-toggle>
                  </td>
                  <td data-title="Action">
                    <div class="d-md-flex justify-content-center">
                      <a class="px-2 listgrid-icon">
                        <em
                          class="fa-solid fa-copy"
                          nbTooltip="Copy"
                          (click)="openNcComponentPanel(ncComponent, ADD_EDIT_COPY_MODE.COPY)"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                        ></em>
                      </a>
                      <a class="listgrid-icon px-2" (click)="openNcComponentPanel(ncComponent, ADD_EDIT_COPY_MODE.EDIT)">
                        <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                      </a>
                      <a class="text-danger px-2 listgrid-icon" (click)="onDelete(ncComponent.evId, ACCORDION_ITEMS.COMPONENT)">
                        <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                      </a>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="12" *ngIf="!ncComponentList?.length" class="no-record text-center">No Data Found</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </nb-accordion-item-body>
    </nb-accordion-item>
    <nb-accordion-item class="border-bottom mb-2">
      <nb-accordion-item-header class="accordion_head">
        Issue/Observation
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          id="ncIssueLog"
          class="addStyle"
          (click)="$event.stopPropagation(); openIssuePanel(null, ADD_EDIT_COPY_MODE.ADD)"
        >
          Add New
        </button>
      </nb-accordion-item-header>
      <nb-accordion-item-body>
        <div class="row">
          <div class="col-sm-4 col-md-2">
            <input
              nbInput
              id="search-by-Issue"
              type="text"
              fullWidth
              [(ngModel)]="issueSearchText"
              (ngModelChange)="onIssueSearchChange($event)"
              placeholder="Search"
            />
          </div>
        </div>
        <div class="row">
          <div id="fixed-table" setTableHeight class="col-12 table-responsive mt-3 table-card-view">
            <table class="table table-hover table-bordered" aria-describedby="Site List">
              <thead>
                <tr>
                  <th scope="col" (click)="sortIssue('componentStr', sortIssueOptionList['componentStr'])">
                    <div class="d-flex justify-content-center align-items-center">
                      Component
                      <span
                        class="fa cursor-pointer ms-2"
                        [ngClass]="{
                          'fa-arrow-up': sortIssueOptionList['componentStr'] === 'desc',
                          'fa-arrow-down': sortIssueOptionList['componentStr'] === 'asc',
                          'icon-selected': issueFilterModel.sortBy === 'componentStr'
                        }"
                      ></span>
                    </div>
                  </th>
                  <th scope="col" (click)="sortIssue('issueObservation', sortIssueOptionList['issueObservation'])">
                    <div class="d-flex justify-content-center align-items-center">
                      Issue/Observation
                      <span
                        class="fa cursor-pointer ms-2"
                        [ngClass]="{
                          'fa-arrow-up': sortIssueOptionList['issueObservation'] === 'desc',
                          'fa-arrow-down': sortIssueOptionList['issueObservation'] === 'asc',
                          'icon-selected': issueFilterModel.sortBy === 'issueObservation'
                        }"
                      ></span>
                    </div>
                  </th>
                  <th scope="col" class="text-center">Active</th>
                  <th scope="col" class="text-center action" id="action">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let issue of issueObservationList; let i = index">
                  <td data-title="Component" class="text-center">{{ issue?.componentStr }}</td>
                  <td data-title="Issue Observation" class="text-center">{{ issue?.issueObservation }}</td>
                  <td data-title="Active" class="text-center">
                    <nb-toggle
                      [disabled]="!issue.isComponentActive"
                      (checkedChange)="onActiveInactive($event, issue, ACCORDION_ITEMS.ISSUE)"
                      [(ngModel)]="issue.isActive"
                    ></nb-toggle>
                  </td>
                  <td data-title="Action">
                    <div class="d-md-flex justify-content-center">
                      <a class="px-2 listgrid-icon">
                        <em
                          class="fa-solid fa-copy"
                          nbTooltip="Copy"
                          (click)="openIssuePanel(issue, ADD_EDIT_COPY_MODE.COPY)"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                        ></em>
                      </a>
                      <a class="listgrid-icon px-2" (click)="openIssuePanel(issue, ADD_EDIT_COPY_MODE.EDIT)">
                        <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                      </a>
                      <a class="text-danger px-2 listgrid-icon" (click)="onDelete(issue.id, ACCORDION_ITEMS.ISSUE)">
                        <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                      </a>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="12" *ngIf="!issueObservationList?.length" class="no-record text-center">No Data Found</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </nb-accordion-item-body>
    </nb-accordion-item>
    <nb-accordion-item class="border-bottom mb-2">
      <nb-accordion-item-header class="accordion_head"
        >Action/Recommendation
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          id="ncActionLog"
          class="addStyle"
          (click)="$event.stopPropagation(); openActionPanel(null, ADD_EDIT_COPY_MODE.ADD)"
        >
          Add New
        </button>
      </nb-accordion-item-header>
      <nb-accordion-item-body>
        <div class="row">
          <div class="col-sm-4 col-md-2">
            <input
              nbInput
              id="search-by-action"
              type="text"
              fullWidth
              [(ngModel)]="actionSearchText"
              (ngModelChange)="onActionSearchChange($event)"
              placeholder="Search"
            />
          </div>
        </div>
        <div class="row">
          <div id="fixed-table" setTableHeight class="col-12 table-responsive mt-3 table-card-view">
            <table class="table table-hover table-bordered" aria-describedby="Site List">
              <thead>
                <tr>
                  <th scope="col" (click)="sortActions('componentStr', sortActionOptionList['componentStr'])">
                    <div class="d-flex justify-content-center align-items-center">
                      Component
                      <span
                        class="fa cursor-pointer ms-2"
                        [ngClass]="{
                          'fa-arrow-up': sortActionOptionList['componentStr'] === 'desc',
                          'fa-arrow-down': sortActionOptionList['componentStr'] === 'asc',
                          'icon-selected': actionsFilterModel.sortBy === 'componentStr'
                        }"
                      ></span>
                    </div>
                  </th>
                  <th scope="col" (click)="sortActions('issueObservationStr', sortActionOptionList['issueObservationStr'])">
                    <div class="d-flex justify-content-center align-items-center">
                      Issue/Observation
                      <span
                        class="fa cursor-pointer ms-2"
                        [ngClass]="{
                          'fa-arrow-up': sortActionOptionList['issueObservationStr'] === 'desc',
                          'fa-arrow-down': sortActionOptionList['issueObservationStr'] === 'asc',
                          'icon-selected': actionsFilterModel.sortBy === 'issueObservationStr'
                        }"
                      ></span>
                    </div>
                  </th>
                  <th scope="col" (click)="sortActions('actionRecommendation', sortActionOptionList['actionRecommendation'])">
                    <div class="d-flex justify-content-center align-items-center">
                      Action/Recommendation
                      <span
                        class="fa cursor-pointer ms-2"
                        [ngClass]="{
                          'fa-arrow-up': sortActionOptionList['actionRecommendation'] === 'desc',
                          'fa-arrow-down': sortActionOptionList['actionRecommendation'] === 'asc',
                          'icon-selected': actionsFilterModel.sortBy === 'actionRecommendation'
                        }"
                      ></span>
                    </div>
                  </th>
                  <th scope="col" class="text-center">Active</th>
                  <th scope="col" class="text-center action" id="action">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let action of actionRecommendationList; let i = index">
                  <td data-title="Component" class="text-center">{{ action?.componentStr }}</td>
                  <td data-title="Issue Observation" class="text-center">{{ action?.issueObservationStr }}</td>
                  <td data-title="Action Recommendation" class="text-center">{{ action?.actionRecommendation }}</td>
                  <td data-title="Active" class="text-center">
                    <nb-toggle
                      [disabled]="!action.isIssueActive"
                      (checkedChange)="onActiveInactive($event, action, ACCORDION_ITEMS.ACTION)"
                      [(ngModel)]="action.isActive"
                    ></nb-toggle>
                  </td>
                  <td data-title="Action">
                    <div class="d-md-flex justify-content-center">
                      <a class="px-2 listgrid-icon">
                        <em
                          class="fa-solid fa-copy"
                          nbTooltip="Copy"
                          (click)="openActionPanel(action, ADD_EDIT_COPY_MODE.COPY)"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                        ></em>
                      </a>
                      <a class="listgrid-icon px-2" (click)="openActionPanel(action, ADD_EDIT_COPY_MODE.EDIT)">
                        <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                      </a>
                      <a class="text-danger px-2 listgrid-icon" (click)="onDelete(action.id, ACCORDION_ITEMS.ACTION)">
                        <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                      </a>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="12" *ngIf="!actionRecommendationList?.length" class="no-record text-center">No Data Found</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </nb-accordion-item-body>
    </nb-accordion-item>
  </nb-accordion>
</div>
