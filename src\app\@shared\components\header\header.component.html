<div class="header-container justify-content-between">
  <div class="logo-container me-3 c-pointer pb-1" (click)="navigateHome()"></div>
  <div class="me-3 nav-container d-none d-lg-block">
    <ul class="nav">
      <ng-container *ngFor="let item of menu">
        <li
          [id]="item.id"
          class="nav-item d-flex align-items-center c-pointer"
          *ngIf="item.hasPermission"
          (click)="isActiveTab = item.title; subMenu = item.subMenu; defaultFirstRoute(item)"
          [ngClass]="{
            active: isActiveTab === item.title,
            disabledMenu: !item.subMenu.length,
            'pe-1 ps-1 pe-lg-2 ps-lg-2 ps-xl-3 pe-xl-3 pe-xxl-4 ps-xxl-4': item.hasPermission
          }"
        >
          <img class="me-2 pb-3" [src]="item.icon" alt="QESolar icons" *ngIf="item.hasPermission" />
          <span class="pb-3" *ngIf="item.hasPermission">{{ item.title }}</span>
          <span [id]="item.id + '-coming-soon'" class="badge badge-dark coming-soon" *ngIf="!item.subMenu.length && item.hasPermission"
            >Coming Soon</span
          >
        </li>
      </ng-container>
    </ul>
  </div>
  <div class="d-flex align-items-center">
    <div class="header-upload-icon me-3" *ngIf="chunkFileUpload">
      <img (click)="showChunkuploadProgress()" src="assets/images/chunk-upload.gif" class="cursor-pointer" alt="uploading" width="50px" />
    </div>
    <div *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
      <span *ngIf="!user.checkedInSiteId" (click)="getLocation()" class="header-site-check-in-btn cursor-pointer me-2">Site Check-In</span>
      <span *ngIf="user.checkedInSiteId" (click)="getLocation()" class="header-site-check-out-btn cursor-pointer me-2">Site Check-Out</span>
    </div>
    <div
      class="dropdown logo-dropdown"
      *ngIf="loggedUser.authorities[0] !== authorityRoleString[roleType.CUSTOMER] && loggedUser.allowNotification"
    >
      <a
        class="dropdown-toggle me-4"
        (click)="openNotificationPopUp()"
        role="button"
        id="notification"
        aria-haspopup="true"
        data-bs-toggle="dropdown"
        aria-expanded="false"
      >
        <em class="fa-solid fa-bell fa-lg"></em>
        <span class="position-absolute top-0 translate-middle badge rounded-pill bg-danger" *ngIf="unreadNotificationCount > 0">
          {{ unreadNotificationCount > 999 ? '999+' : unreadNotificationCount }}
        </span>
      </a>
      <div
        #myDropdown
        class="dropdown-menu notification-screen overflow-hidden py-0"
        aria-labelledby="notification"
        [nbSpinner]="NotificationLoading"
        (click)="$event.stopPropagation()"
      >
        <div class="d-flex flex-column h-100 justify-content-between">
          <div>
            <div class="notification-header p-3 bg-light">
              <div class="d-flex align-items-center justify-content-between w-100">
                <div class="d-flex small-expand align-items-center justify-content-between">
                  <p class="mb-2">Notifications</p>
                  <p class="mb-2 expand-icon">
                    <em aria-hidden="true" class="fa fa-expand-alt text-primary cursor-pointer" (click)="expandView()"></em>
                  </p>
                </div>
                <div class="d-flex align-items-center justify-content-center gap-3">
                  <p class="mb-2 d-flex align-items-center justify-content-between gap-3 text-nowrap">
                    <label class="label mb-0 align-items-center justify-content-between gap-2 d-flex" for="customer">
                      Only show unread
                      <nb-toggle
                        [(checked)]="notificationParams.showUnreadOnly"
                        labelPosition="start"
                        (checkedChange)="refreshListOnToggleChange()"
                      ></nb-toggle>
                    </label>
                  </p>
                  <p class="mb-2 small-hidden-icon">
                    <em aria-hidden="true" class="fa fa-expand-alt text-primary cursor-pointer" (click)="expandView()"></em>
                  </p>
                </div>
              </div>
              <p *ngIf="isAnyCheckboxChecked" class="mb-0 text-end mark-read-link">
                <a href="javascript: void(0);" class="pe-auto mark-all" (click)="markSelectedAsRead()">Mark selected as read</a>
              </p>
            </div>
            <div class="list-notification">
              <ul class="list-group list-group-flush">
                <li
                  class="list-group-item"
                  *ngFor="
                    let item of notificationsList
                      | paginate
                        : {
                            itemsPerPage: notificationParams.itemsCount,
                            currentPage: currentPage,
                            totalItems: total
                          };
                    let i = index
                  "
                >
                  <div class="d-flex align-items-center">
                    <div
                      *ngIf="item.triggerId !== 18 && item.triggerId !== 19 && item.triggerId !== 20"
                      class="user_info me-2"
                      [ngStyle]="{
                        'background-color': item.profileBackgroundColor ? item.profileBackgroundColor : profileRandomBgColor,
                        color: item.profileBackgroundColor
                          ? getInitialsColorForBgColor(item.profileBackgroundColor)
                          : getInitialsColorForBgColor(profileRandomBgColor)
                      }"
                    >
                      <span>
                        <sfl-user-avatar
                          [photoUrl]="item.profileImage"
                          [width]="'40px'"
                          [height]="'40px'"
                          [initials]="getInitials(item.user)"
                        ></sfl-user-avatar>
                      </span>
                    </div>
                    <div class="w-100 me-2">
                      <div class="d-flex align-items-center justify-content-between">
                        <a href="javascript: void(0);" class="mb-1 fw-bold pe-auto" (click)="navigateToEntity(item)">{{ item.title }}</a>
                        <p class="mb-1 ms-2">
                          <nb-checkbox
                            [(ngModel)]="item.isUnread"
                            (change)="updateCheckboxStatus(item.userNotificationId, item.isRead)"
                          ></nb-checkbox>
                        </p>
                      </div>
                      <p class="mb-1 description">{{ item.description }}</p>
                      <div class="d-flex align-items-center justify-content-between mt-2">
                        <p class="mb-1 me-3 user_date">{{ item.user || '' }}</p>
                        <p class="mb-1 details">
                          {{ item.notificationDate | date : dateTimeFormat }}
                          <button
                            *ngIf="item?.auditActionId > 0"
                            class="btn more-details-btn"
                            nbTooltip="{{ item.isMoreDetails ? 'Show less' : 'Show more' }}"
                            nbTooltipPlacement="top"
                            nbTooltipStatus="primary"
                            (click)="toggleMoreDetails(item.auditActionId, item.isMoreDetails)"
                          >
                            <em [ngClass]="item.isMoreDetails ? 'fa-solid fa-caret-up' : 'fa-solid fa-caret-down'"></em>
                          </button>
                        </p>
                      </div>
                      <div>
                        <ng-container *ngIf="notificationActions?.length && item.isMoreDetails">
                          <div class="action-summary">
                            <div class="row auditActionSummary pb-1">
                              <div class="col-2"><label class="label mb-0">Field</label></div>
                              <div class="col-5 text-center"><label class="label mb-0">Original Value</label></div>
                              <div class="col-5 text-center"><label class="label mb-0">New Value</label></div>
                            </div>
                            <hr class="dropdown-divider" />
                            <div class="mt-1" *ngFor="let actionSummary of notificationActions">
                              <div class="row">
                                <div class="col-2">{{ actionSummary?.Label }}</div>
                                <div class="col-5 text-center">{{ actionSummary?.Value?.Old || '-' }}</div>
                                <div class="col-5 text-center">{{ actionSummary?.Value?.New || '-' }}</div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container class="auditActionSummary" *ngIf="!notificationActions?.length && item.isMoreDetails">
                          <div class="d-flex align-items-center justify-content-center">
                            <p>No Field Updated</p>
                          </div>
                        </ng-container>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
              <div *ngIf="!notificationsList?.length" class="d-flex align-items-center justify-content-center mt-4">
                <p>No Data Found</p>
              </div>
            </div>
          </div>
          <div
            class="modal-footer pagination-footer mt-2 d-flex align-items-center justify-content-between bg-light p-3"
            *ngIf="notificationsList?.length"
          >
            <div class="d-flex align-items-center">
              <label class="mb-0">Items per page: </label>
              <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()">
                <ng-option value="5">5</ng-option>
                <ng-option value="10">10</ng-option>
                <ng-option value="50">50</ng-option>
                <ng-option value="100">100</ng-option>
              </ng-select>
              <strong class="ms-md-3">Total: {{ total }}</strong>
            </div>
            <div class="ms-md-auto ms-sm-0">
              <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pb-1 dropdown logo-dropdown d-none d-lg-block">
      <a
        id="userMenu"
        class="userMenu dropdown-toggle"
        role="button"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        [ngStyle]="{
          'background-color': loggedUser.profileBackgroundColor ? loggedUser.profileBackgroundColor : profileRandomBgColor,
          color: loggedUser.profileBackgroundColor
            ? getInitialsColorForBgColor(loggedUser.profileBackgroundColor)
            : getInitialsColorForBgColor(profileRandomBgColor)
        }"
      >
        <span>
          <sfl-user-avatar
            [photoUrl]="loggedUser.profileImage"
            [width]="'40px'"
            [height]="'40px'"
            [initials]="getInitials(loggedUser.firstName + ' ' + loggedUser.lastName)"
          ></sfl-user-avatar>
        </span>
      </a>
      <div class="dropdown-menu" aria-labelledby="userMenu">
        <div class="dropdown-item d-flex align-items-center">
          <span class="pe-3">Light</span>
          <nb-toggle id="theme-toggler" status="primary" [(checked)]="darkThemeActive" (checkedChange)="changeTheme()"></nb-toggle>
          <span class="ps-1">Dark</span>
        </div>
        <a
          class="dropdown-item"
          [id]="item.id"
          *ngFor="let item of userMenu"
          (click)="onItemSelection(item.title)"
          [routerLink]="[item.link]"
        >
          {{ item.title }}
        </a>
      </div>
    </div>
    <div class="pb-1 dropdown d-lg-none">
      <a id="userMenu" class="userMenu" (click)="openSidebar()">
        {{ getFirstLetter(user.firstName) }}
      </a>
    </div>
  </div>
</div>
<div *ngIf="environment !== 'prod'" class="env-wrapper">
  <div class="env-identifier" [ngClass]="environment">
    <span>{{ environment }}</span>
  </div>
</div>
<div class="sub-header-container justify-content-center row d-none d-lg-block">
  <div class="nav-container d-lg-flex" *ngIf="subMenu.length">
    <ul class="nav w-100">
      <ng-container *ngFor="let item of subMenu">
        <li
          [id]="item.id"
          class="nav-item d-flex align-items-center c-pointer"
          *ngIf="item.hasPermission"
          (click)="isActiveSubTab = item.title"
          [ngClass]="{
            disabledMenu: !item.route,
            'pe-1 ps-1 pe-lg-2 ps-lg-2 ps-xl-2 pe-xl-2 ps-xxl-3 pe-xxl-3 ': item.hasPermission,
            zIndex: item.subMenu.length
          }"
        >
          <div *ngIf="!item.subMenu.length">
            <a class="pb-3" *ngIf="item.route && item.hasPermission" [routerLink]="[item.route]" [routerLinkActive]="['activeSubTab']">
              {{ item.title }}
            </a>
            <span *ngIf="!item.route && item.hasPermission">{{ item.title }} </span>
            <span [id]="item.id + '-coming-soon'" class="coming-soon ms-1" *ngIf="!item.route && item.hasPermission">(Coming Soon)</span>
          </div>
          <div class="dropdown" *ngIf="item.subMenu.length">
            <a
              class="pb-3 dropdown-toggle"
              role="button"
              data-bs-toggle="dropdown"
              aria-expanded="false"
              aria-haspopup="true"
              *ngIf="item.route && item.hasPermission"
              [ngClass]="{ activeSubTab: getClass(item.route) }"
            >
              {{ item.title }}
            </a>
            <span *ngIf="!item.route && item.hasPermission">{{ item.title }} </span>
            <span [id]="item.id + '-coming-soon'" class="coming-soon ms-1" *ngIf="!item.route && item.hasPermission">(Coming Soon)</span>
            <div class="pt-0 pb-0 dropdown-menu" aria-labelledby="dropdownMenuButton">
              <ng-container *ngFor="let menu of item.subMenu">
                <a
                  [id]="menu.id"
                  class="dropdown-item"
                  *ngIf="menu.route && menu.hasPermission"
                  [routerLink]="[menu.route]"
                  [routerLinkActive]="['activeSubTab']"
                  href="#"
                >
                  {{ menu.title }}
                </a>
              </ng-container>
            </div>
          </div>
        </li>
      </ng-container>
    </ul>
    <ng-container
      *ngIf="jumpToMenuConfig.isVisible && jumpToMenuConfig.jumpToMenuList && jumpToMenuConfig.qeMenuEnum !== qeMenuEnums.QE_NO_MENU"
    >
      <div class="px-2 d-none d-lg-flex align-items-center">
        <button
          nbButton
          status="primary"
          size="small"
          [nbPopover]="templateRef"
          nbPopoverPlacement="bottom"
          nbPopoverTrigger="click"
          nbPopoverClass="jump-to-menu-overlay"
          nbPopoverAdjustment="noop"
          [disabled]="jumpToMenuConfig.disable"
          #jumpToMenuPopover="nbPopover"
          (click)="onJumpToMenuPopoverOpen(jumpToMenuPopover)"
        >
          JUMP TO:
        </button>
      </div>
    </ng-container>
  </div>
</div>
<p-sidebar [(visible)]="showSidebar" position="right">
  <div>
    <p-panelMenu [model]="menuItems"></p-panelMenu>
    <div class="d-flex align-items-center mt-2">
      <span class="pe-3">Light</span>
      <nb-toggle id="theme-toggler" status="primary" [(checked)]="darkThemeActive" (checkedChange)="changeTheme()"></nb-toggle>
      <span class="ps-1">Dark</span>
    </div>
  </div>
</p-sidebar>

<ng-template #templateRef>
  <div class="jump-to-section-wrapper d-none d-lg-block">
    <div class="p-2 pt-1 w-100">
      <input
        id="jump-to-menu-search"
        class="form-control search-textbox sfl-track-input"
        placeholder="Search"
        type="text"
        name="search"
        autocomplete="off"
        [(ngModel)]="jumpToMenuConfig.search"
        (ngModelChange)="onJumptoMenuSearchChanged()"
      />
    </div>
    <ng-container *ngIf="jumpToMenuConfig.filteredJumpToMenuList.length; else noMenuFound">
      <ng-container *ngFor="let item of jumpToMenuConfig.filteredJumpToMenuList">
        <ul class="nav my-0 px-2 py-1 w-100">
          <ng-container *ngIf="item.hasPermission">
            <div class="w-100">
              <div class="d-flex align-items-center">
                <img class="me-2" [src]="item.icon" alt="QESolar icons" height="18" width="18" />
                <span [id]="item.id">{{ item.title }}</span>
              </div>
              <ng-container *ngIf="item.subMenu.length">
                <ng-container *ngFor="let menu of item.subMenu">
                  <ng-container *ngIf="menu.route && menu.hasPermission">
                    <li class="nav-item w-100 p-1 ps-4">
                      <a
                        class="nav-item-anchor ms-2"
                        [id]="menu.id"
                        [routerLink]="menu.route"
                        [queryParams]="menu.queryParams"
                        [routerLinkActive]="['activeJumpToMenu']"
                        (click)="onJumpToMenuClick()"
                      >
                        {{ menu.title }}
                      </a>
                    </li>
                  </ng-container>
                </ng-container>
              </ng-container>
            </div>
          </ng-container>
        </ul>
      </ng-container>
    </ng-container>
    <ng-template #noMenuFound>
      <div class="w-100 text-center">No Menu Found</div>
    </ng-template>
  </div>
</ng-template>
