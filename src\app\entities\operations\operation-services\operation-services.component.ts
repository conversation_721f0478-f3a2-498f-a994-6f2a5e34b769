import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { ErrorMessages } from '../../../@shared/constants';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { AddEditOperationServicesComponent } from './add-edit-operation-services/add-edit-operation-services.component';
import { ServicesList } from './operation-services.modal';
import { OperationServicesService } from './operation-services.service';
@Component({
  selector: 'sfl-operation-services',
  templateUrl: './operation-services.component.html',
  styleUrls: ['./operation-services.component.scss']
})
export class OperationServicesComponent implements OnInit {
  loading = false;
  subscription: Subscription = new Subscription();
  servicesLists: ServicesList[] = [];
  modalRef: BsModalRef;
  userRoles: string;
  constructor(
    private readonly operationServices: OperationServicesService,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.getServicesList();
  }

  getServicesList() {
    this.loading = true;
    this.subscription.add(
      this.operationServices.getServicesLists().subscribe({
        next: (res: ServicesList[]) => {
          this.servicesLists = res;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  openAddEditServicesModel(mode, id = null) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        serviceId: id,
        mode: mode
      }
    };
    this.modalRef = this.modalService.show(AddEditOperationServicesComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      if (res) {
        this.loading = false;

        this.getServicesList();
      }
    });
  }

  navigateToAddService() {
    this.router.navigate(['entities/operations/services/add']);
  }

  onDelete(serviceId: string, isServiceInUse: boolean) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: isServiceInUse
          ? `This service is used in ticket activity, ${ErrorMessages.cannotDeleteServiceInuse}`
          : ErrorMessages.deleteMessage,
        showConfirmButton: isServiceInUse ? false : true,
        cancelBtnText: isServiceInUse ? 'Okay' : 'Cancel'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);

    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.deleteService(serviceId);
      }
    });
  }

  deleteService(serviceId: string) {
    this.loading = true;
    this.subscription.add(
      this.operationServices.deleteServices(serviceId).subscribe({
        next: () => {
          this.loading = false;
          this.alertService.showSuccessToast(`Service Deleted Successfully`);
          this.getServicesList();
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }
}
