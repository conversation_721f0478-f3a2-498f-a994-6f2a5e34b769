import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NbMenuService } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { AppConstants } from '../../../@shared/constants';
import { ROLE_TYPE } from '../../../@shared/enums';
import {
  AutomationDeviceList,
  BULK_SITE_ACTIONS_ENUMS,
  BulkSiteAction,
  SiteAutomationDevicesList
} from '../../../@shared/models/site.model';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { checkAuthorisations } from '../../../@shared/utils';
import { DeviceFetchStatus, FetchDevice } from '../../availability/data-table/data-table-model';
import { AvailabilityDataTableService } from '../../availability/data-table/data-table.service';
import { AddSiteDevice, ImportSiteDevice } from '../../site-device/site-device.model';
import { SiteDeviceService } from '../../site-device/site-device.service';
import { ViewDeviceDetailsComponent } from '../view-device-details/view-device-details.component';

@Component({
  selector: 'sfl-automation-site-device-list',
  templateUrl: './automation-site-device-list.component.html',
  styleUrls: ['./automation-site-device-list.component.scss']
})
export class AutomationSiteDeviceListComponent implements OnInit, OnDestroy {
  @ViewChild('uploadSiteAutomationDevicesFileInput') uploadSiteAutomationDevicesFileInput;
  @Input() siteId: number;
  @Input() customerId: number;
  @Input() portfolioId: number;
  @Input() automationDataSourceId: number;
  @Input() automationSiteDetailId: string;
  @Output() loading: EventEmitter<boolean> = new EventEmitter<boolean>();
  subscription: Subscription = new Subscription();
  automationDeviceList: AutomationDeviceList[] = [];
  listOfPlottingUnit = [];
  listOfReportingUnit = [];
  modalRef: BsModalRef;
  missingDCLoadCount: number;
  viewMode: boolean = true;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;
  bulkSiteAutomationDevicesItems: BulkSiteAction[] = [new BulkSiteAction(BULK_SITE_ACTIONS_ENUMS.EXPORT_SITE_AUTHOMATION_DEVICES)];
  bulkSiteAutomationDevicesMenuTag = 'bulkSiteAutomationDevicesMenu';
  isReFetching: boolean = false;
  fetchingDevice = [];

  constructor(
    public readonly siteDeviceService: SiteDeviceService,
    private readonly alertService: AlertService,
    private readonly router: Router,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly nbMenuService: NbMenuService,
    private readonly availabilityDataTableService: AvailabilityDataTableService,
    public datePipe: DatePipe
  ) {}

  ngOnInit(): void {
    this.subscription.add(
      this.nbMenuService.onItemClick().subscribe(event => {
        if (event.tag === this.bulkSiteAutomationDevicesMenuTag) {
          this.onBulkSiteAutomationDevicesSelection(event.item as BulkSiteAction);
        }
      })
    );

    if (this.siteId) {
      this.getSiteAutomationDeviceList();
    }
  }

  getSiteAutomationDeviceList() {
    this.loading.emit(true);
    this.subscription.add(
      this.siteDeviceService.getSiteAutomationDeviceList(this.siteId).subscribe({
        next: (res: SiteAutomationDevicesList) => {
          this.automationDeviceList = res.siteDevices;
          this.missingDCLoadCount = res.invDCNullCount + res.meterDCNullCount;
          this.setDevicePlottingReportingUnit();
          setTimeout(() => {
            this.loading.emit(false);
          }, 0);
        },
        error: _e => {
          setTimeout(() => {
            this.loading.emit(false);
          }, 0);
        }
      })
    );
  }

  setDevicePlottingReportingUnit(): void {
    this.automationDeviceList.forEach((value: AutomationDeviceList, index) => {
      if (!this.automationDeviceList[index].reportingUnitId) {
        this.automationDeviceList[index].reportingUnitId = value.unitModel.defaultReportingUnit;
      }
      if (!this.automationDeviceList[index].plottingUnitId) {
        this.automationDeviceList[index].plottingUnitId = value.unitModel.defaultPlottingUnit;
      }
      this.listOfPlottingUnit[index] = value.unitModel.listOfPlottingUnit;
      this.listOfReportingUnit[index] = value.unitModel.listOfReportingUnit;
    });
  }

  onBulkSiteAutomationDevicesMenuOpen(): void {
    const exportSiteAutomationDevicesMenuItem = new BulkSiteAction(BULK_SITE_ACTIONS_ENUMS.EXPORT_SITE_AUTHOMATION_DEVICES);
    const uploadSiteAutomationDevicesUpdateMenuItem = new BulkSiteAction(
      BULK_SITE_ACTIONS_ENUMS.UPLOAD_SITE_AUTHOMATION_DEVICE_FOR_UPDATES
    );
    this.bulkSiteAutomationDevicesItems = [exportSiteAutomationDevicesMenuItem, uploadSiteAutomationDevicesUpdateMenuItem];
  }

  onBulkSiteAutomationDevicesSelection(item: BulkSiteAction): void {
    if (item.id === BULK_SITE_ACTIONS_ENUMS.EXPORT_SITE_AUTHOMATION_DEVICES) {
      this.exportSiteAutomationDevices();
    } else if (item.id === BULK_SITE_ACTIONS_ENUMS.UPLOAD_SITE_AUTHOMATION_DEVICE_FOR_UPDATES) {
      this.uploadSiteAutomationDevices();
    }
  }

  exportSiteAutomationDevices(): void {
    const exportDevicesParams = {
      siteId: this.siteId
    };
    this.loading.emit(true);
    this.subscription.add(
      this.siteDeviceService.exportSiteAutomationDevices(exportDevicesParams).subscribe({
        next: (res: Blob) => {
          const link = this.commonService.createObject(res, 'application/vnd.ms-excel');
          link.download = `Automation_Site_Devices.xlsx`;
          link.click();
          this.loading.emit(false);
        },
        error: _ => {
          this.loading.emit(false);
        }
      })
    );
  }

  uploadSiteAutomationDevices(): void {
    this.uploadSiteAutomationDevicesFileInput.nativeElement.click();
  }

  uploadSiteAutomationDeviceFile(files: File[]): void {
    if (files.length > 0) {
      this.loading.emit(true);
      const formData = new FormData();
      formData.append('siteId', this.siteId.toString());
      formData.append('uploadedFile', files[0] as File);
      this.subscription.add(
        this.siteDeviceService.uploadSiteAutomationDevices(formData).subscribe({
          next: (res: { message: string; file: string }) => {
            if (res.file) {
              const fileBlob = this.commonService.createObjectFromBased64(res.file, 'application/vnd.ms-excel');
              const link = this.commonService.createObject(fileBlob, 'application/vnd.ms-excel');
              link.download = `Automation_Site_Import_Error.xlsx`;
              link.click();
              this.alertService.showErrorToast(res.message);
            } else {
              this.alertService.showSuccessToast(res.message);
            }

            this.loading.emit(false);
            this.uploadSiteAutomationDevicesFileInput && this.uploadSiteAutomationDevicesFileInput?.nativeElement
              ? (this.uploadSiteAutomationDevicesFileInput.nativeElement.value = '')
              : null;

            if (this.siteId) {
              this.getSiteAutomationDeviceList();
            }
          },
          error: () => {
            this.loading.emit(false);
            this.uploadSiteAutomationDevicesFileInput && this.uploadSiteAutomationDevicesFileInput?.nativeElement
              ? (this.uploadSiteAutomationDevicesFileInput.nativeElement.value = '')
              : null;
          }
        })
      );
    }
  }

  getDataByDeviceType() {
    for (const [index, value] of this.automationDeviceList.entries()) {
      setTimeout(() => {
        this.getDevicePlottingReporting(value.deviceTypeId, value.automationSiteDetailId, this.siteId, index);
      }, 0);
    }
  }

  getDevicePlottingReporting(id, automationSiteDetailId, siteId: number, index: number) {
    this.subscription.add(
      this.siteDeviceService.getDevicePlottingReporting(siteId, id, automationSiteDetailId).subscribe({
        next: (res: any) => {
          if (!this.automationDeviceList[index].reportingUnitId) {
            this.automationDeviceList[index].reportingUnitId = res.defaultReportingUnit;
          }
          if (!this.automationDeviceList[index].plottingUnitId) {
            this.automationDeviceList[index].plottingUnitId = res.defaultPlottingUnit;
          }
          this.listOfPlottingUnit[index] = res.listOfPlottingUnit;
          this.listOfReportingUnit[index] = res.listOfReportingUnit;
        }
      })
    );
  }

  gotoEditableDevice() {
    this.viewMode = false;
  }

  updateAutomationDeviceLIst() {
    this.loading.emit(true);
    this.subscription.add(
      this.siteDeviceService.updateSiteAutomationDeviceList(this.automationDeviceList).subscribe({
        next: (_res: any) => {
          const updatedDcLoad = this.automationDeviceList.filter(
            item =>
              (item.dcLoad === '' || item.dcLoad === '-' || item.dcLoad === null) &&
              (item.deviceType === 'Inverter' || item.deviceType === 'Meter')
          );
          this.missingDCLoadCount = updatedDcLoad.length;
          this.loading.emit(false);
          this.alertService.showSuccessToast('Records update successfully.');
          this.viewMode = true;
        },
        error: _e => {
          this.loading.emit(false);
        }
      })
    );
  }

  Cancel() {
    this.viewMode = true;
  }

  redirectToDeviceAdd() {
    const prePopulateData: AddSiteDevice = new AddSiteDevice();
    prePopulateData.customerId = this.customerId;
    prePopulateData.portfolioId = this.portfolioId;
    prePopulateData.siteId = this.siteId;
    this.siteDeviceService.setAddDevicePrePopulateData(prePopulateData);
    this.router.navigateByUrl('/entities/site-device/add');
  }

  redirectToDeviceImport() {
    const prePopulateData: ImportSiteDevice = new ImportSiteDevice();
    prePopulateData.automationDataSourceId = this.automationDataSourceId;
    prePopulateData.automationSiteDetailId = this.automationSiteDetailId;
    this.siteDeviceService.setImportDevicePrePopulateData(prePopulateData);
    this.router.navigateByUrl('/entities/site-device/import');
  }

  viewSiteDeviceDetails(id: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        deviceId: id
      }
    };
    this.modalRef = this.modalService.show(ViewDeviceDetailsComponent, ngModalOptions);
  }

  reFetchValueCell(): void {
    this.fetchingDevice = this.automationDeviceList.filter(device => device.hardwareId).map(device => device.hardwareId);
    if (this.fetchingDevice.length > 0) {
      this.isReFetching = true;
      this.subscription.add(
        this.siteDeviceService.getLatestDeviceValues(this.fetchingDevice, this.customerId, this.siteId).subscribe({
          next: (response: any) => {
            if (response.status === 500) {
              this.alertService.showErrorToast(response.message);
            } else {
              this.alertService.showSuccessToast(response.message);
              this.reFetchDeviceInfo();
            }
          },
          error: error => {
            this.alertService.showErrorToast('Fail to Re-Fetching Data.');
          }
        })
      );
    }
  }

  reFetchDeviceInfo(first: number = 1, second: number = 2) {
    if (this.fetchingDevice.length) {
      let time: number = first + second;
      time = time > 8 ? 8 : time;
      if (time >= 2) {
        setTimeout(() => {
          this.checkFetchDevice(second, time);
        }, time * 1000);
      } else {
        this.reFetchDeviceInfo(second, time);
      }
    }
  }

  checkFetchDevice(fibosCountFirst: number = 1, fibosCountSecond: number = 2) {
    const data: FetchDevice = new FetchDevice();
    data.customerId = Number(this.customerId);
    data.siteId = Number(this.siteId);
    data.datePerformed = this.datePipe.transform(new Date(), AppConstants.fullDateFormat);
    data.hardwareIds = this.fetchingDevice;
    this.subscription.add(
      this.availabilityDataTableService.refetchDataTablesByDevice(data).subscribe({
        next: (res: DeviceFetchStatus) => {
          if (res.fetched.length) {
            this.isReFetching = false;
            this.getSiteAutomationDeviceList();
          }
          if (res.fetching.length) {
            this.fetchingDevice = res.fetching;
            this.isReFetching = true;
            this.reFetchDeviceInfo(fibosCountFirst, fibosCountSecond);
          } else {
            this.fetchingDevice = [];
          }
        }
      })
    );
  }
  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
