<div class="modal-content" id="activitylogticket" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header modal-fixed-header">
    <div class="d-flex align-items-center w-100">
      <h6>{{ issueMode + ' ISSUE/OBSERVATION' }}</h6>
      <div class="ms-auto">
        <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
          <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
        </button>
      </div>
    </div>
  </div>
  <form
    name="issueItemForm"
    #issueItemForm="ngForm"
    aria-labelledby="title"
    autocomplete="off"
    class="issue-form"
    (ngSubmit)="issueItemForm?.form?.valid && onAddEditIssue()"
  >
    <div class="modal-body">
      <div class="row">
        <div class="col-12 mb-2">
          <label class="label" for="selectedComponent"> Component <span class="ms-1 text-danger">*</span> </label>
          <div>
            <ng-select
              class="model-dd"
              name="selectedComponent"
              id="selectedComponent"
              [items]="componentList"
              bindLabel="name"
              bindValue="id"
              notFoundText="No Component Found"
              placeholder="Select Component"
              #selectedComponent="ngModel"
              [(ngModel)]="issueItem.componentId"
              required
            >
            </ng-select>
            <div class="error-message-section">
              <sfl-error-msg
                [control]="selectedComponent"
                [isFormSubmitted]="issueItemForm?.submitted"
                fieldName="Component"
              ></sfl-error-msg>
            </div>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="label" for="issueObservation">Issue Observation <span class="ms-1 text-danger">*</span></label>
        <textarea
          nbInput
          fullWidth
          name="issueObservation"
          id="input-issueObservation"
          #issueObservation="ngModel"
          [(ngModel)]="issueItem.issueObservation"
          maxlength="5120"
          required
        >
        </textarea>
        <sfl-error-msg
          [control]="issueObservation"
          [isFormSubmitted]="issueItemForm?.submitted"
          fieldName="Issue Observation"
        ></sfl-error-msg>
      </div>
    </div>
    <div class="modal-footer">
      <button nbButton type="button" status="basic" size="medium" (click)="onCancel()">Cancel</button>
      <button nbButton status="primary" size="medium" type="submit" id="closeSubmit">Save</button>
    </div>
  </form>
</div>
