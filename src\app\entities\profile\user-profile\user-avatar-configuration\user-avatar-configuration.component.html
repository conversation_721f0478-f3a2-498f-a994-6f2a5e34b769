<div class="alert-box">
  <div class="modal-header">
    <h6 class="modal-title ModalBody">{{ isImagesChanges ? 'Change profile photo' : 'Edit Initials Background' }}</h6>
  </div>
  <div class="modal-body">
    <ng-container *ngIf="!isImagesChanges; else changeProfileImg">
      <div class="container">
        <div class="row">
          <div class="col-12 col-md-4">
            <div
              class="avatar-initials"
              [ngStyle]="{
                'background-color': profileBackgroundColor ? profileBackgroundColor : initialsBgColors[0].bgColor,
                color: profileBackgroundColor
                  ? getInitialsColorForBgColor(profileBackgroundColor)
                  : getInitialsColorForBgColor(initialsBgColors[0].color)
              }"
            >
              <span>{{ initials }}</span>
            </div>
          </div>
          <div class="col-12 col-md-8 pe-0">
            <span>Background color </span>
            <div class="d-flex align-items-center my-3">
              <div class="mx-1 color-box" *ngFor="let initials of initialsBgColors; let i = index">
                <button class="btn bg-colors" [ngStyle]="{ 'background-color': initials.bgColor }" (click)="getSelectedColor(i)"></button>
                <em class="fa-solid fa-check check-icon" [ngClass]="{ 'd-none': !initials.isSelected }"></em>
              </div>
            </div>
            <div class="warning-msg">
              <span>This replaces your current profile picture.</span>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-template #changeProfileImg>
      <div class="row">
        <div class="col-12 col-md-4 d-flex align-items-center justify-content-center">
          <div class="avatar-initials" *ngIf="isFileUploaded">
            <img
              *ngIf="avatarParams.imageSrc"
              [src]="avatarParams.imageSrc ? avatarParams.imageSrc : 'assets/images/no-image-found.jpg'"
              class="avatar-image"
              alt="avatar photo"
            />
          </div>
        </div>
        <div class="col-12" [ngClass]="{ 'col-md-8': isFileUploaded }">
          <div *ngIf="!isFileUploaded" class="dropZone" ngFileDragDrop (fileDropped)="getUploadedFiles($event)">
            <input type="file" accept="image/*" #fileDropRef id="fileDropRef" (change)="getUploadedFiles($event)" />
            <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
            <h5 class="fw-bold">Drag & Drop files to attach</h5>
            <label style="text-transform: none" class="fw-bold">or Browse to choose a file </label>
          </div>
          <div *ngIf="isFileUploaded" class="positon-relative">
            <image-cropper
              [imageChangedEvent]="avatarParams.photoFile"
              [maintainAspectRatio]="true"
              [aspectRatio]="4 / 3"
              format="png"
              (imageCropped)="imageCropped($event)"
            ></image-cropper>
            <button type="button" class="close change-image" aria-label="Close" (click)="changeImage()">
              <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
            </button>
          </div>
        </div>
      </div>
    </ng-template>
  </div>

  <div class="modal-footer ModalFooter">
    <button nbButton size="small" (click)="_bsModalRef.hide()">Cancel</button>
    <button nbButton status="primary" size="small" (click)="onConfirm()">Update</button>
  </div>
</div>
