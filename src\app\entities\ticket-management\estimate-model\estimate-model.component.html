<div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <div class="d-flex align-items-center w-100">
      <h6>{{ mode === 'create' ? 'Create' : 'Edit' }} Estimate</h6>
      <div class="ms-auto">
        <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
          <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
        </button>
      </div>
    </div>
  </div>
  <div class="modal-body ModalBody rma-detail-modal-body">
    <div>
      <form [formGroup]="addUpdateEstimateForm">
        <div class="row">
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Estimate Number<span class="ms-1 text-danger">*</span></label>
            <input
              nbInput
              type="text"
              fullWidth
              formControlName="estNumber"
              class="form-control mfg-name"
              [attr.disabled]="isEditableByQEUsers()"
              placeholder="Enter Estimate Number"
            />
            <sfl-error-msg [control]="addUpdateEstimateForm?.controls?.estNumber" fieldName="Estimate Number"></sfl-error-msg>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-Portfolio">Additional Hours</label>
            <input
              nbInput
              type="text"
              fullWidth
              formControlName="additionalHours"
              class="form-control mfg-name"
              [mask]="maskDecimal"
              [attr.disabled]="isEditableByQEUsers()"
              placeholder="Enter Additional Hours"
              [attr.maxLength]="3"
            />
            <sfl-error-msg [control]="addUpdateEstimateForm?.controls?.additionalHours" fieldName="Additional Hours"></sfl-error-msg>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Estimate Total<span class="ms-1 text-danger">*</span></label>
            <input
              nbInput
              type="text"
              fullWidth
              formControlName="estimateTotal"
              class="form-control mfg-name"
              [mask]="maskDecimal"
              [attr.disabled]="isEditableByQEUsers()"
              thousandSeparator=","
              placeholder="Enter Estimate Total"
              [attr.maxLength]="15"
            />
            <sfl-error-msg [control]="addUpdateEstimateForm?.controls?.estimateTotal" fieldName="Estimate Total"></sfl-error-msg>
          </div>
          <div class="col-12 col-md-6 mb-3" *ngIf="returnTrackingFiles.length < 1">
            <div class="dropZone" ngFileDragDrop (fileDropped)="getUploadedFiles($event, false)">
              <input
                type="file"
                [attr.disabled]="isEditableByQEUsers()"
                #file
                (change)="getUploadedFiles($event.target.files, false)"
                accept="application/pdf"
              />
              <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
              <h5 class="fw-bold">Drag & Drop file to attach</h5>
              <label style="text-transform: none" class="fw-bold">or Browse to choose a file </label>
            </div>
          </div>
          <div class="col-12 col-md-6 mt-3" *ngIf="returnTrackingFiles.length > 0">
            <ng-container>
              <div class="d-flex mt-2 image-container" *ngFor="let files of returnTrackingFiles; let i = index">
                <ng-container>
                  <label
                    class="imageFilename cursor-pointer"
                    nbTooltip="{{ files?.fileName }}"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                  >
                    <a *ngIf="files?.documentUrl" [href]="files.documentUrl" target="_blank" [innerHTML]="files?.fileName"></a>
                    <span *ngIf="!files?.documentUrl">{{ files?.fileName }}</span>
                  </label>
                  <span class="cursor-pointer">
                    <em
                      (click)="deleteUploadedAttachment(i, files)"
                      nbtooltip="Delete"
                      nbtooltipplacement="top"
                      nbtooltipstatus="text-danger"
                      aria-hidden="true"
                      *ngIf="!isEditableByQEUsers()"
                      class="fa fa-times-circle text-danger ps-2"
                    ></em>
                  </span>
                </ng-container>
              </div>
            </ng-container>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Status<span class="ms-1 text-danger">*</span></label>
            <div *ngIf="isStatusFiledDisabled() || mode === 'create'; else estimateStatusEditable">
              <span>{{ getStatusLabel(addUpdateEstimateForm.value.estimateStatus) }} </span>
            </div>
            <ng-template #estimateStatusEditable>
              <ng-select
                name="TruckRollId"
                [items]="statusDropDownList"
                bindLabel="name"
                bindValue="id"
                formControlName="estimateStatus"
                notFoundText="No status Found"
                placeholder="Select status"
                [closeOnSelect]="true"
                [clearable]="false"
              >
              </ng-select>
              <sfl-error-msg [control]="addUpdateEstimateForm?.controls?.estimateStatus" fieldName="Status"></sfl-error-msg>
            </ng-template>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-Portfolio">Customer PO</label>
            <div *ngIf="isUserCustomerOrCamPlus(userRole) && addUpdateEstimateForm.value.estimateStatus === 3; else customerPoNotEditable">
              <input
                nbInput
                type="text"
                fullWidth
                formControlName="customerPO"
                class="form-control mfg-name"
                placeholder="Enter customer PO"
              />
              <sfl-error-msg [control]="addUpdateEstimateForm?.controls?.customerPO" fieldName="Customer PO"></sfl-error-msg>
            </div>
            <ng-template #customerPoNotEditable>
              <ng-container
                *ngIf="addUpdateEstimateForm.value.estimateStatus === 1 || addUpdateEstimateForm.value.estimateStatus === 2; else qesolar"
              >
                <p>-</p>
              </ng-container>
              <ng-template #qesolar>
                <p>{{ addUpdateEstimateForm.value.customerPO || '-' }}</p>
              </ng-template>
            </ng-template>
          </div>
        </div>
        <div class="row">
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-Portfolio">Approved By</label>
            <p>{{ addUpdateEstimateForm.value.approvedByName || '-' }}</p>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-Portfolio">Approved On</label>
            <p>{{ (addUpdateEstimateForm.value.approvedOn | date : dateFormat) || '-' }}</p>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer ModalFooter">
      <button nbButton status="basic" size="medium" type="button" (click)="onCancel()">Cancel</button>
      <button nbButton status="primary" size="medium" id="deviceSubmit" type="submit" (click)="onConfirm()">Save</button>
    </div>
  </div>
</div>
