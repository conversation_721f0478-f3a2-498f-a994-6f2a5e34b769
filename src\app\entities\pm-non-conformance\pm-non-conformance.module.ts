import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { SharedModule } from '../../@shared/shared.module';
import { NcActionAddEditComponent } from './nc-action-add-edit/nc-action-add-edit.component';
import { NcComponentAddEditComponent } from './nc-component-add-edit/nc-component-add-edit.component';
import { NcIssueAddEditComponent } from './nc-issue-add-edit/nc-issue-add-edit.component';
import { PmNonConformanceRoutingModule } from './pm-non-conformance-routing.module';
import { PmNonConformanceComponent } from './pm-non-conformance.component';

@NgModule({
  declarations: [PmNonConformanceComponent, NcIssueAddEditComponent, NcActionAddEditComponent, NcComponentAddEditComponent],
  imports: [CommonModule, SharedModule, PmNonConformanceRoutingModule]
})
export class PmNonConformanceModule {}
