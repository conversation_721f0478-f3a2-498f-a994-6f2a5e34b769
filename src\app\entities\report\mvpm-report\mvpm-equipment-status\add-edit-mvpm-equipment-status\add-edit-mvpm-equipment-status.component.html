<!-- Modal -->
<div class="modal-content" id="ncaddedit" data-focus-on="input:first" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <div class="container">
      <div class="row">
        <div class="col-6">
          <h6>{{ editNC.esGuid ? 'Update Equipment Status' : 'Add Equipment Status' }}</h6>
        </div>
        <div class="col-6 text-end">
          <button type="button" class="close" aria-label="Close" (click)="onCancel()">
            <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
          </button>
        </div>
      </div>
    </div>
  </div>
  <form
    name="equipmentStatusForm"
    #equipmentStatusForm="ngForm"
    aria-labelledby="title"
    autocomplete="off"
    (ngSubmit)="equipmentStatusForm?.form?.valid && createEditEquipmentStatus()"
  >
    <div class="modal-body">
      <div class="row">
        <div class="col-md-6">
          <h6>Equipment Status</h6>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <label class="label" for="input-component">Device Type <span *ngIf="!editNC?.esGuid" class="ms-1 text-danger">*</span></label>
          <div *ngIf="editNC?.esGuid">
            {{ editNC?.deviceTypeName }}
          </div>
          <div *ngIf="!editNC?.esGuid">
            <ng-select
              name="deviceType"
              [items]="deviceTypeList"
              (change)="DeviceTypeSelect(editNC.deviceTypeId)"
              bindLabel="name"
              bindValue="id"
              #deviceType="ngModel"
              [(ngModel)]="editNC.deviceTypeId"
              notFoundText="No Device Type Found"
              placeholder="Select Device Type"
              [clearable]="false"
              required
            >
            </ng-select>

            <sfl-error-msg
              [control]="deviceType"
              [isFormSubmitted]="equipmentStatusForm?.submitted"
              fieldName="Device Type"
            ></sfl-error-msg>
          </div>
        </div>
        <div class="col-md-4" *ngIf="editNC?.deviceTypeId">
          <label class="label" for="input-component"
            >{{ siteDeviceName }} Name <span *ngIf="!editNC?.esGuid" class="ms-1 text-danger">*</span></label
          >
          <div *ngIf="editNC?.esGuid">
            {{ editNC?.siteDeviceName }}
          </div>
          <div *ngIf="!editNC?.esGuid">
            <ng-select
              name="siteDeviceType"
              [items]="sitedDeviceTypeList"
              bindLabel="name"
              bindValue="id"
              #siteDeviceType="ngModel"
              [(ngModel)]="editNC.siteDeviceId"
              notFoundText="No Device Type Found"
              placeholder="Select Device Type"
              [clearable]="false"
              required
            >
            </ng-select>
            <sfl-error-msg
              [control]="siteDeviceType"
              [isFormSubmitted]="equipmentStatusForm?.submitted"
              fieldName="Device Type"
            ></sfl-error-msg>
          </div>
        </div>
        <div class="col-12 mb-3 m-t-sm" *ngIf="editNC?.deviceTypeId">
          <div class="form-control-group">
            <label class="label" for="input-address">Add Photos<span class="ms-1 text-danger">*</span></label>
            <div class="row" *ngIf="editNC?.deviceTypeId === 9">
              <ng-container *ngFor="let item of xfmrPhotoList; let i = index">
                <div class="col-md-6 col-lg-4 m-t-sm" *ngIf="!xfmrPhotosLinks[i.toString()]">
                  <label class="label" [for]="'xfmr' + i">{{ item }}<span class="ms-1 text-danger">*</span></label>

                  <div class="dropZone" ngFileDragDrop (fileDropped)="getXfmrUpload($event, i)">
                    <input [id]="'xfmr' + i" type="file" #file accept="image/*" (change)="getXfmrUpload($event.target.files, i)" />
                    <em class="fa fa-cloud-upload-alt text-primary iconimageupload fs-small" aria-hidden="true"></em><br />
                    <h5 class="fw-bold fs-small">Drop Image To Attach</h5>
                    <label style="text-transform: none" class="fw-bold fs-small">or Click To Browse </label>
                  </div>
                  <span *ngIf="xfmrPhotos[i.toString()]?.name">
                    {{ xfmrPhotos[i.toString()]?.name }}
                    <em
                      (click)="clearXfmrFile(i)"
                      nbtooltip="Delete"
                      nbtooltipplacement="top"
                      nbtooltipstatus="text-danger"
                      aria-hidden="true"
                      class="fa fa-times-circle text-danger ms-2 cursor-pointer"
                    ></em>
                  </span>
                </div>
                <div class="col-md-6 col-lg-4 m-t-sm" *ngIf="xfmrPhotosLinks[i.toString()]">
                  <label class="label">{{ item }}<span class="ms-1 text-danger">*</span></label>
                  <div class="example-box cdkImagesMargin" style="margin: 20px">
                    <a
                      (click)="deleteEquipmentStatusImages(xfmrPhotosLinks[i.toString()].fileId, false)"
                      class="removeNonConformImagesIcon"
                      *ngIf="selectedisFinal && !viewdeletetedbutton"
                    >
                      <em
                        class="fa fa-times-circle text-danger ms-2 pointer"
                        nbTooltip="Delete"
                        nbTooltipPlacement="top"
                        nbTooltipStatus="text-danger"
                        aria-hidden="true"
                      ></em>
                    </a>
                    <a class="checkNCImageIcon text-primary" *ngIf="selectedisFinal && !viewdeletetedbutton">
                      <nb-checkbox
                        class="chkRadioIncludebtn me-3"
                        (change)="includeReport(xfmrPhotosLinks[i.toString()]?.fileId)"
                        [(ngModel)]="xfmrPhotosLinks[i.toString()].isIncludeinReport"
                        [ngModelOptions]="{ standalone: true }"
                        nbTooltip="Include in report"
                        nbTooltipPlacement="top"
                        nbTooltipStatus="primary"
                      ></nb-checkbox>
                    </a>
                    <a
                      class="checkNCImageDeletedIcon text-primary"
                      *ngIf="viewdeletetedbutton && xfmrPhotosLinks[i.toString()]?.isIncludeinReport"
                    >
                      <em
                        class="fa fa-times-circle text-danger ms-2 pointer"
                        nbTooltip="Include in report"
                        nbTooltipPlacement="top"
                        nbTooltipStatus="primary"
                      ></em
                    ></a>
                    <img
                      (click)="imagePopup(index, xfmrPhotosLinks[i.toString()].imageGuid, true)"
                      [src]="xfmrPhotosLinks[i.toString()].imageUrl"
                      alt="Equipment Status Image"
                      class="text-center cursor-pointer card-img-top nonConformanceImage"
                      onError="this.src='assets/images/no-image-found.jpg'"
                    />
                  </div>
                  <div class="example-box cdkImagesMargin" *ngIf="viewdeletetedbutton">
                    <a class="checkGalleryIcon text-primary" *ngIf="xfmrPhotosLinks[i.toString()]?.isIncludeinReport">
                      <em class="fa fa-check-circle" nbTooltip="Include in report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em
                    ></a>
                    <img
                      (click)="imagePopup(index, xfmrPhotosLinks[i.toString()].imageGuid, editNC?.reportGuid)"
                      [src]="xfmrPhotosLinks[i.toString()]?.imageUrl"
                      alt="General Image"
                      class="text-center cursor-pointer card-img-top galleryImage"
                      onError="this.src='assets/images/no-image-found.jpg'"
                    />
                  </div>
                </div>
              </ng-container>
            </div>
            <div *ngIf="editNC?.deviceTypeId !== 9">
              <div class="dropZone" ngFileDragDrop (fileDropped)="getUpload($event)">
                <input type="file" #file required accept="image/*" multiple (change)="getUpload($event)" />
                <em class="fa fa-cloud-upload-alt text-primary iconimageupload fs-small" aria-hidden="true"></em><br />
                <h5 *ngIf="editNC?.deviceTypeId === 8 || editNC?.deviceTypeId === 33" class="fw-bold fs-small">
                  Add any 6 images to upload in report
                </h5>

                <h5 class="fw-bold fs-small">Drop Images To Attach</h5>
                <label style="text-transform: none" class="fw-bold fs-small">or Click To Browse </label>
              </div>
              <ul *ngIf="files.length">
                <li *ngFor="let item of files; let i = index">
                  <span>{{ item.name }}</span>
                  <em
                    (click)="deleteFile(i)"
                    nbtooltip="Delete"
                    nbtooltipplacement="top"
                    nbtooltipstatus="text-danger"
                    aria-hidden="true"
                    class="fa fa-times-circle text-danger ms-2 pointer"
                  ></em>
                </li>
              </ul>
            </div>

            <div class="form-group row mt-4" *ngIf="editNC?.equipmentStatusImages?.length > 0 && editNC?.deviceTypeId !== 9">
              <div class="col-12">
                <label class="fw-bold">Drag card to organize images</label>
              </div>
            </div>
            <div class="container" *ngIf="editNC.esGuid && editNC?.deviceTypeId !== 9">
              <div class="row mt-4 justify-content-center">
                <div class="col-md-12 row" cdkDropListGroup>
                  <div
                    [id]="'cdklist-' + index"
                    cdkDropList
                    [cdkDropListData]="index + 1"
                    class="col-sm-5 cdkImagesborder me-2 mb-2"
                    *ngFor="let item of editNC?.equipmentStatusImages | orderBy : 'order'; let index = index"
                    style="border: 1px dashed lightgray"
                  >
                    <div
                      cdkDrag
                      [cdkDragData]="index + 1"
                      class="example-box cdkImagesMargin"
                      (cdkDragEntered)="dragEntered($event, editNC?.equipmentStatusImages)"
                      style="margin: 20px"
                    >
                      <a
                        (click)="deleteEquipmentStatusImages(item.fileId, false)"
                        class="removeNonConformImagesIcon"
                        *ngIf="selectedisFinal && !viewdeletetedbutton"
                      >
                        <em
                          class="fa fa-times-circle text-danger ms-2 pointer"
                          nbTooltip="Delete"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="text-danger"
                          aria-hidden="true"
                        ></em>
                      </a>
                      <a class="checkNCImageIcon text-primary" *ngIf="selectedisFinal && !viewdeletetedbutton">
                        <nb-checkbox
                          class="chkRadioIncludebtn me-3"
                          (change)="includeReport(item?.fileId)"
                          [(ngModel)]="item.isIncludeinReport"
                          [ngModelOptions]="{ standalone: true }"
                          nbTooltip="Include in report"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                        ></nb-checkbox>
                      </a>
                      <a class="checkNCImageDeletedIcon text-primary" *ngIf="viewdeletetedbutton && item.isIncludeinReport">
                        <em
                          class="fa fa-times-circle text-danger ms-2 pointer"
                          nbTooltip="Include in report"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                        ></em
                      ></a>
                      <img
                        (click)="imagePopup(index, item.imageGuid, true)"
                        [src]="item?.thumbnailUrl || item?.imageUrl"
                        alt="Equipment Status Image"
                        class="text-center cursor-pointer card-img-top nonConformanceImage"
                        onError="this.src='assets/images/no-image-found.jpg'"
                      />
                    </div>
                    <div
                      cdkDrag
                      [cdkDragData]="index"
                      class="example-box cdkImagesMargin"
                      (cdkDragEntered)="dragEntered($event, editNC?.equipmentStatusImages)"
                      *ngIf="viewdeletetedbutton"
                    >
                      <a class="checkGalleryIcon text-primary" *ngIf="item.isIncludeinReport">
                        <em class="fa fa-check-circle" nbTooltip="Include in report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em
                      ></a>
                      <img
                        (click)="imagePopup(index, item.imageGuid, editNC?.reportGuid)"
                        [src]="item?.thumbnailUrl || item?.imageUrl"
                        alt="General Image"
                        class="text-center cursor-pointer card-img-top galleryImage"
                        onError="this.src='assets/images/no-image-found.jpg'"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row display-content" *ngIf="editNC?.deviceTypeId === 9">
          <div class="col-12">
            <label class="fw-bold">Transformer Status</label>
          </div>

          <div class="col-md-4">
            <label class="label" for="input-truckRolls">Sample Valve<span class="ms-1 text-danger">*</span></label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="sampleValve"
                required
                #sampleValve="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.sampleValve"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
              <sfl-error-msg
                [control]="sampleValve"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Sample Valve"
              ></sfl-error-msg>
            </div>
          </div>
          <div class="col-md-4">
            <label class="label" for="input-truckRolls">External</label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="sampleExternalValve"
                #sampleExternalValve="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.sampleValveExternal"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
            </div>
          </div>
          <div class="col-md-4"></div>
          <div class="col-md-4">
            <label class="label" for="input-truckRolls">Nitrogen Fill Value<span class="ms-1 text-danger">*</span></label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="nitrogenFillValve"
                required
                #nitrogenFillValve="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.nitrogenFillValve"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
              <sfl-error-msg
                [control]="nitrogenFillValve"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Nitrogen Fill Value"
              ></sfl-error-msg>
            </div>
          </div>
          <div class="col-md-4">
            <label class="label" for="input-truckRolls">External</label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="nitrogenFillExternalValve"
                #nitrogenFillExternalValve="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.nitrogenFillValveExternal"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
            </div>
          </div>
          <div class="col-md-4"></div>
          <div class="col-md-4">
            <label class="label" for="input-truckRolls">Pressure Gauge<span class="ms-1 text-danger">*</span></label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="pressureGauge"
                required
                #pressureGauge="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.pressureGauge"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
              <sfl-error-msg
                [control]="pressureGauge"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Pressure Gauge"
              ></sfl-error-msg>
            </div>
          </div>
          <div class="col-md-4">
            <label class="label" for="input-truckRolls">External</label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="pressureGaugeExternal"
                #pressureGaugeExternal="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.pressureGaugeExternal"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
            </div>
          </div>
          <div class="col-md-4"></div>
          <div class="col-md-4 m-t-sm">
            <label class="label" for="input-truckRolls">Temperature Gauge<span class="ms-1 text-danger">*</span></label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="temperatureGauge"
                required
                #temperatureGauge="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.temperatureGauge"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
              <sfl-error-msg
                [control]="temperatureGauge"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Temperature Gauge"
              ></sfl-error-msg>
            </div>
          </div>
          <div class="col-md-4">
            <label class="label" for="input-truckRolls">External</label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="temperatureGaugeExternal"
                #temperatureGaugeExternal="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.temperatureGaugeExternal"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
            </div>
          </div>
          <div class="col-md-4"></div>
          <div class="col-md-4 m-t-sm">
            <label class="label" for="input-truckRolls">Max Temp Reset<span class="ms-1 text-danger">*</span></label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="maxTempReset"
                required
                #maxTempReset="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.maxTempReset"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
              <sfl-error-msg
                [control]="maxTempReset"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Max Temp Reset"
              ></sfl-error-msg>
            </div>
          </div>
          <div class="col-md-4 m-t-sm">
            <label class="label" for="input-truckRolls">Liquid Level Gauge<span class="ms-1 text-danger">*</span></label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="liquidLevelGauge"
                required
                #liquidLevelGauge="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.liquidLevelGauge"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
              <sfl-error-msg
                [control]="liquidLevelGauge"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Liquid Level Gauge"
              ></sfl-error-msg>
            </div>
          </div>
          <div class="col-md-4 m-t-sm">
            <label class="label" for="input-truckRolls">Live Sample<span class="ms-1 text-danger">*</span></label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="liveSample"
                required
                #liveSample="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.liveSample"
              >
                <nb-radio [value]="true">Yes</nb-radio>
                <nb-radio [value]="false">No</nb-radio>
              </nb-radio-group>
              <sfl-error-msg
                [control]="liveSample"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Live Sample"
              ></sfl-error-msg>
            </div>
          </div>
          <div class="col-12 d-flex m-t-sm">
            <div class="col-10 pe-3">
              <label class="label" for="input-component"
                >Liquid Level<span *ngIf="!isNotRequired.liquidLevel" class="ms-1 text-danger">*</span></label
              >
              <ng-select
                name="liquidLevel"
                [items]="liquidLevelList"
                #liquidLevel="ngModel"
                [(ngModel)]="editNC.transformerEquipmentStatus.liquidLevel"
                placeholder="Select Liquid Level Type"
                [clearable]="true"
                [required]="!isNotRequired.liquidLevel"
                [disabled]="isNotRequired.liquidLevel"
              >
              </ng-select>

              <sfl-error-msg
                [control]="liquidLevel"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Liquid Level"
              ></sfl-error-msg>
            </div>
            <div class="col-2 mt-33">
              <nb-checkbox
                [(ngModel)]="isNotRequired.liquidLevel"
                [ngModelOptions]="{ standalone: true }"
                name="liquidLevelNA"
                (checkedChange)="editNC.transformerEquipmentStatus.liquidLevel = null"
                >N/A</nb-checkbox
              >
            </div>
          </div>
          <div class="col-12 d-flex m-t-sm">
            <div class="col-10 pe-3 d-flex flex-wrap">
              <div class="col-md-6 pe-md-3 col-12">
                <label class="label" for="input-component"
                  >Top Oil Temperature<span *ngIf="!isNotRequired.oilTemp" class="ms-1 text-danger">*</span><br class="d-md-none" />&nbsp;
                  <small>(White Needle)</small></label
                >
                <div class="d-flex align-items-center">
                  <div class="col-10 pe-2">
                    <input
                      nbInput
                      fullWidth
                      [(ngModel)]="editNC.transformerEquipmentStatus.topOilTemperature"
                      #topOilTemperature="ngModel"
                      name="topOilTemperature"
                      class="form-control"
                      required
                      [attr.aria-invalid]="topOilTemperature.invalid && topOilTemperature.touched ? true : null"
                      [disabled]="isNotRequired.oilTemp"
                      (input)="validateDecimal($event)"
                    />
                  </div>
                  <div class="col-2"><span>°C</span></div>
                </div>
                <sfl-error-msg
                  [control]="topOilTemperature"
                  [isFormSubmitted]="equipmentStatusForm?.submitted"
                  fieldName="Top Oil Temperature"
                ></sfl-error-msg>
              </div>
              <div class="col-md-6 mt-md-0 mt-2 col-12">
                <label class="label" for="input-component"
                  >Max Oil Temperature<span *ngIf="!isNotRequired.oilTemp" class="ms-1 text-danger">*</span><br class="d-md-none" />&nbsp;
                  <small>(Red Needle)</small></label
                >
                <div class="d-flex align-items-center">
                  <div class="col-10 pe-2">
                    <input
                      nbInput
                      fullWidth
                      [(ngModel)]="editNC.transformerEquipmentStatus.maxOilTemperature"
                      #maxOilTemperature="ngModel"
                      name="maxOilTemperature"
                      class="form-control"
                      required
                      [attr.aria-invalid]="maxOilTemperature.invalid && maxOilTemperature.touched ? true : null"
                      [disabled]="isNotRequired.oilTemp"
                      (input)="validateDecimal($event)"
                    />
                  </div>
                  <div class="col-2"><span>°C</span></div>
                </div>
                <sfl-error-msg
                  [control]="maxOilTemperature"
                  [isFormSubmitted]="equipmentStatusForm?.submitted"
                  fieldName="Max Oil Temperature"
                ></sfl-error-msg>
              </div>
            </div>
            <div class="col-2 checkbox-for-temp">
              <nb-checkbox
                [(ngModel)]="isNotRequired.oilTemp"
                [ngModelOptions]="{ standalone: true }"
                name="oilTempNA"
                (checkedChange)="
                  editNC.transformerEquipmentStatus.maxOilTemperature = null; editNC.transformerEquipmentStatus.topOilTemperature = null
                "
                >N/A</nb-checkbox
              >
            </div>
          </div>
          <div class="col-12 d-flex m-t-sm">
            <div class="col-10 pe-3 d-flex flex-wrap">
              <div class="col-md-6 pe-md-3 col-12">
                <label class="label" for="input-component"
                  >Pressure As Found<span *ngIf="!isNotRequired.pressure" class="ms-1 text-danger">*</span></label
                >
                <div class="d-flex align-items-center">
                  <div class="col-10 pe-2">
                    <input
                      nbInput
                      fullWidth
                      [(ngModel)]="editNC.transformerEquipmentStatus.pressureAsFound"
                      #pressureAsFound="ngModel"
                      name="pressureAsFound"
                      class="form-control"
                      [required]="!isNotRequired.pressure"
                      [disabled]="isNotRequired.pressure"
                      [attr.aria-invalid]="pressureAsFound.invalid && pressureAsFound.touched ? true : null"
                      (input)="validateDecimal($event)"
                    />
                  </div>
                  <div class="col-2"><span>psi</span></div>
                </div>
                <sfl-error-msg
                  [control]="pressureAsFound"
                  [isFormSubmitted]="equipmentStatusForm?.submitted"
                  fieldName="Pressure As Found"
                ></sfl-error-msg>
              </div>
              <div class="col-md-6 col-12">
                <label class="label" for="input-component"
                  >Pressure As Left<span *ngIf="!isNotRequired.pressure" class="ms-1 text-danger">*</span></label
                >
                <div class="d-flex align-items-center">
                  <div class="col-10 pe-2">
                    <input
                      nbInput
                      fullWidth
                      [(ngModel)]="editNC.transformerEquipmentStatus.pressureAsLeft"
                      #pressureAsLeft="ngModel"
                      name="pressureAsLeft"
                      class="form-control"
                      [required]="!isNotRequired.pressure"
                      [disabled]="isNotRequired.pressure"
                      [attr.aria-invalid]="pressureAsLeft.invalid && pressureAsLeft.touched ? true : null"
                      (input)="validateDecimal($event)"
                    />
                  </div>
                  <div class="col-2"><span>psi</span></div>
                </div>
                <sfl-error-msg
                  [control]="pressureAsLeft"
                  [isFormSubmitted]="equipmentStatusForm?.submitted"
                  fieldName="Pressure As Left"
                ></sfl-error-msg>
              </div>
            </div>
            <div class="col-2 checkbox-for-pressure">
              <nb-checkbox
                [(ngModel)]="isNotRequired.pressure"
                [ngModelOptions]="{ standalone: true }"
                name="pressureNA"
                id="pressureNA"
                (checkedChange)="
                  editNC.transformerEquipmentStatus.pressureAsFound = null; editNC.transformerEquipmentStatus.pressureAsLeft = null
                "
                >N/A</nb-checkbox
              >
            </div>
          </div>
          <div class="col-12 d-flex m-t-sm">
            <div class="col-10 pe-3">
              <label class="label" for="input-component"
                >DETC Position<span *ngIf="!isNotRequired.detc" class="ms-1 text-danger">*</span></label
              >
              <input
                nbInput
                fullWidth
                [(ngModel)]="editNC.transformerEquipmentStatus.detcPosition"
                #detcPosition="ngModel"
                name="detcPosition"
                class="form-control"
                [required]="!isNotRequired.detc"
                [disabled]="isNotRequired.detc"
                [attr.aria-invalid]="detcPosition.invalid && detcPosition.touched ? true : null"
              />

              <sfl-error-msg
                [control]="detcPosition"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="DETC Position"
              ></sfl-error-msg>
            </div>
            <div class="col-2 mt-33">
              <nb-checkbox
                [(ngModel)]="isNotRequired.detc"
                [ngModelOptions]="{ standalone: true }"
                name="detcNA"
                (checkedChange)="editNC.transformerEquipmentStatus.detcPosition = null"
                >N/A</nb-checkbox
              >
            </div>
          </div>
          <div class="col-12 d-flex m-t-sm">
            <div class="col-10 pe-3">
              <label class="label" for="groundingTestResults"
                >Grounding Test Results<span *ngIf="!isNotRequired.grdtest" class="ms-1 text-danger">*</span> <br /><small
                  >Note: Per NEC, resistance must be below 25 ohm.</small
                >
              </label>
              <input
                nbInput
                fullWidth
                [(ngModel)]="editNC.transformerEquipmentStatus.groundingTestResults"
                #groundingTestResults="ngModel"
                name="groundingTestResults"
                id="groundingTestResults"
                class="form-control"
                [required]="!isNotRequired.grdtest"
                [disabled]="isNotRequired.grdtest"
                [attr.aria-invalid]="groundingTestResults.invalid && groundingTestResults.touched ? true : null"
              />

              <sfl-error-msg
                [control]="groundingTestResults"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Grounding Test Results"
              ></sfl-error-msg>
            </div>
            <div class="col-2 mt-45">
              <nb-checkbox
                [(ngModel)]="isNotRequired.grdtest"
                [ngModelOptions]="{ standalone: true }"
                name="detcNA"
                (checkedChange)="editNC.transformerEquipmentStatus.groundingTestResults = null"
                >N/A</nb-checkbox
              >
            </div>
          </div>
          <div class="col-lg-4 col-sm-6 m-t-sm" *ngIf="editNC?.transformerEquipmentStatus?.nameplateImageURL">
            <label class="label" for="input-component">Nameplate Photo<span class="ms-1 text-danger">*</span></label>
            <div class="text-center position-relative">
              <a
                (click)="deleteEquipmentStatusImages(editNC?.transformerEquipmentStatus?.nameplateImageFileId, true)"
                class="removeNonConformImagesIcon"
              >
                <em
                  class="fa fa-times-circle text-danger ms-2 pointer"
                  nbTooltip="Delete"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="danger"
                  aria-hidden="true"
                ></em>
              </a>
              <img
                [src]="editNC?.transformerEquipmentStatus?.nameplateImageURL"
                alt="Equipment Status Image"
                class="text-center img-thumbnail img-responsive siteImage cursor-pointer"
                onError="this.src='assets/images/no-image-found.jpg'"
              />
            </div>
          </div>
          <div class="col-lg-4 col-sm-6 m-t-sm" *ngIf="!editNC?.transformerEquipmentStatus.nameplateImageURL">
            <label class="label" for="input-component">Nameplate Photo<span class="ms-1 text-danger">*</span></label>

            <div class="dropZone" ngFileDragDrop (fileDropped)="getNamePlateImageUpload($event)">
              <input type="file" #file accept="image/*" (change)="getNamePlateImageUpload($event.target.files)" />
              <em class="fa fa-cloud-upload-alt text-primary iconimageupload fs-small" aria-hidden="true"></em><br />
              <h5 class="fw-bold fs-small">Drop Image To Attach</h5>
              <label style="text-transform: none" class="fw-bold fs-small">or Click To Browse </label>
            </div>
            <span *ngIf="namePlateImageFile?.name">
              {{ namePlateImageFile?.name }}
              <em
                (click)="namePlateImageFile = null"
                nbtooltip="Delete"
                nbtooltipplacement="top"
                nbtooltipstatus="text-danger"
                aria-hidden="true"
                class="fa fa-times-circle text-danger ms-2 pointer"
              ></em>
            </span>
          </div>
        </div>

        <div class="row display-content" *ngIf="editNC?.deviceTypeId === 33">
          <div class="mb-2">
            <div class="row">
              <nb-accordion class="pb-2">
                <nb-accordion-item [expanded]="false" class="border-bottom">
                  <nb-accordion-item-header class="accordion_head flex-wrap">
                    <label class="label sf-15" for="CTs/PTs">CTs/PTs</label>
                  </nb-accordion-item-header>
                  <nb-accordion-item-body class="accordion_body">
                    <div class="col-12 m-t-sm">
                      <div
                        class="dropZone mv-disconnect"
                        ngFileDragDrop
                        (fileDropped)="getMVDisconnectOtherSectionUpload($event, 'cTpsFiles')"
                      >
                        <input
                          id="CTs/PTs"
                          type="file"
                          #fileCTsPTs
                          multiple
                          accept="image/*"
                          (change)="getMVDisconnectOtherSectionUpload($event, 'cTpsFiles')"
                        />
                        <em class="fa fa-cloud-upload-alt text-primary iconimageupload fs-small" aria-hidden="true"></em><br />
                        <h6 class="fw-bold fs-small">Add any 4 images to upload in report</h6>
                        <h6 class="fw-bold fs-small">Drop Image To Attach</h6>
                        <label style="text-transform: none" class="fw-bold fs-small">or Click To Browse </label>
                      </div>
                      <ul *ngIf="cTpsFiles?.length">
                        <li *ngFor="let item of cTpsFiles; let i = index">
                          <span>{{ item.name }}</span>
                          <em
                            (click)="deleteImageFile(i, 'cTpsFiles')"
                            nbtooltip="Delete"
                            nbtooltipplacement="top"
                            nbtooltipstatus="text-danger"
                            aria-hidden="true"
                            class="fa fa-times-circle text-danger ms-2 pointer"
                          ></em>
                        </li>
                      </ul>
                      <!-- Label -->
                      <div class="form-group row mt-4" *ngIf="mvDisconnectImages?.length && editNC?.deviceTypeId === 33">
                        <div class="col-12">
                          <label class="fw-bold"> CTs/PTs images</label>
                        </div>
                      </div>

                      <!-- Image Grid -->
                      <div class="container" *ngIf="mvDisconnectImages?.length && editNC?.deviceTypeId === 33">
                        <div class="row mt-4 justify-content-center">
                          <div class="col-md-12 row">
                            <ng-container *ngFor="let file of mvDisconnectImages | orderBy : 'order'; let index = index">
                              <div
                                *ngIf="file.sectionId === 1"
                                class="col-sm-5 cdkImagesborder me-2 mb-2"
                                style="border: 1px dashed lightgray"
                              >
                                <div class="example-box cdkImagesMargin" style="margin: 20px">
                                  <!-- Delete Icon -->
                                  <a
                                    (click)="deleteEquipmentStatusImages(file.fileId, false)"
                                    class="removeNonConformImagesIcon"
                                    *ngIf="selectedisFinal && !viewdeletetedbutton"
                                  >
                                    <em
                                      class="fa fa-times-circle text-danger ms-2 pointer"
                                      nbTooltip="Delete"
                                      nbTooltipPlacement="top"
                                      nbTooltipStatus="text-danger"
                                      aria-hidden="true"
                                    ></em>
                                  </a>

                                  <!-- Image -->
                                  <img
                                    (click)="imagePopup(index, file.imageGuid, true)"
                                    [src]="file?.thumbnailUrl || file?.imageUrl"
                                    alt="CTs/PTs Image"
                                    class="text-center cursor-pointer card-img-top nonConformanceImage"
                                    onError="this.src='assets/images/no-image-found.jpg'"
                                  />
                                </div>
                              </div>
                            </ng-container>
                          </div>
                        </div>
                      </div>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>

                <nb-accordion-item [expanded]="false" class="border-bottom">
                  <nb-accordion-item-header class="accordion_head flex-wrap">
                    <label class="label sf-15" for="Fusing">Fusing</label>
                  </nb-accordion-item-header>
                  <nb-accordion-item-body class="accordion_body">
                    <div class="col-12 m-t-sm">
                      <div
                        class="dropZone mv-disconnect"
                        ngFileDragDrop
                        (fileDropped)="getMVDisconnectOtherSectionUpload($event, 'fusingFiles')"
                      >
                        <input
                          id="Fusing"
                          type="file"
                          #fileFusing
                          multiple
                          accept="image/*"
                          (change)="getMVDisconnectOtherSectionUpload($event, 'fusingFiles')"
                        />
                        <em class="fa fa-cloud-upload-alt text-primary iconimageupload fs-small" aria-hidden="true"></em><br />
                        <h6 class="fw-bold fs-small">Add any 4 images to upload in report</h6>
                        <h6 class="fw-bold fs-small">Drop Image To Attach</h6>
                        <label style="text-transform: none" class="fw-bold fs-small">or Click To Browse </label>
                      </div>
                      <ul *ngIf="fusingFiles?.length">
                        <li *ngFor="let item of fusingFiles; let i = index">
                          <span>{{ item.name }}</span>
                          <em
                            (click)="deleteImageFile(i, 'fusingFiles')"
                            nbtooltip="Delete"
                            nbtooltipplacement="top"
                            nbtooltipstatus="text-danger"
                            aria-hidden="true"
                            class="fa fa-times-circle text-danger ms-2 pointer"
                          ></em>
                        </li>
                      </ul>
                      <!-- Label -->
                      <div class="form-group row mt-4" *ngIf="mvDisconnectImages?.length && editNC?.deviceTypeId === 33">
                        <div class="col-12">
                          <label class="fw-bold">Fusing images</label>
                        </div>
                      </div>

                      <!-- Image Grid -->
                      <div class="container" *ngIf="mvDisconnectImages?.length && editNC?.deviceTypeId === 33">
                        <div class="row mt-4 justify-content-center">
                          <div class="col-md-12 row">
                            <ng-container *ngFor="let file of mvDisconnectImages | orderBy : 'order'; let index = index">
                              <div
                                *ngIf="file.sectionId === 2"
                                class="col-sm-5 cdkImagesborder me-2 mb-2"
                                style="border: 1px dashed lightgray"
                              >
                                <div class="example-box cdkImagesMargin" style="margin: 20px">
                                  <!-- Delete Icon -->
                                  <a
                                    (click)="deleteEquipmentStatusImages(file.fileId, false)"
                                    class="removeNonConformImagesIcon"
                                    *ngIf="selectedisFinal && !viewdeletetedbutton"
                                  >
                                    <em
                                      class="fa fa-times-circle text-danger ms-2 pointer"
                                      nbTooltip="Delete"
                                      nbTooltipPlacement="top"
                                      nbTooltipStatus="text-danger"
                                      aria-hidden="true"
                                    ></em>
                                  </a>

                                  <!-- Image -->
                                  <img
                                    (click)="imagePopup(index, file.imageGuid, true)"
                                    [src]="file?.thumbnailUrl || file?.imageUrl"
                                    alt="CTs/PTs Image"
                                    class="text-center cursor-pointer card-img-top nonConformanceImage"
                                    onError="this.src='assets/images/no-image-found.jpg'"
                                  />
                                </div>
                              </div>
                            </ng-container>
                          </div>
                        </div>
                      </div>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>

                <nb-accordion-item [expanded]="false" class="border-bottom">
                  <nb-accordion-item-header class="accordion_head flex-wrap">
                    <label class="label sf-15" for="InstrumentPower">Instrument / Control Power Transformers</label>
                  </nb-accordion-item-header>
                  <nb-accordion-item-body class="accordion_body">
                    <div class="col-12 m-t-sm">
                      <div
                        class="dropZone mv-disconnect"
                        ngFileDragDrop
                        (fileDropped)="getMVDisconnectOtherSectionUpload($event, 'instrumentPowerFiles')"
                      >
                        <input
                          id="InstrumentPower"
                          type="file"
                          #fileInstrumentPower
                          multiple
                          accept="image/*"
                          (change)="getMVDisconnectOtherSectionUpload($event, 'instrumentPowerFiles')"
                        />
                        <em class="fa fa-cloud-upload-alt text-primary iconimageupload fs-small" aria-hidden="true"></em><br />
                        <h6 class="fw-bold fs-small">Add any 4 images to upload in report</h6>
                        <h6 class="fw-bold fs-small">Drop Image To Attach</h6>
                        <label style="text-transform: none" class="fw-bold fs-small">or Click To Browse </label>
                      </div>
                      <ul *ngIf="instrumentPowerFiles?.length">
                        <li *ngFor="let item of instrumentPowerFiles; let i = index">
                          <span>{{ item.name }}</span>
                          <em
                            (click)="deleteImageFile(i, 'instrumentPowerFiles')"
                            nbtooltip="Delete"
                            nbtooltipplacement="top"
                            nbtooltipstatus="text-danger"
                            aria-hidden="true"
                            class="fa fa-times-circle text-danger ms-2 pointer"
                          ></em>
                        </li>
                      </ul>
                      <!-- Label -->
                      <div class="form-group row mt-4" *ngIf="mvDisconnectImages?.length && editNC?.deviceTypeId === 33">
                        <div class="col-12">
                          <label class="fw-bold">Instrument / Control Power Transformers images</label>
                        </div>
                      </div>

                      <!-- Image Grid -->
                      <div class="container" *ngIf="mvDisconnectImages?.length && editNC?.deviceTypeId === 33">
                        <div class="row mt-4 justify-content-center">
                          <div class="col-md-12 row">
                            <ng-container *ngFor="let file of mvDisconnectImages | orderBy : 'order'; let index = index">
                              <div
                                *ngIf="file.sectionId === 3"
                                class="col-sm-5 cdkImagesborder me-2 mb-2"
                                style="border: 1px dashed lightgray"
                              >
                                <div class="example-box cdkImagesMargin" style="margin: 20px">
                                  <!-- Delete Icon -->
                                  <a
                                    (click)="deleteEquipmentStatusImages(file.fileId, false)"
                                    class="removeNonConformImagesIcon"
                                    *ngIf="selectedisFinal && !viewdeletetedbutton"
                                  >
                                    <em
                                      class="fa fa-times-circle text-danger ms-2 pointer"
                                      nbTooltip="Delete"
                                      nbTooltipPlacement="top"
                                      nbTooltipStatus="text-danger"
                                      aria-hidden="true"
                                    ></em>
                                  </a>

                                  <!-- Image -->
                                  <img
                                    (click)="imagePopup(index, file.imageGuid, true)"
                                    [src]="file?.thumbnailUrl || file?.imageUrl"
                                    alt="CTs/PTs Image"
                                    class="text-center cursor-pointer card-img-top nonConformanceImage"
                                    onError="this.src='assets/images/no-image-found.jpg'"
                                  />
                                </div>
                              </div>
                            </ng-container>
                          </div>
                        </div>
                      </div>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
              </nb-accordion>
            </div>
          </div>

          <div class="col-12">
            <label class="fw-bold">MV Disconnect Status</label>
          </div>

          <div class="col-md-6 d-flex m-t-sm">
            <div class="col-10 pe-3">
              <label class="label" for="input-component"
                >SF6 Level<span *ngIf="!isNotRequired.sF6Level" class="ms-1 text-danger">*</span></label
              >
              <ng-select
                name="sF6Level"
                [items]="sF6LevelList"
                #sF6Level="ngModel"
                [(ngModel)]="editNC.mvDisconnectedStatus.sF6Level"
                placeholder="Select SF6 Level Type"
                [clearable]="true"
                [required]="!isNotRequired.sF6Level"
                [disabled]="isNotRequired.sF6Level"
              >
              </ng-select>

              <sfl-error-msg [control]="sF6Level" [isFormSubmitted]="equipmentStatusForm?.submitted" fieldName="SF6 Level"></sfl-error-msg>
            </div>
            <div class="col-2 mt-33">
              <nb-checkbox
                [(ngModel)]="isNotRequired.sF6Level"
                [ngModelOptions]="{ standalone: true }"
                name="sF6LevelNA"
                (checkedChange)="editNC.mvDisconnectedStatus.sF6Level = null"
                >N/A</nb-checkbox
              >
            </div>
          </div>

          <div class="col-md-6 d-flex m-t-sm">
            <div class="col-10 pe-3">
              <label class="label" for="input-component"
                >Liquid Level<span *ngIf="!isNotRequired.liquidLevel" class="ms-1 text-danger">*</span></label
              >
              <ng-select
                name="liquidLevel"
                [items]="liquidLevelList"
                #liquidLevel="ngModel"
                [(ngModel)]="editNC.mvDisconnectedStatus.liquidLevel"
                placeholder="Select Liquid Level Type"
                [clearable]="true"
                [required]="!isNotRequired.liquidLevel"
                [disabled]="isNotRequired.liquidLevel"
              >
              </ng-select>

              <sfl-error-msg
                [control]="liquidLevel"
                [isFormSubmitted]="equipmentStatusForm?.submitted"
                fieldName="Liquid Level"
              ></sfl-error-msg>
            </div>
            <div class="col-2 mt-33">
              <nb-checkbox
                [(ngModel)]="isNotRequired.liquidLevel"
                [ngModelOptions]="{ standalone: true }"
                name="liquidLevelNA"
                (checkedChange)="editNC.mvDisconnectedStatus.liquidLevel = null"
                >N/A</nb-checkbox
              >
            </div>
          </div>
          <div class="col-md-6 m-t-sm">
            <label class="label" for="input-truckRolls">Voltage Indicator Functional</label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="voltageIndicatorFunctional"
                #voltageIndicatorFunctional="ngModel"
                [(ngModel)]="editNC.mvDisconnectedStatus.voltageIndicatorFunctional"
              >
                <nb-radio [value]="1">Yes</nb-radio>
                <nb-radio [value]="0">No</nb-radio>
                <nb-radio [value]="2">N/A</nb-radio>
              </nb-radio-group>
            </div>
          </div>
          <div class="col-md-6 m-t-sm">
            <label class="label" for="input-Notes">Notes/Comments</label>
            <textarea
              nbInput
              fullWidth
              name="notesComments"
              id="input-fileDetailsNote"
              #notesComments="ngModel"
              [(ngModel)]="editNC.mvDisconnectedStatus.note"
              maxlength="5120"
            ></textarea>
          </div>
        </div>

        <div class="row display-content" *ngIf="editNC?.deviceTypeId === 8">
          <div class="col-12">
            <label class="fw-bold">Protective Relay Status</label>
          </div>

          <div class="col-md-4">
            <label class="label" for="input-truckRolls">Relay 3 Phase Open Test</label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="relay3PhaseOpenTest"
                #relay3PhaseOpenTest="ngModel"
                [(ngModel)]="editNC.protectiveRelayStatus.relay3PhaseOpenTest"
              >
                <nb-radio [value]="true">Pass</nb-radio>
                <nb-radio [value]="false">Fail</nb-radio>
                <nb-radio [value]="null">N/A</nb-radio>
              </nb-radio-group>
            </div>
          </div>

          <div class="col-md-4">
            <label class="label" for="input-relay">Relay Loss Of Power Test</label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="relayLossOfPowerTest"
                #relayLossOfPowerTest="ngModel"
                [(ngModel)]="editNC.protectiveRelayStatus.relayLossOfPowerTest"
              >
                <nb-radio [value]="true">Pass</nb-radio>
                <nb-radio [value]="false">Fail</nb-radio>
                <nb-radio [value]="null">N/A</nb-radio>
              </nb-radio-group>
            </div>
          </div>

          <div class="col-md-4">
            <label class="label" for="input-internal">Relay Internal Self-Test</label>
            <div>
              <nb-radio-group
                class="d-flex"
                name="relayInternalSelfTest"
                #relayInternalSelfTest="ngModel"
                [(ngModel)]="editNC.protectiveRelayStatus.relayInternalSelfTest"
              >
                <nb-radio [value]="true">Pass</nb-radio>
                <nb-radio [value]="false">Fail</nb-radio>
                <nb-radio [value]="null">N/A</nb-radio>
              </nb-radio-group>
            </div>
          </div>
          <div class="col-md-4">
            <label class="label" for="replaceDate"
              >Backup Battery Charge/Replace Date<span *ngIf="!editNC.protectiveRelayStatus.replaceDateNA" class="ms-1 text-danger"
                >*</span
              ></label
            >

            <div class="row">
              <div class="col-md-3 mt-1">
                <nb-checkbox
                  [checked]="editNC.protectiveRelayStatus.replaceDateNA"
                  [(ngModel)]="editNC.protectiveRelayStatus.replaceDateNA"
                  [ngModelOptions]="{ standalone: true }"
                  name="replaceDate"
                  (checkedChange)="editNC.protectiveRelayStatus.replaceDate = null"
                >
                  N/A
                </nb-checkbox>
              </div>
              <div class="col-md-8">
                <input
                  nbInput
                  fullWidth
                  class="form-control"
                  [nbDatepicker]="replaceDate"
                  [(ngModel)]="editNC.protectiveRelayStatus.replaceDate"
                  name="replaceDate"
                  id="input-replaceDate"
                  #replaceDates="ngModel"
                  autocomplete="off"
                  [disabled]="editNC.protectiveRelayStatus.replaceDateNA"
                  readonly
                  [required]="editNC.protectiveRelayStatus.replaceDateNA ? false : true"
                />

                <nb-datepicker #replaceDate></nb-datepicker>
                <sfl-error-msg
                  [control]="replaceDates"
                  [isFormSubmitted]="equipmentStatusForm?.submitted"
                  fieldName="replace date"
                ></sfl-error-msg>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <label class="label" for="relayDate"
              >Relay Calibration Date
              <span *ngIf="!editNC.protectiveRelayStatus.relayCalibrationDateNA" class="ms-1 text-danger">*</span></label
            >

            <div class="row">
              <div class="col-md-3 mt-1">
                <nb-checkbox
                  name="relayDate"
                  (checkedChange)="editNC.protectiveRelayStatus.relayCalibrationDate = null"
                  [ngModelOptions]="{ standalone: true }"
                  [checked]="editNC.protectiveRelayStatus.relayCalibrationDateNA"
                  [(ngModel)]="editNC.protectiveRelayStatus.relayCalibrationDateNA"
                >
                  N/A
                </nb-checkbox>
              </div>
              <div class="col-md-8">
                <input
                  nbInput
                  fullWidth
                  name="relayDate"
                  class="form-control"
                  [nbDatepicker]="relayDate"
                  readonly
                  #relayDates="ngModel"
                  autocomplete="off"
                  [disabled]="editNC.protectiveRelayStatus.relayCalibrationDateNA"
                  [(ngModel)]="editNC.protectiveRelayStatus.relayCalibrationDate"
                  [required]="editNC.protectiveRelayStatus.relayCalibrationDate ? false : true"
                />
                <nb-datepicker #relayDate></nb-datepicker>
                <sfl-error-msg
                  [control]="relayDates"
                  [isFormSubmitted]="equipmentStatusForm?.submitted"
                  fieldName="relay date"
                ></sfl-error-msg>
              </div>
            </div>
          </div>
          <div class="col-12 col-lg-8 m-t-sm">
            <label class="label" for="input-Notes">Notes/Comments</label>
            <textarea
              nbInput
              fullWidth
              name="notesComments"
              id="input-fileDetailsNote"
              #notesComments="ngModel"
              [(ngModel)]="editNC.protectiveRelayStatus.note"
              maxlength="5120"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button nbButton status="primary" size="medium" *ngIf="selectedisFinal && !viewdeletetedbutton" type="submit">Save</button>
      <button nbButton status="basic" size="medium" type="button" (click)="onCancel()">Cancel</button>
    </div>
  </form>
</div>
