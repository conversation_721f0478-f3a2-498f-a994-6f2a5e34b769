export class PowerCardsFilter {
  public customerId: number;
  public portfolioIds: number[];
  public siteIds: number[] = []; // Changed to array like power chart
  public sortBy: number;
  public startDate?: Date | string;
  public endDate?: Date | string;
}

export interface PowerCardItem extends PowerCardsResponse {
  isLoading: boolean;
}

export class PowerCardsResponse {
  public siteId: number;
  public siteName: string;
  public acSize: number;
  public dcSize: number;
  public deviceData: DeviceRecords[];
  public alertDetails: AlertDetails[];
  public alertCount: number = 0;
}

export class DeviceRecords {
  public deviceId: number;
  public deviceName: string;
  public deviceTypeId: number;
  public binData: number;
  public isDeviceAlert: boolean;
  public binDateTime: string;
}

export class AlertDetails {
  public deviceName: string;
  public binData: number;
}
