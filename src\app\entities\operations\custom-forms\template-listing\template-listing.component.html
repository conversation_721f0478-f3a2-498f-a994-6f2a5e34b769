<nb-card class="customerSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center"></div>
    </div>
    <div class="row">
      <div class="col-12 d-flex justify-content-between align-items-center">
        <h6>Templates</h6>
        <div>
          <button
            class="linear-mode-button me-2"
            nbButton
            status="primary"
            size="small"
            [disabled]="loading"
            (click)="gotoForms()"
            type="button"
            *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]"
          >
            <span class="d-none d-md-inline-block">Go to Forms</span>
            <em class="d-inline-block d-md-none fa-regular fa-square-plus"></em>
          </button>
          <button
            class="linear-mode-button me-2"
            nbButton
            status="primary"
            size="small"
            [disabled]="loading"
            (click)="openModal(addTemplate, 'modal-md')"
            type="button"
            *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]"
          >
            <span class="d-none d-md-inline-block">Add Template</span>
            <em class="d-inline-block d-md-none fa-regular fa-square-plus"></em>
          </button>
          <button
            class="linear-mode-button"
            nbButton
            status="basic"
            size="small"
            [disabled]="loading"
            (click)="goToLandingPage()"
            type="button"
          >
            <span class="d-none d-lg-inline-block">Back</span>
            <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row" *ngIf="!isNew">
      <div class="col-12 customerFilter appFilter">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive mt-3 table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Customer List">
          <thead>
            <tr>
              <th (click)="sort('templateTypeName', sortOptionList['templateTypeName'])" id="templateTypeName">
                <div class="d-flex align-items-center">
                  Template Type
                  <span
                    class="fa text-center cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['templateTypeName'] === 'desc',
                      'fa-arrow-down': sortOptionList['templateTypeName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'templateTypeName'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('templateName', sortOptionList['templateName'])" id="templateName">
                <div class="d-flex align-items-center">
                  Template Name
                  <span
                    class="fa text-center cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['templateName'] === 'desc',
                      'fa-arrow-down': sortOptionList['templateName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'templateName'
                    }"
                  ></span>
                </div>
              </th>
              <th id="Note">Note</th>
              <th (click)="sort('updatedDate', sortOptionList['updatedDate'])" id="updatedDate">
                <div class="d-flex align-items-center">
                  Last Updated On
                  <span
                    class="fa text-center cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['updatedDate'] === 'desc',
                      'fa-arrow-down': sortOptionList['updatedDate'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'updatedDate'
                    }"
                  ></span>
                </div>
              </th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let template of templateList
                  | paginate
                    : { itemsPerPage: filterModel.itemsCount, currentPage: currentPage, totalItems: totalCount, id: 'formTemplateListing' }
              "
            >
              <td data-title="Template Type">{{ template.templateTypeName }}</td>
              <td data-title="Template Name">{{ template.templateName }}</td>
              <td data-title="Template Note">{{ template.templateNote }}</td>
              <td data-title="Last Updated On">{{ (template.updatedDate | date : 'MM/dd/yyyy') || '-' }}</td>
              <td data-title="Action" class="text-center template-action">
                <em
                  class="fa fa-copy text-primary cursor-pointer me-2"
                  nbTooltip="Clone Template"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                  (click)="cloneQESTTemplate(template.qestTemplateId)"
                  *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]"
                ></em>
                <em
                  class="fa fa-eye cursor-pointer text-primary me-2"
                  nbTooltip="View"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                  aria-hidden="true"
                  (click)="customFormService.previewFormOrTemplate(template.qestTemplateId)"
                  *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]"
                ></em>
                <em
                  class="fa fa-edit cursor-pointer text-primary me-2"
                  nbTooltip="Edit"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                  aria-hidden="true"
                  (click)="goToEditTemplatePage(template.qestTemplateId)"
                  *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]"
                ></em>
                <em
                  class="fa fa-trash cursor-pointer text-danger"
                  nbTooltip="Delete"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                  aria-hidden="true"
                  (click)="onTemplateDelete(template.qestTemplateId)"
                  *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]"
                ></em>
              </td>
            </tr>
            <tr *ngIf="!templateList?.length">
              <td colspan="5" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="templateList?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            id="formTemplateListing"
            [(ngModel)]="pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSize()"
            appendTo="body"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls id="formTemplateListing" (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
    <div *ngIf="isNew && !loading">
      <ng-container *ngTemplateOutlet="noTemplateYet"></ng-container>
    </div>
  </nb-card-body>
</nb-card>

<ng-template #noTemplateYet>
  <div class="no_template_main d-flex justify-content-center align-items-center">
    <div class="row text-center">
      <div class="col-12 justify-content-center">
        <div class="mb-4">
          <img src="assets/images/exclamation.png" />
        </div>
        <div class="mb-4">
          <h6>No templates have been created yet!</h6>
          <p *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]">
            Click on the button to add a new template.
          </p>
        </div>
        <div class="mb-4 d-flex justify-content-center align-items-center">
          <button
            class="linear-mode-button me-3"
            nbButton
            status="secondary"
            size="small"
            [disabled]="loading"
            (click)="goToLandingPage()"
            type="button"
          >
            Back
          </button>
          <button
            class="linear-mode-button me-3"
            nbButton
            status="primary"
            size="small"
            [disabled]="loading"
            (click)="openModal(addTemplate, 'modal-md')"
            type="button"
            *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]"
          >
            Add New
          </button>
        </div>
        <div
          *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.FIELDTECH]"
          class="d-flex justify-content-center align-items-center"
        >
          <img class="me-1" src="assets/images/info_bulb.png" />
          <span class="me-2"> Not Sure How It Works? </span>
          <a href="javascript: void(0);" (click)="openModal(howToUseModal, 'modal-lg')">CHECK HERE</a>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #howToUseModal>
  <div class="alert-box">
    <div class="modal-header">
      <div class="top-info">
        <h6>Instructions of how to use QEST Forms</h6>
        <p class="mb-0 small-text">Please review the following steps to begin the custom form setup.</p>
      </div>
      <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="row">
        <div class="col-12 col-lg-8">
          <div class="video_player lg_screen position-relative">
            <div id="carouselModelIndicators" class="carousel slide" data-bs-ride="carousel">
              <div class="carousel-indicators">
                <button
                  type="button"
                  data-bs-target="#carouselModelIndicators"
                  data-bs-slide-to="0"
                  class="active"
                  aria-current="true"
                  aria-label="Slide 1"
                ></button>
                <button type="button" data-bs-target="#carouselModelIndicators" data-bs-slide-to="1" aria-label="Slide 2"></button>
                <button type="button" data-bs-target="#carouselModelIndicators" data-bs-slide-to="2" aria-label="Slide 3"></button>
              </div>
              <div class="carousel-inner">
                <div class="carousel-item active">
                  <iframe src="https://www.youtube.com/embed/tgbNymZ7vqy?autoplay=1&mute=1"> </iframe>
                </div>
                <div class="carousel-item">
                  <iframe src="https://www.youtube.com/embed/tgbNymZ7vqy?autoplay=1&mute=1"> </iframe>
                </div>
                <div class="carousel-item">
                  <iframe src="https://www.youtube.com/embed/tgbNymZ7vqy?autoplay=1&mute=1"> </iframe>
                </div>
              </div>
              <button class="carousel-control-prev" type="button" data-bs-target="#carouselModelIndicators" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
              </button>
              <button class="carousel-control-next" type="button" data-bs-target="#carouselModelIndicators" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
              </button>
            </div>
            <img class="expand-icon" src="assets/images/expand-icon.png" alt="" (click)="modalRef.hide(); expandView()" />
          </div>
        </div>
        <div class="col-12 col-lg-4">
          <div class="info-text">
            <h6>Create New Template</h6>
            <p class="mb-0">The operations/forms tab will be selected, directing the user to a drop-down window.</p>
            <p>Here, they can select between Forms or Templates.</p>
            <p class="mb-0">
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Est incidunt ratione provident suscipit excepturi ducimus, quia enim
              accusamus odit consectetur. Lorem ipsum dolor sit amet consectetur adipisicing elit. At dicta sunt, ad quam placeat assumenda
              aliquid impedit animi tempora labore.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #addTemplate>
  <div class="alert-box">
    <div class="modal-header">
      <h6 class="modal-title">Add Template</h6>
      <button type="button" class="close" aria-label="Close" (click)="onCancel()">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body">
      <form #templateForm="ngForm">
        <div class="row">
          <div class="col-12 col-md-6 mb-3">
            <div class="template-dropZone border" ngFileDragDrop (fileDropped)="getUpload($event)">
              <input type="file" multiple accept=".pdf" (change)="getUpload($event)" #pdfUploadInput />
              <img src="assets/images/uploadPdf.png" class="mb-2" />
              <h6 class="fw-bold">Upload PDF</h6>
              <label style="text-transform: none" class="small-text">Upload template .pdf </label>
            </div>
            <span class="text-danger" *ngIf="templateForm.touched && !customFormService.selectedFileForTemplate">
              PDF file is required.
            </span>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <div class="template-dropZone border">
              <img src="assets/images/createScratch.png" class="mb-2" />
              <h6 class="fw-bold">Create from scratch</h6>
              <span class="small-text">Phase 2 </span>
            </div>
          </div>
          <div class="col-12 mb-3">
            <input
              nbInput
              fullWidth
              name="fileName"
              id="input-name"
              class="form-control"
              [(ngModel)]="customFormService.templateName"
              #templateName="ngModel"
              placeholder="Template Name"
              required
            />
            <sfl-error-msg [control]="templateName" fieldName="Template Name"></sfl-error-msg>
          </div>
          <div class="col-12">
            <div class="row align-items-center">
              <div class="col-4">Template Type</div>
              <div class="col-8">
                <ng-select
                  name="templateType"
                  bindLabel="templateTypeName"
                  bindValue="templateTypeId"
                  [items]="templateTypesList"
                  notFoundText="No template Type Found"
                  placeholder="Select template Type"
                  [clearable]="false"
                  [(ngModel)]="customFormService.templateType"
                  #templateType="ngModel"
                  required
                >
                </ng-select>
                <sfl-error-msg [control]="templateType" fieldName="Template Type"></sfl-error-msg>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <div class="mb-4 d-flex justify-content-end align-items-center">
        <button
          class="linear-mode-button me-3"
          nbButton
          status="secondary"
          size="small"
          [disabled]="loading"
          (click)="onCancel()"
          type="button"
        >
          Cancel
        </button>
        <button
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="createTemplate(templateForm)"
          type="submit"
        >
          Create
        </button>
      </div>
    </div>
  </div>
</ng-template>
