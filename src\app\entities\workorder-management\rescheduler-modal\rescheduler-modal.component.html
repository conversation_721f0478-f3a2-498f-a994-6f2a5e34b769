<div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <div class="d-flex">
      <h6 class="modal-title ModalBody">Reschedule Workorder</h6>
    </div>
    <button type="button" class="close" aria-label="Close" (click)="onHide(false)">
      <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
    </button>
  </div>
  <div class="modal-body">
    <form
      name="addUpdateRescheduler"
      #addUpdateRescheduler="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="addUpdateRescheduler.valid && reschedule(successTemplate)"
    >
      <div class="row align-items-center">
        <div class="col-12">
          <label class="label">Current Scheduled Date:&nbsp; </label>
          <strong>{{ isWoPendingRescheduled || !currentSchedule ? '-' : (currentSchedule | date : 'MM/dd/yyyy') }}</strong>
        </div>
      </div>
      <div class="row align-items-center" *ngIf="isWoPendingRescheduled">
        <div class="col-12"><label class="label">Reschedule Status:&nbsp;</label> <strong>Pending Reschedule</strong></div>
      </div>
      <div class="row align-items-center">
        <div class="col-12">
          <label class="label" for="new-schedule-date"
            >New Scheduled Date<span *ngIf="isWoPendingRescheduled" class="ms-1 text-danger">*</span></label
          >
        </div>
        <div class="col-12 position-relative">
          <input
            class="form-control search-textbox new-schedule-date sfl-track-input"
            [nbDatepicker]="newScheduleDate"
            name="NewScheduleDate"
            #newScheduleDateField="ngModel"
            placeholder="Select Date"
            id="new-schedule-date"
            [(ngModel)]="rescheduleForm.scheduleDate"
            autocomplete="off"
            [required]="isWoPendingRescheduled"
            readonly
          />
          <span
            class="clear-icon position-absolute cursor-pointer"
            *ngIf="rescheduleForm?.scheduleDate"
            (click)="rescheduleForm.scheduleDate = null"
            >&times;</span
          >
          <sfl-error-msg
            [control]="newScheduleDateField"
            [isFormSubmitted]="addUpdateRescheduler?.submitted"
            fieldName="New Schedule Date"
          ></sfl-error-msg>
          <nb-datepicker #newScheduleDate [filter]="dateFilter"></nb-datepicker>
        </div>
      </div>

      <div class="row align-items-center">
        <div class="col-12">
          {{ workOrderData?.scheduleDate }}
          <label class="label" for="input-fieldTech">
            Field Tech
            <span class="ms-1 text-danger" *ngIf="workOrderData?.workOrderStatus === 'Field Work Complete'">*</span>
          </label>
          <ng-select
            id="input-fieldTech"
            name="FieldTech"
            #fieldTech="ngModel"
            [items]="primaryFieldtTechdata"
            [multiple]="true"
            [(ngModel)]="rescheduleForm.fieldTech"
            bindLabel="name"
            bindValue="id"
            notFoundText="No Field Tech Found"
            placeholder="Select Field Tech"
            [closeOnSelect]="true"
            [required]="workOrderData?.workOrderStatus === 'Field Work Complete'"
            appendTo="body"
          >
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" name="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
          <sfl-error-msg [control]="fieldTech" [isFormSubmitted]="addUpdateRescheduler?.submitted" fieldName="Field Tech"></sfl-error-msg>
        </div>
      </div>
      <div class="row align-items-center">
        <div class="col-12">
          <label class="label" for="input-reason">Reason<span *ngIf="!isWoPendingRescheduled" class="ms-1 text-danger">*</span></label>
          <ng-select
            id="input-reason"
            name="Reason"
            #reason="ngModel"
            [items]="categories"
            [(ngModel)]="rescheduleForm.resasonForReschedule"
            bindLabel="name"
            bindValue="id"
            notFoundText="No Reason Found"
            placeholder="Select a Reason"
            [closeOnSelect]="true"
            [required]="!isWoPendingRescheduled"
            appendTo="body"
          >
          </ng-select>
          <sfl-error-msg [control]="reason" [isFormSubmitted]="addUpdateRescheduler?.submitted" fieldName="Reason"></sfl-error-msg>
        </div>
      </div>
      <div class="row align-items-center">
        <div class="col-12">
          <label class="label" for="input-notes">Notes<span *ngIf="!isWoPendingRescheduled" class="ms-1 text-danger">*</span></label>
        </div>
        <div class="col-12">
          <textarea
            nbInput
            name="notes"
            #notes="ngModel"
            [required]="!isWoPendingRescheduled"
            id="input-notes"
            class="notes col-12 size-large"
            rows="4"
            [(ngModel)]="rescheduleForm.notes"
          ></textarea>
          <sfl-error-msg [control]="notes" [isFormSubmitted]="addUpdateRescheduler?.submitted" fieldName="Note"></sfl-error-msg>
        </div>
      </div>
      <div class="modal-footer">
        <button nbButton status="secondary" size="medium" type="button" class="float-end m-1" (click)="onHide(false)">Back</button>
        <button nbButton status="primary" size="medium" type="submit" class="float-end m-1">Save & Reschedule</button>
      </div>
    </form>
  </div>
</div>

<ng-template #successTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Workorder Reschedule Status</h4>
  </div>
  <div class="modal-body">
    <p>{{ bulkRescheduleResponseMessage }}</p>
    <div class="container">
      <div class="table-responsive">
        <table class="table year-site-table">
          <thead>
            <tr>
              <th>Customer</th>
              <th>Portfolio</th>
              <th>Site</th>
              <th>Workorder Number</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let res of bulkRescheduleResponse">
              <td>{{ res.customerName }}</td>
              <td>{{ res.portfolioName }}</td>
              <td>{{ res.siteName }}</td>
              <td>{{ res.workOrderNumber }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="modal-footer ModalFooter">
        <div class="row">
          <div class="col-md-12">
            <button nbButton size="small" (click)="modalRef?.hide()">Ok</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>
