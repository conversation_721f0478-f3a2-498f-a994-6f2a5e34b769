export class PmIssueNCItems {
  public id = 0;
  public componentId = null;
  public componentStr = '';
  public issueObservation = '';
  public isActive = true;
}
export class PmIssueActionNCItems {
  public id = 0;
  public componentId = null;
  public componentStr = '';
  public issueObservation = '';
  public isActive = true;
}
export class NcComponentListing {
  abbreviation: string;
  categoryId: number | null;
  categoryName: string | null;
  evId: number;
  id: number;
  isActive: boolean;
  isSelected: boolean;
  name: string;
  componentName: string;
  order: number;
  sectionAbbreviation: string;
  sectionDetails: any[];
  sectionId: number;
  sectionName: string;
  sectionOverview: any[];
  subSections: any[];
}

export class PmActionRecommendationListResponse {
  id: number = 0;
  componentId: number = null;
  componentStr: string = '';
  issueObservationId: number = 0;
  issueObservationStr: string = '';
  actionRecommendation: string = '';
  isActive: boolean = true;
  isIssueActive: boolean = true;
}
export class PmIssueObservationListResponse {
  id: number = 0;
  componentId: number = null;
  componentStr: string = '';
  issueObservation: string = '';
  isActive: boolean = true;
  isComponentActive: boolean = true;
}

export enum ACCORDION_ITEMS {
  COMPONENT = 'Component',
  ISSUE = 'Issue/Observation',
  ACTION = 'Action/Recommendation'
}

export enum ADD_EDIT_COPY_MODE {
  ADD = 'ADD',
  EDIT = 'EDIT',
  COPY = 'COPY'
}

export class CmpFilterModel {
  itemsCount = 0;
  sortBy = '';
  page = 0;
  direction = 'desc';
  searchBy = '';
}
export class IssueFilterModel {
  itemsCount = 0;
  sortBy = '';
  page = 0;
  direction = 'desc';
  searchBy = '';
}
export class ActionFilterModel {
  itemsCount = 0;
  sortBy = '';
  page = 0;
  direction = 'desc';
  searchBy = '';
}
