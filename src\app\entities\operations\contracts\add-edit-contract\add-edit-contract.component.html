<nb-card class="ticket-detail ticketDetailSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header class="d-flex align-items-center">
    <h6 class="w-100">{{ title }}</h6>
    <div class="d-flex align-items-center w-100">
      <div class="ms-auto d-flex button_list">
        <div>
          <button nbButton status="basic" type="button" size="medium" (click)="onBack()" class="ms-1 ms-sm-2">
            <span class="d-none d-lg-inline-block">Back</span> <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <form
      name="contractForm"
      #contractForm="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="contractForm?.form?.valid && saveContract()"
    >
      <div class="row">
        <div class="col-12">
          <div class="form-group row">
            <div class="col-12">
              <table class="table two-header table-bordered table-header-rotated">
                <thead>
                  <tr>
                    <th scope="col" class="text-start" id="empty" colspan="7"></th>
                    <th scope="col" class="text-start" id="material" colspan="4">
                      <div class="d-flex align-items-center">
                        <span class="me-2">Material</span>
                      </div>
                    </th>
                  </tr>
                  <tr>
                    <th scope="col" class="text-start" id="contractname">
                      <div class="d-flex align-items-center"><span class="me-2">Contract Name</span></div>
                    </th>
                    <th scope="col" class="text-start" id="startdate">
                      <div class="d-flex align-items-center"><span class="me-2">Start Date</span></div>
                    </th>
                    <th scope="col" class="text-start" id="enddate">
                      <div class="d-flex align-items-center"><span class="me-2">End Date</span></div>
                    </th>
                    <th scope="col" class="text-start" id="escalationrate">
                      <div class="d-flex align-items-center"><span class="me-2">Escalation Rate</span></div>
                    </th>
                    <th scope="col" class="text-start" id="overtimerate">
                      <div class="d-flex align-items-center"><span class="me-2">Overtime Rate</span></div>
                    </th>
                    <th scope="col" class="text-start" id="weekendrate">
                      <div class="d-flex align-items-center"><span class="me-2">Weekend Rate</span></div>
                    </th>
                    <th scope="col" class="text-start" id="holidayrate">
                      <div class="d-flex align-items-center"><span class="me-2">Holiday Rate</span></div>
                    </th>
                    <th scope="col" class="text-start" id="rate1">
                      <div class="d-flex align-items-center">
                        <span class="me-2">Rate 1</span>
                      </div>
                    </th>
                    <th scope="col" class="text-start" id="rate2">
                      <div class="d-flex align-items-center"><span class="me-2">Rate2</span></div>
                    </th>
                    <th scope="col" class="text-start" id="threshold">
                      <div class="d-flex align-items-center"><span class="me-2">Threshold</span></div>
                    </th>
                    <th scope="col" class="text-start" id="dispatchminhours">
                      <div class="d-flex align-items-center"><span class="me-2">Dispatch Minimum Hours </span></div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <!-- <tr>
                            <td class="text-center" colspan="8">No Reschedule Records Found</td>
                          </tr> -->
                  <tr>
                    <td data-title="Contract Name" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.contractName"
                        #contractName="ngModel"
                        name="contractName"
                        placeholder="Contract Name"
                        id="input-contractName"
                        class="form-control input-full-width"
                        contenteditable="true"
                        required
                      />
                      <sfl-error-msg [control]="contractName" [isFormSubmitted]="contractForm?.submitted" fieldName="Name"></sfl-error-msg>
                    </td>
                    <td data-title="Start Date" class="text-start">
                      <input
                        nbInput
                        name="startDate"
                        placeholder="Start Date"
                        autocomplete="off"
                        #startDateModel="ngModel"
                        [(ngModel)]="contractsData.startDate"
                        class="form-control input-full-width"
                        [nbDatepicker]="startDate"
                        (ngModelChange)="onChangeStartDate()"
                        required
                      />
                      <nb-datepicker #startDate></nb-datepicker>
                      <sfl-error-msg
                        [control]="startDateModel"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Start Date"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="End Date" class="text-start">
                      <input
                        nbInput
                        name="endDate"
                        #endDateModel="ngModel"
                        [(ngModel)]="contractsData.endDate"
                        placeholder="End Date"
                        class="form-control input-full-width"
                        [nbDatepicker]="endDate"
                        readonly
                        required
                        autocomplete="off"
                      />
                      <nb-datepicker #endDate [min]="contractsData.startDate"></nb-datepicker>
                      <sfl-error-msg
                        [control]="endDateModel"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="End date"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="Escalation Rate" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.escalationRate"
                        #escalationRate="ngModel"
                        name="escalationRate"
                        placeholder="Escalation Rate"
                        id="input-escalationRate"
                        pattern=".*\S.*"
                        class="form-control input-full-width percent nospin"
                        contenteditable="true"
                        sflNumbersOnly
                        type="number"
                        step="0.01"
                      />
                      <sfl-error-msg
                        [control]="escalationRate"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Escalation Rate"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="Overtime Rate" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.overtimeRate"
                        #overtimeRate="ngModel"
                        name="overtimeRate"
                        placeholder="Overtime Rate"
                        id="input-overtimeRate"
                        pattern=".*\S.*"
                        class="form-control input-full-width nospin"
                        contenteditable="true"
                        sflNumbersOnly
                        type="number"
                      />
                      <sfl-error-msg
                        [control]="overtimeRate"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Overtime Rate"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="Weekend Rate" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.weekendRate"
                        #weekendRate="ngModel"
                        name="weekendRate"
                        placeholder="Weekend Rate"
                        id="input-weekendRate"
                        pattern=".*\S.*"
                        class="form-control input-full-width nospin"
                        contenteditable="true"
                        sflNumbersOnly
                        type="number"
                      />
                      <sfl-error-msg
                        [control]="weekendRate"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Weekend Rate"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="Holiday Rate" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.holidayRate"
                        #holidayRate="ngModel"
                        name="holidayRate"
                        placeholder="Holiday Rate"
                        id="input-holidayRate"
                        pattern=".*\S.*"
                        class="form-control input-full-width nospin"
                        contenteditable="true"
                        sflNumbersOnly
                        type="number"
                      />
                      <sfl-error-msg
                        [control]="holidayRate"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Holiday Rate"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="Rate1" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.materialRate1"
                        #materialRate1="ngModel"
                        name="materialRate1"
                        placeholder="Material Rate1"
                        id="input-materialRate1"
                        pattern=".*\S.*"
                        class="form-control input-full-width nospin"
                        contenteditable="true"
                        sflNumbersOnly
                        type="number"
                      />
                      <sfl-error-msg
                        [control]="materialRate1"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Rate1"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="Rate2" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.materialRate2"
                        #materialRate2="ngModel"
                        name="materialRate2"
                        placeholder="Material Rate2"
                        id="input-materialRate2"
                        pattern=".*\S.*"
                        class="form-control input-full-width nospin"
                        contenteditable="true"
                        sflNumbersOnly
                        type="number"
                      />
                      <sfl-error-msg
                        [control]="materialRate2"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Rate2"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="Threshold" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.materialThreshold"
                        #materialThreshold="ngModel"
                        name="materialThreshold"
                        placeholder="Threshold"
                        id="input-materialThreshold"
                        pattern=".*\S.*"
                        class="form-control input-full-width nospin"
                        contenteditable="true"
                        sflNumbersOnly
                        [required]="contractsData.materialRate2"
                        type="number"
                      />
                      <sfl-error-msg
                        [control]="materialThreshold"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Threshold"
                      ></sfl-error-msg>
                    </td>
                    <td data-title="Dispatch Minimum Hours" class="text-start">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="contractsData.dispatchMinimumHours"
                        #dispatchMinimumHours="ngModel"
                        name="dispatchMinimumHours"
                        placeholder="Dispatch Minimum Hours"
                        id="input-dispatchMinimumHours"
                        pattern=".*\S.*"
                        class="form-control input-full-width nospin"
                        contenteditable="true"
                        sflNumbersOnly
                        type="number"
                      />
                      <sfl-error-msg
                        [control]="dispatchMinimumHours"
                        [isFormSubmitted]="contractForm?.submitted"
                        fieldName="Dispatch Minimum Hours"
                      ></sfl-error-msg>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="row">
                <div class="col-12">
                  <div class="form-group row">
                    <div class="col-12">
                      <h6>Base Rates</h6>
                      <table class="table two-header table-bordered table-header-rotated">
                        <thead>
                          <tr>
                            <th scope="col" class="text-start" id="technician">
                              <div class="d-flex align-items-center"><span class="me-2">Technician</span></div>
                            </th>
                            <th scope="col" class="text-start" id="electrician">
                              <div class="d-flex align-items-center"><span class="me-2">Electrician</span></div>
                            </th>
                            <th scope="col" class="text-start" id="mvtechnician">
                              <div class="d-flex align-items-center"><span class="me-2">MV Technician</span></div>
                            </th>
                            <th scope="col" class="text-start" id="besstechnician">
                              <div class="d-flex align-items-center"><span class="me-2">BESS Technician </span></div>
                            </th>
                            <th scope="col" class="text-start" id="administrative">
                              <div class="d-flex align-items-center"><span class="me-2">Administrative</span></div>
                            </th>
                            <th scope="col" class="text-start" id="engineering">
                              <div class="d-flex align-items-center"><span class="me-2">Engineering</span></div>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td data-title="Technician" class="text-start">
                              <input
                                nbInput
                                fullWidth
                                [(ngModel)]="contractsData.baseRates.technicianRate"
                                #technicianRate="ngModel"
                                name="technicianRate"
                                placeholder="Technician Base Rate"
                                id="input-technicianRate"
                                pattern=".*\S.*"
                                class="form-control input-full-width nospin"
                                contenteditable="true"
                                sflNumbersOnly
                                required
                                type="number"
                              />
                              <sfl-error-msg
                                [control]="technicianRate"
                                [isFormSubmitted]="contractForm?.submitted"
                                fieldName="Technician Base Rate"
                              ></sfl-error-msg>
                            </td>
                            <td data-title="Electrician" class="text-start">
                              <input
                                nbInput
                                fullWidth
                                [(ngModel)]="contractsData.baseRates.electricianRate"
                                #electricianRate="ngModel"
                                name="electricianRate"
                                placeholder="Electrician Base Rate"
                                id="input-electricianRate"
                                pattern=".*\S.*"
                                class="form-control input-full-width nospin"
                                contenteditable="true"
                                sflNumbersOnly
                                required
                                type="number"
                              />
                              <sfl-error-msg
                                [control]="electricianRate"
                                [isFormSubmitted]="contractForm?.submitted"
                                fieldName="Electrician Base Rate"
                              ></sfl-error-msg>
                            </td>
                            <td data-title="MV Technician" class="text-start">
                              <input
                                nbInput
                                fullWidth
                                [(ngModel)]="contractsData.baseRates.mvTechnicianRate"
                                #mvTechnicianRate="ngModel"
                                name="mvTechnicianRate"
                                placeholder="MV Technician Base Rate"
                                id="input-mvTechnicianRate"
                                pattern=".*\S.*"
                                class="form-control input-full-width nospin"
                                contenteditable="true"
                                sflNumbersOnly
                                required
                                type="number"
                              />
                              <sfl-error-msg
                                [control]="mvTechnicianRate"
                                [isFormSubmitted]="contractForm?.submitted"
                                fieldName="MV Technician Base Rate"
                              ></sfl-error-msg>
                            </td>
                            <td data-title="BESS Technician" class="text-start">
                              <input
                                nbInput
                                fullWidth
                                [(ngModel)]="contractsData.baseRates.bessTechnicianRate"
                                #bessTechnicianRate="ngModel"
                                name="bessTechnicianRate"
                                placeholder="BESS Technician Base Rate"
                                id="input-bessTechnicianRate"
                                pattern=".*\S.*"
                                class="form-control input-full-width nospin"
                                contenteditable="true"
                                sflNumbersOnly
                                required
                                type="number"
                              />
                              <sfl-error-msg
                                [control]="bessTechnicianRate"
                                [isFormSubmitted]="contractForm?.submitted"
                                fieldName="BESS Technician Base Rate"
                              ></sfl-error-msg>
                            </td>
                            <td data-title="Administrative" class="text-start">
                              <input
                                nbInput
                                fullWidth
                                [(ngModel)]="contractsData.baseRates.administrativeRate"
                                #administrativeRate="ngModel"
                                name="administrativeRate"
                                placeholder="Administrative Base Rate"
                                id="input-administrativeRate"
                                pattern=".*\S.*"
                                class="form-control input-full-width nospin"
                                contenteditable="true"
                                sflNumbersOnly
                                required
                                type="number"
                              />
                              <sfl-error-msg
                                [control]="administrativeRate"
                                [isFormSubmitted]="contractForm?.submitted"
                                fieldName="Administrative Base Rate"
                              ></sfl-error-msg>
                            </td>
                            <td data-title="Engineering" class="text-start">
                              <input
                                nbInput
                                fullWidth
                                [(ngModel)]="contractsData.baseRates.engineeringRate"
                                #engineeringRate="ngModel"
                                name="engineeringRate"
                                placeholder="Engineering Base Rate"
                                id="input-engineeringRate"
                                pattern=".*\S.*"
                                class="form-control input-full-width nospin"
                                contenteditable="true"
                                sflNumbersOnly
                                required
                                type="number"
                              />
                              <sfl-error-msg
                                [control]="engineeringRate"
                                [isFormSubmitted]="contractForm?.submitted"
                                fieldName="Engineering Base Rate"
                              ></sfl-error-msg>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row" *appHasPermission="[roleType.ADMIN, roleType.SUPPORT]">
                <div class="col-12">
                  <div class="form-group row">
                    <div class="col-12">
                      <h6>Contract Link</h6>
                      <div class="col-4">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="contractsData.contractLinkUrl"
                          #contractLink="ngModel"
                          name="contractLink"
                          placeholder="Contract Link"
                          id="input-Contract-Link"
                          class="form-control input-full-width nospin"
                          contenteditable="true"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row mb-3">
                <div class="col-12">
                  <button
                    nbButton
                    status="primary"
                    size="medium"
                    type="submit"
                    id="siteSubmit"
                    (click)="contractForm.onSubmit()"
                    class="float-end m-1"
                    *appHasPermission="[roleType.ADMIN, roleType.SUPPORT]"
                  >
                    Save
                  </button>
                  <!-- <button
                    nbButton
                    status="primary"
                    size="medium"
                    type="button"
                    id="siteSubmit"
                    (click)="previewRates()"
                    class="float-end m-1"
                  >
                    Preview Rates
                  </button> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
    <div class="fade-contract-rates" *ngIf="showRates">
      <nb-accordion class="mb-3">
        <nb-accordion-item [expanded]="true">
          <nb-accordion-item-header class="bg-light">
            <div class="col-md-6">
              <h6>Rates</h6>
            </div>
          </nb-accordion-item-header>
          <nb-accordion-item-body>
            <h6 class="text-center">Rates by month</h6>
            <table class="table table-bordered table-header-rotated">
              <thead>
                <tr>
                  <th></th>
                  <th scope="col" class="text-start" id="technician">
                    <div class="d-flex align-items-center"><span class="me-2">Technician</span></div>
                  </th>
                  <th scope="col" class="text-start" id="electrician">
                    <div class="d-flex align-items-center"><span class="me-2">Electrician</span></div>
                  </th>
                  <th scope="col" class="text-start" id="mvtechnician">
                    <div class="d-flex align-items-center"><span class="me-2">MV Technician</span></div>
                  </th>
                  <th scope="col" class="text-start" id="besstechnician">
                    <div class="d-flex align-items-center"><span class="me-2">BESS Technician </span></div>
                  </th>
                  <th scope="col" class="text-start" id="administrative">
                    <div class="d-flex align-items-center"><span class="me-2">Administrative</span></div>
                  </th>
                  <th scope="col" class="text-start" id="engineering">
                    <div class="d-flex align-items-center"><span class="me-2">Engineering</span></div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- <tr>
                                  <td class="text-center" colspan="8">No Reschedule Records Found</td>
                                </tr> -->
                <!-- rates with escalation calculation -->
                <tr>
                  <td></td>
                  <td data-title="Technician" class="text-start">
                    {{ contractsData.ratesByMonth.technician.escalationRate | currency }}
                  </td>
                  <td data-title="Electrician" class="text-start">
                    {{ contractsData.ratesByMonth.electrician.escalationRate | currency }}
                  </td>
                  <td data-title="MV Technician" class="text-start">
                    {{ contractsData.ratesByMonth.mvTechnician.escalationRate | currency }}
                  </td>
                  <td data-title="BESS Technician" class="text-start">
                    {{ contractsData.ratesByMonth.bessTechnician.escalationRate | currency }}
                  </td>
                  <td data-title="Administrative" class="text-start">
                    {{ contractsData.ratesByMonth.administrative.escalationRate | currency }}
                  </td>
                  <td data-title="Engineering" class="text-start">
                    {{ contractsData.ratesByMonth.engineering.escalationRate | currency }}
                  </td>
                </tr>
                <!-- Overtime calculation -->
                <tr>
                  <td>Overtime</td>
                  <td data-title="Technician" class="text-start">
                    {{ contractsData.ratesByMonth.technician.overtimeRate | currency }}
                  </td>
                  <td data-title="Electrician" class="text-start">
                    {{ contractsData.ratesByMonth.electrician.overtimeRate | currency }}
                  </td>
                  <td data-title="MV Technician" class="text-start">
                    {{ contractsData.ratesByMonth.mvTechnician.overtimeRate | currency }}
                  </td>
                  <td data-title="BESS Technician" class="text-start">
                    {{ contractsData.ratesByMonth.bessTechnician.overtimeRate | currency }}
                  </td>
                  <td data-title="Administrative" class="text-start">
                    {{ contractsData.ratesByMonth.administrative.overtimeRate | currency }}
                  </td>
                  <td data-title="Engineering" class="text-start">
                    {{ contractsData.ratesByMonth.engineering.overtimeRate | currency }}
                  </td>
                </tr>
                <!-- Weekend calculation -->
                <tr>
                  <td>Weekend</td>
                  <td data-title="Technician" class="text-start">
                    {{ contractsData.ratesByMonth.technician.weekendRate | currency }}
                  </td>
                  <td data-title="Electrician" class="text-start">
                    {{ contractsData.ratesByMonth.electrician.weekendRate | currency }}
                  </td>
                  <td data-title="MV Technician" class="text-start">
                    {{ contractsData.ratesByMonth.mvTechnician.weekendRate | currency }}
                  </td>
                  <td data-title="BESS Technician" class="text-start">
                    {{ contractsData.ratesByMonth.bessTechnician.weekendRate | currency }}
                  </td>
                  <td data-title="Administrative" class="text-start">
                    {{ contractsData.ratesByMonth.administrative.weekendRate | currency }}
                  </td>
                  <td data-title="Engineering" class="text-start">
                    {{ contractsData.ratesByMonth.engineering.weekendRate | currency }}
                  </td>
                </tr>
                <!-- Holiday calculation -->
                <tr>
                  <td>Holiday</td>
                  <td data-title="Technician" class="text-start">
                    {{ contractsData.ratesByMonth.technician.holidayRate | currency }}
                  </td>
                  <td data-title="Electrician" class="text-start">
                    {{ contractsData.ratesByMonth.electrician.holidayRate | currency }}
                  </td>
                  <td data-title="MV Technician" class="text-start">
                    {{ contractsData.ratesByMonth.mvTechnician.holidayRate | currency }}
                  </td>
                  <td data-title="BESS Technician" class="text-start">
                    {{ contractsData.ratesByMonth.bessTechnician.holidayRate | currency }}
                  </td>
                  <td data-title="Administrative" class="text-start">
                    {{ contractsData.ratesByMonth.administrative.holidayRate | currency }}
                  </td>
                  <td data-title="Engineering" class="text-start">
                    {{ contractsData.ratesByMonth.engineering.holidayRate | currency }}
                  </td>
                </tr>
              </tbody>
            </table>

            <h6 class="text-center">Contract Months Rates by Type</h6>
            <div class="col-8 col-sm-6 col-md-3 col-lg-3 col-xl-3 pe-sm-0 mb-2">
              <label class="label" for="customer">Select a Type</label>
              <ng-select
                id="type-drop-down"
                class="sfl-track-dropdown"
                name="type"
                bindValue="key"
                bindLabel="name"
                [items]="types"
                [(ngModel)]="selectedType"
                notFoundText="No Types Found"
                placeholder="Select a Type"
                appendTo="body"
                [clearable]="false"
              >
              </ng-select>
            </div>
            <table class="table table-bordered table-header-rotated">
              <thead>
                <tr>
                  <th></th>
                  <th scope="col" class="text-start" id="january">
                    <div class="d-flex align-items-center"><span class="me-2">January</span></div>
                  </th>
                  <th scope="col" class="text-start" id="february">
                    <div class="d-flex align-items-center"><span class="me-2">February</span></div>
                  </th>
                  <th scope="col" class="text-start" id="march">
                    <div class="d-flex align-items-center"><span class="me-2">March</span></div>
                  </th>
                  <th scope="col" class="text-start" id="april">
                    <div class="d-flex align-items-center"><span class="me-2">April</span></div>
                  </th>
                  <th scope="col" class="text-start" id="may">
                    <div class="d-flex align-items-center"><span class="me-2">May</span></div>
                  </th>
                  <th scope="col" class="text-start" id="june">
                    <div class="d-flex align-items-center"><span class="me-2">June</span></div>
                  </th>
                  <th scope="col" class="text-start" id="july">
                    <div class="d-flex align-items-center"><span class="me-2">July</span></div>
                  </th>
                  <th scope="col" class="text-start" id="august">
                    <div class="d-flex align-items-center"><span class="me-2">August</span></div>
                  </th>
                  <th scope="col" class="text-start" id="september">
                    <div class="d-flex align-items-center"><span class="me-2">September</span></div>
                  </th>
                  <th scope="col" class="text-start" id="october">
                    <div class="d-flex align-items-center"><span class="me-2">October</span></div>
                  </th>
                  <th scope="col" class="text-start" id="november">
                    <div class="d-flex align-items-center"><span class="me-2">November</span></div>
                  </th>
                  <th scope="col" class="text-start" id="december">
                    <div class="d-flex align-items-center"><span class="me-2">December</span></div>
                  </th>
                </tr>
              </thead>
              <tbody class="months">
                <!-- <tr>
                                  <td class="text-center" colspan="8">No Reschedule Records Found</td>
                                </tr> -->
                <!-- rates with escalation calculation -->
                <ng-container *ngFor="let monthRates of contractsData.monthsRatesByType">
                  <ng-container *ngIf="monthRates.type === selectedType">
                    <tr *ngFor="let month of monthRates.data">
                      <td class="year">{{ month.year }}</td>
                      <td class="jan">{{ month.months.january ? (month.months.january | currency) : '-' }}</td>
                      <td class="feb">{{ month.months.february ? (month.months.february | currency) : '-' }}</td>
                      <td class="march">{{ month.months.march ? (month.months.march | currency) : '-' }}</td>
                      <td class="apr">{{ month.months.april ? (month.months.april | currency) : '-' }}</td>
                      <td class="may">{{ month.months.may ? (month.months.may | currency) : '-' }}</td>
                      <td class="jun">{{ month.months.june ? (month.months.june | currency) : '-' }}</td>
                      <td class="jul">{{ month.months.july ? (month.months.july | currency) : '-' }}</td>
                      <td class="aug">{{ month.months.august ? (month.months.august | currency) : '-' }}</td>
                      <td class="sep">{{ month.months.september ? (month.months.september | currency) : '-' }}</td>
                      <td class="oct">{{ month.months.october ? (month.months.october | currency) : '-' }}</td>
                      <td class="nov">{{ month.months.november ? (month.months.november | currency) : '-' }}</td>
                      <td class="dec">{{ month.months.december ? (month.months.december | currency) : '-' }}</td>
                    </tr>
                  </ng-container>
                </ng-container>
              </tbody>
            </table>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
    </div>
  </nb-card-body>
</nb-card>
