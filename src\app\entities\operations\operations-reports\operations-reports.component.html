<div class="row">
  <div class="col-12 col-xl-2 pe-0">
    <nb-card>
      <nb-card-body>
        <div class="flex side-report-menu">
          <ul class="list-group">
            <li
              class="list-group-item"
              *ngFor="let menu of operationsReportsMenu"
              [routerLink]="menu.routerLink"
              routerLinkActive="active-report"
              (click)="operationsReportsService.selectedDashboard = null"
            >
              <span>{{ menu.label }}</span>
            </li>
          </ul>

          <hr />
          <div class="position-relative custom-dashboard-panel-height">
            <div class="custom-dashboard-button mb-3">
              <button nbButton status="primary" size="medium" id="deviceSubmit" type="submit" (click)="createCustomDashboard()">
                Create Dashboard
              </button>
            </div>
            <ng-container *ngIf="operationsReportsService.dashboardList?.length">
              <p class="side-panel-heading"><strong>Custom Dashboards</strong></p>
              <ul class="list-group">
                <li
                  class="list-group-item"
                  *ngFor="let dashboard of operationsReportsService.dashboardList"
                  [class.active-report]="dashboard?.id === operationsReportsService?.selectedDashboard?.id"
                  (click)="onCustomDashboardClick(dashboard)"
                >
                  <span>{{ dashboard.menuTitle }}</span>
                </li>
              </ul>
            </ng-container>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>
  <div class="col-12 col-xl-10 main-content">
    <router-outlet></router-outlet>
  </div>
</div>
