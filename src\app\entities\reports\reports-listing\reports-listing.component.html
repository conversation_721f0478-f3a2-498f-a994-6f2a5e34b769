<nb-card class="reportsSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header class="d-flex align-items-center">
    <h6 class="w-100">Reports</h6>
    <div class="d-flex align-items-center w-100">
      <div class="ms-auto d-flex button_list">
        <button
          class="linear-mode-button ms-1 ms-sm-2"
          (click)="onDownload()"
          nbButton
          status="primary"
          size="medium"
          [disabled]="loading || !reportIds.length"
          type="button"
        >
          <span class="d-none d-xl-inline-block">Download Selected Report</span>
          <i class="d-inline-block d-xl-none fa fa-file-download download_icon"></i>
        </button>
        <button
          class="linear-mode-button ms-1 ms-sm-2"
          (click)="onLink()"
          nbButton
          status="primary"
          size="medium"
          type="button"
          *ngIf="linking === false"
        >
          <span class="d-none d-xl-inline-block">Batch Report Request</span>
          <i class="d-inline-block d-xl-none fa-solid fa-book-open"></i>
        </button>
        <button
          class="linear-mode-button ms-1 ms-sm-2"
          nbButton
          *ngIf="
            checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER]) && !filterModel.isArchive
          "
          status="primary"
          size="medium"
          [disabled]="loading"
          (click)="bulkArchive()"
          type="button"
        >
          <span class="d-none d-xl-inline-block">Archive All</span>
          <i class="d-inline-block d-xl-none fa-sharp fa-solid fa-box-archive"></i>
        </button>
        <button
          class="linear-mode-button ms-1 ms-sm-2"
          nbButton
          *ngIf="
            checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER]) && filterModel.isArchive
          "
          status="primary"
          size="medium"
          [disabled]="loading"
          (click)="bulkArchive()"
          type="button"
        >
          <span class="d-none d-xl-inline-block">Unarchive All</span>
          <i class="d-inline-block d-xl-none fa-solid fa-box-archive"></i>
        </button>
        <button
          class="linear-mode-button ms-1 ms-sm-2"
          nbButton
          status="primary"
          size="medium"
          [disabled]="loading"
          (click)="addNCReport()"
          type="button"
          *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
        >
          <span class="d-none d-xl-inline-block">Export Non-Conformance</span>
          <i class="d-inline-block d-xl-none fa fa-file-export"></i>
        </button>
        <button
          class="linear-mode-button ms-1 ms-sm-2"
          (click)="Back()"
          nbButton
          status="primary"
          size="medium"
          type="button"
          *ngIf="linking === true"
        >
          <span class="d-none d-xl-inline-block">Back</span>
          <i class="d-inline-block d-xl-none fa-solid fa-arrow-left"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body style="overflow: inherit !important">
    <div class="form-control-group">
      <div class="row">
        <div class="col-12 reportsFilter appFilter mb-2">
          <sfl-filter
            *ngIf="linking === false"
            [filterDetails]="filterDetails"
            (refreshList)="refreshList($event)"
            (clearParentList)="reportsData = []"
            (refreshTableHeight)="this.isFilterDisplay = $event"
            [addReportTypeJHA]="true"
          ></sfl-filter>
        </div>
      </div>
    </div>
    <div *ngIf="linking === false" class="form-control-group">
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Report List">
          <thead>
            <tr>
              <th scope="col" class="text-center">
                <nb-checkbox (change)="selectDeselectAll()" [checked]="selectAllCheckbox" [(ngModel)]="selectAllCheckbox"></nb-checkbox>
              </th>
              <th scope="col" (click)="sort('CustomerPortfolio', sortOptionList['CustomerPortfolio'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Customer (Portfolio)</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['CustomerPortfolio'] === 'desc',
                      'fa-arrow-down': sortOptionList['CustomerPortfolio'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'CustomerPortfolio'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('SiteName', sortOptionList['SiteName'])" id="siteName">
                <div class="d-flex align-items-center">
                  <span class="me-2">Site</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['SiteName'] === 'desc',
                      'fa-arrow-down': sortOptionList['SiteName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'SiteName'
                    }"
                  ></span>
                </div>
              </th>
              <th id="workOrder">Work Order</th>
              <th id="workOrder">Report Type</th>
              <th id="workOrder">Year</th>
              <th class="text-start" id="totalReports" style="display: none">
                <div>Total Reports</div>
              </th>
              <th id="uploadDate" (click)="sort('Year', sortOptionList['Year'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Last Uploaded</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Year'] === 'desc',
                      'fa-arrow-down': sortOptionList['Year'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Year'
                    }"
                  ></span>
                </div>
              </th>
              <ng-container *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                <th id="regionName" (click)="sort('RegionName', sortOptionList['RegionName'])">
                  <div class="d-flex align-items-center">
                    <span class="me-2">Region</span>
                    <span
                      class="fa cursor-pointer ms-auto"
                      [ngClass]="{
                        'fa-arrow-up': sortOptionList['RegionName'] === 'desc',
                        'fa-arrow-down': sortOptionList['RegionName'] === 'asc',
                        'icon-selected': filterModel.sortBy === 'RegionName'
                      }"
                    ></span>
                  </div>
                </th>
                <th id="subRegionName" (click)="sort('SubRegionName', sortOptionList['SubRegionName'])">
                  <div class="d-flex align-items-center">
                    <span class="me-2">Subregion</span>
                    <span
                      class="fa cursor-pointer ms-auto"
                      [ngClass]="{
                        'fa-arrow-up': sortOptionList['SubRegionName'] === 'desc',
                        'fa-arrow-down': sortOptionList['SubRegionName'] === 'asc',
                        'icon-selected': filterModel.sortBy === 'SubRegionName'
                      }"
                    ></span>
                  </div>
                </th>
              </ng-container>
              <th class="text-center" id="actions">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let sitevisitReportData of reportsData
                  | paginate : { itemsPerPage: filterModel.itemsCount, currentPage: currentPage, totalItems: total }
              "
            >
              <td data-title="Select" class="text-center">
                <nb-checkbox
                  [disabled]="!sitevisitReportData.isCompleted"
                  (change)="selectReport($event, sitevisitReportData.reportId)"
                  [checked]="sitevisitReportData.isSelected"
                  [(ngModel)]="sitevisitReportData.isSelected"
                ></nb-checkbox>
              </td>
              <td data-title="Customer (Portfolio)" class="td-custom-width">{{ sitevisitReportData?.customerPortfolio }}</td>
              <td
                data-title="Site"
                class="text-truncate td-custom-width"
                nbTooltip="{{ sitevisitReportData?.siteName }}"
                nbTooltipPlacement="top"
                nbTooltipStatus="primary"
              >
                {{ sitevisitReportData?.siteName }}
              </td>
              <td data-title="Work Order" class="report-workOrder">{{ sitevisitReportData?.workorderName }}</td>
              <td data-title="Report Type">{{ sitevisitReportData?.reportType }}</td>
              <td data-title="Year">{{ sitevisitReportData?.year }}</td>
              <td data-title="Total Reports" class="text-center" style="display: none">{{ sitevisitReportData?.reportCount }}</td>
              <td data-title="Last Uploaded" style="min-width: 105px">{{ sitevisitReportData?.createdDate | date : fullDateFormat }}</td>
              <ng-container *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                <td data-title="Region" style="min-width: 105px">{{ sitevisitReportData?.regionName }}</td>
                <td data-title="Subregion" style="min-width: 105px">{{ sitevisitReportData?.subRegionName }}</td>
              </ng-container>
              <td data-title="Action" class="text-end report-action" *ngIf="!viewdeletetedbutton">
                <div class="d-md-flex justify-content-end">
                  <a
                    class="px-2 listgrid-icon"
                    *ngIf="
                      checkAuthorisationsFn([roleType.CUSTOMER]) &&
                      !sitevisitReportData?.isFromMobileUploaded &&
                      (sitevisitReportData?.reportType === 'MVPM' ||
                        sitevisitReportData?.reportType === 'SV' ||
                        sitevisitReportData?.reportType === 'VGT')
                    "
                    (click)="viewReportDetails(sitevisitReportData?.workorderId, template)"
                  >
                    <em class="fa fa-eye" nbTooltip="View Detail" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-white icon-color"
                    *ngIf="!sitevisitReportData?.isFromMobileUploaded && sitevisitReportData?.reportType === 'SV' && viewArchUnarchbutton"
                    [routerLink]="['/entities/reports/sitevisits/edit/' + sitevisitReportData?.id, viewdeletetedbutton]"
                  >
                    <em class="fa fa-eye" nbTooltip="View Detail" nbTooltipPlacement="top" nbTooltipStatus="secondary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-white icon-color"
                    *ngIf="!sitevisitReportData?.isFromMobileUploaded && sitevisitReportData?.reportType === 'VGT' && viewArchUnarchbutton"
                    [routerLink]="['/entities/reports/vegetation/edit/' + sitevisitReportData?.id, viewdeletetedbutton]"
                  >
                    <em class="fa fa-eye" nbTooltip="View Detail" nbTooltipPlacement="top" nbTooltipStatus="secondary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-white icon-color"
                    *ngIf="sitevisitReportData?.reportType === 'JHA' && viewArchUnarchbutton"
                    [routerLink]="['/entities/reports/jhas/edit/' + sitevisitReportData?.reportId, viewdeletetedbutton]"
                  >
                    <em class="fa fa-eye" nbTooltip="View Detail" nbTooltipPlacement="top" nbTooltipStatus="secondary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-white icon-color"
                    *ngIf="!sitevisitReportData?.isFromMobileUploaded && sitevisitReportData?.reportType === 'MVPM' && viewArchUnarchbutton"
                    [routerLink]="['/entities/reports/mvpm/edit/' + sitevisitReportData?.id, viewdeletetedbutton]"
                  >
                    <em class="fa fa-eye" nbTooltip="View Detail" nbTooltipPlacement="top" nbTooltipStatus="secondary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon"
                    *ngIf="
                      !sitevisitReportData?.isFromMobileUploaded &&
                      sitevisitReportData?.reportType === 'VGT' &&
                      !checkAuthorisationsFn([roleType.CUSTOMER]) &&
                      !viewArchUnarchbutton
                    "
                    [routerLink]="['/entities/reports/vegetation/edit/' + sitevisitReportData?.id, viewdeletetedbutton]"
                  >
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon"
                    *ngIf="
                      !sitevisitReportData?.isFromMobileUploaded &&
                      !checkAuthorisationsFn([roleType.CUSTOMER]) &&
                      sitevisitReportData?.reportType === 'SV' &&
                      !viewArchUnarchbutton
                    "
                    [routerLink]="['/entities/reports/sitevisits/edit/' + sitevisitReportData?.id, viewdeletetedbutton]"
                  >
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon"
                    *ngIf="
                      sitevisitReportData?.reportType === 'JHA' && !checkAuthorisationsFn([roleType.CUSTOMER]) && !viewArchUnarchbutton
                    "
                    [routerLink]="['/entities/reports/jhas/edit/' + sitevisitReportData?.reportId, viewdeletetedbutton]"
                  >
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon"
                    *ngIf="
                      !sitevisitReportData?.isFromMobileUploaded &&
                      !checkAuthorisationsFn([roleType.CUSTOMER]) &&
                      sitevisitReportData?.reportType === 'MVPM' &&
                      !viewArchUnarchbutton
                    "
                    [routerLink]="['/entities/reports/mvpm/edit/' + sitevisitReportData?.id, viewdeletetedbutton]"
                  >
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-primary"
                    *ngIf="
                      !sitevisitReportData?.isFromMobileUploaded &&
                      (sitevisitReportData?.reportType === 'SV' ||
                        sitevisitReportData?.reportType === 'VGT' ||
                        sitevisitReportData?.reportType === 'MVPM') &&
                      !viewArchUnarchbutton &&
                      !checkAuthorisationsFn([roleType.CUSTOMER])
                    "
                    [routerLink]="['/entities/reports/sitevisits/image-gallery/' + sitevisitReportData?.id]"
                  >
                    <em class="fa fa-images" nbTooltip="Image Gallery" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-primary"
                    *ngIf="
                      sitevisitReportData?.reportType === 'JHA' && !checkAuthorisationsFn([roleType.CUSTOMER]) && !viewArchUnarchbutton
                    "
                    [routerLink]="['/entities/reports/jhas/image-gallery/' + sitevisitReportData?.reportId]"
                  >
                    <em class="fa fa-images" nbTooltip="Image Gallery" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-primary"
                    *ngIf="
                      checkAuthorisationsFn([roleType.CUSTOMER]) &&
                      !sitevisitReportData?.isFromMobileUploaded &&
                      (sitevisitReportData?.reportType === 'SV' ||
                        sitevisitReportData?.reportType === 'VGT' ||
                        sitevisitReportData?.reportType === 'MVPM')
                    "
                    [routerLink]="['/entities/reports/sitevisits/image-gallery/' + sitevisitReportData?.id]"
                  >
                    <em class="fa fa-images" nbTooltip="Image Gallery" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-primary"
                    *ngIf="checkAuthorisationsFn([roleType.CUSTOMER]) && sitevisitReportData?.reportType === 'JHA'"
                    [routerLink]="['/entities/reports/jhas/image-gallery/' + sitevisitReportData?.reportId]"
                  >
                    <em class="fa fa-images" nbTooltip="Image Gallery" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 text-primary listgrid-icon"
                    *ngIf="
                      (!sitevisitReportData?.isFromMobileUploaded &&
                        (sitevisitReportData?.reportType === 'SV' ||
                          sitevisitReportData?.reportType === 'VGT' ||
                          sitevisitReportData?.reportType === 'MVPM') &&
                        !checkAuthorisationsFn([roleType.CUSTOMER])) ||
                      viewArchUnarchbutton
                    "
                    (click)="imageCopyUrl(sitevisitReportData?.id)"
                  >
                    <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 text-primary listgrid-icon"
                    *ngIf="
                      sitevisitReportData?.reportType === 'JHA' && !checkAuthorisationsFn([roleType.CUSTOMER]) && !viewArchUnarchbutton
                    "
                    (click)="imageJhaCopyUrl(sitevisitReportData?.reportId)"
                  >
                    <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 text-primary listgrid-icon"
                    *ngIf="checkAuthorisationsFn([roleType.CUSTOMER]) && sitevisitReportData?.reportType === 'JHA'"
                    (click)="imageJhaCopyUrl(sitevisitReportData?.reportId)"
                  >
                    <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 text-primary listgrid-icon"
                    *ngIf="
                      checkAuthorisationsFn([roleType.CUSTOMER]) &&
                      !sitevisitReportData?.isFromMobileUploaded &&
                      (sitevisitReportData?.reportType === 'SV' ||
                        sitevisitReportData?.reportType === 'VGT' ||
                        sitevisitReportData?.reportType === 'MVPM')
                    "
                    (click)="imageCopyUrl(sitevisitReportData?.id)"
                  >
                    <em class="fa fa-copy" nbTooltip="Share Link" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                    class="px-2 text-primary listgrid-icon"
                    (click)="viewReport(sitevisitReportData?.id, sitevisitReportData?.reportId)"
                  >
                    <em class="fa fa-cloud" nbTooltip="Uploaded Report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    *appHasPermission="roleType.CUSTOMER"
                    class="px-2 text-primary listgrid-icon"
                    (click)="viewReport(sitevisitReportData?.id, sitevisitReportData?.reportId)"
                  >
                    <em class="fa fa-cloud" nbTooltip="Download Report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 text-danger listgrid-icon"
                    *ngIf="
                      (checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER, roleType.SUPPORT]) ||
                        !checkAuthorisationsFn([roleType.CUSTOMER])) &&
                      !viewArchUnarchbutton
                    "
                    (click)="archiveUnarchiveReportById(sitevisitReportData?.reportId)"
                  >
                    <img
                      src="/assets/images/archive_icon.png"
                      nbTooltip="Archive"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      height="25"
                      alt="No found archive"
                    />
                  </a>
                  <a
                    class="px-2 text-danger listgrid-icon"
                    *ngIf="
                      (checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.PORTFOLIOMANAGER, roleType.SUPPORT]) ||
                        !checkAuthorisationsFn([roleType.CUSTOMER])) &&
                      viewArchUnarchbutton
                    "
                    (click)="archiveUnarchiveReportById(sitevisitReportData?.reportId)"
                  >
                    <img
                      src="/assets/images/unarchive_icon.png"
                      nbTooltip="Unarchive"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      height="25"
                      alt="No found unarchive"
                    />
                  </a>
                  <a
                    class="px-2 text-danger listgrid-icon"
                    *ngIf="
                      (sitevisitReportData?.reportType === 'SV' || sitevisitReportData?.reportType !== 'JHA') &&
                      !checkAuthorisationsFn([roleType.CUSTOMER])
                    "
                    (click)="deleteReport(sitevisitReportData?.id)"
                  >
                    <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                  </a>
                  <a
                    class="px-2 text-danger listgrid-icon"
                    *ngIf="sitevisitReportData?.reportType === 'JHA' && !checkAuthorisationsFn([roleType.CUSTOMER])"
                    (click)="deleteJHAReport(sitevisitReportData?.reportId)"
                  >
                    <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                  </a>
                </div>
              </td>
              <td data-title="Action" class="text-end upload-icon-action" *ngIf="viewdeletetedbutton">
                <div class="d-md-flex justify-content-end">
                  <a
                    class="px-2 listgrid-icon text-white icon-color"
                    *ngIf="!sitevisitReportData?.isFromMobileUploaded && sitevisitReportData?.reportType === 'SV'"
                    [routerLink]="['/entities/reports/sitevisits/edit/' + sitevisitReportData?.id, viewdeletetedbutton]"
                  >
                    <em class="fa fa-eye" nbTooltip="View Detail" nbTooltipPlacement="top" nbTooltipStatus="secondary"></em>
                  </a>
                  <a
                    class="px-2 listgrid-icon text-white icon-color"
                    *ngIf="sitevisitReportData?.reportType === 'JHA'"
                    [routerLink]="['/entities/reports/jhas/edit/' + sitevisitReportData?.reportId, viewdeletetedbutton]"
                  >
                    <em class="fa fa-eye" nbTooltip="View Detail" nbTooltipPlacement="top" nbTooltipStatus="secondary"></em>
                  </a>

                  <a
                    class="px-2 text-primary listgrid-icon"
                    *ngIf="
                      checkAuthorisationsFn([roleType.ADMIN]) &&
                      (sitevisitReportData?.reportType === 'SV' || sitevisitReportData?.reportType !== 'JHA')
                    "
                    (click)="restoreSVReport(sitevisitReportData?.id)"
                  >
                    <em class="fa fa-undo" nbTooltip="Restore Report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 text-primary listgrid-icon"
                    *ngIf="checkAuthorisationsFn([roleType.ADMIN]) && sitevisitReportData?.reportType === 'JHA'"
                    (click)="restoreJHAReport(sitevisitReportData?.reportId)"
                  >
                    <em class="fa fa-undo" nbTooltip="Restore Report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a
                    class="px-2 text-danger listgrid-icon"
                    *ngIf="
                      checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER]) &&
                      !viewArchUnarchbutton
                    "
                    (click)="archiveUnarchiveReportById(sitevisitReportData?.reportId)"
                  >
                    <img
                      src="/assets/images/archive_icon.png"
                      nbTooltip="Archive"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      height="25"
                      alt="No found archive"
                    />
                  </a>
                  <a
                    class="px-2 text-primary listgrid-icon"
                    *ngIf="
                      checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER]) &&
                      viewArchUnarchbutton
                    "
                    (click)="archiveUnarchiveReportById(sitevisitReportData?.reportId)"
                  >
                    <img
                      src="/assets/images/unarchive_icon.png"
                      height="25"
                      nbTooltip="Unarchive"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      alt="No found unarchive"
                    />
                  </a>
                </div>
              </td>
            </tr>
            <tr *ngIf="!reportsData?.length">
              <td colspan="10" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="reportsData?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()" appendTo="body">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
            <ng-option value="100">1000</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
    <div *ngIf="linking === true" class="form-control-group">
      <div id="fixed-table" setTableHeight class="mt-3 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Report List">
          <thead>
            <tr>
              <th scope="col" (click)="sort('CustomerPortfolio', sortOptionList['CustomerPortfolio'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Customer (Portfolio)</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['CustomerPortfolio'] === 'desc',
                      'fa-arrow-down': sortOptionList['CustomerPortfolio'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'CustomerPortfolio'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('SiteName', sortOptionList['SiteName'])" id="siteName">
                <div class="d-flex align-items-center">
                  <span class="me-2">Site</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['SiteName'] === 'desc',
                      'fa-arrow-down': sortOptionList['SiteName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'SiteName'
                    }"
                  ></span>
                </div>
              </th>
              <th id="workOrder">Status</th>
              <th id="workOrder">Request By</th>
              <th id="workOrder">Request Date Time</th>
              <th class="text-start">Zip File Link</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of linkData; trackBy: trackByFunction">
              <td data-title="Customer (Portfolio)" class="td-custom-width">{{ data.customerPortfolio }}</td>
              <td
                data-title="Site"
                class="text-truncate td-custom-width"
                nbTooltip="{{ data?.siteName }}"
                nbTooltipPlacement="top"
                nbTooltipStatus="primary"
              >
                {{ data.siteName }}
              </td>
              <td data-title="Status">{{ data.status }}</td>
              <td data-title="Request By">{{ data.requestBy }}</td>
              <td data-title="Request Date Time">{{ data.requestedDate | date : fullDateFormat }}</td>
              <td data-title="Zip File Link">
                <ng-container *ngIf="data.status !== 'In Progress'; else preparingReport">
                  <ng-container *ngIf="data.status !== 'Error' && data.zipFileName !== ''; else downloadReportErrorLogs">
                    <a [href]="ENV.serverUrl + '/OtherReport/' + data.zipFileName + '/downloadZipFile'" target="_blank">{{
                      '/api/OtherReport/' + data.zipFileName + '/downloadZipFile'
                    }}</a>
                  </ng-container>
                </ng-container>

                <ng-template #downloadReportErrorLogs>
                  <span [innerHTML]="data.errorMesssage"></span>
                </ng-template>
                <ng-template #preparingReport>
                  <span>Please wait, we are preparing your report.</span>
                </ng-template>
              </td>
            </tr>
            <tr *ngIf="!linkData?.length">
              <td colspan="6" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </nb-card-body>
</nb-card>
<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Preview</h4>
  </div>
  <div class="modal-body preview-body">
    <div #divLegalNoticeHeight [innerHTML]="previewHTML | safeHTML"></div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" #closeModalButton (click)="previewModalRef.hide()">Close</button>
  </div>
</ng-template>
