<nb-card class="workOrderPlanInfo" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-body class="dropdownOverlap">
    <div class="completion-plan-heading text-center">
      <h5>{{ chartLabel }}</h5>
    </div>

    <div class="mb-3 chart-box">
      <div echarts [options]="workOrderChart" [theme]="currentTheme" class="chart-height-150"></div>
    </div>
    <div class="mb-3 chart-box">
      <div echarts [options]="completionChart" [theme]="currentTheme" class="chart-height-425"></div>
    </div>

    <p class="text-end fw-bolder" *ngIf="!filterHasNotChanged">Please apply changes to continue.</p>

    <div class="position-relative">
      <div class="block-ui" *ngIf="!filterHasNotChanged"></div>
      <div class="chart-box row pm-completion-numbers px-0 mx-auto my-4 overflow-auto">
        <ng-container *ngFor="let workOrderInfo of workOrderPlanInfo; let i = index">
          <div
            class="selectable-div d-flex flex-column align-items-center p-2 m-3 cursor-pointer"
            [ngClass]="{
              active: selectedStatus === workOrderInfo?.name,
              'cursor-default': selectedStatus === workOrderInfo?.name && filterHasNotChanged,
              'cursor-blocked': !filterHasNotChanged
            }"
            (click)="onStatusChange(workOrderInfo?.name)"
          >
            <span class="fw-bolder text-nowrap">{{ workOrderInfo?.name }}</span>
            <span class="primary-color numbers fs-5 fw-bold">{{ workOrderInfo?.data }}</span>
          </div>
        </ng-container>
      </div>

      <div class="row mb-3 wo-table-header justify-content-between align-items-center">
        <span class="fw-bold col-6">{{ selectedStatus }}</span>
        <div class="col-6 d-flex justify-content-end align-items-center">
          <button
            *ngIf="
              selectedStatus !== PMTableStatusStr.Total &&
              selectedStatus !== PMTableStatusStr.Unscheduled &&
              selectedStatus !== PMTableStatusStr.Tentative &&
              selectedStatus !== PMTableStatusStr.OnTime
            "
            type="button"
            class="btn btn-primary ms-auto"
            [disabled]="!selectedWorkOrderIds.length"
            (click)="openRescheduleModal()"
          >
            Reschedule <span *ngIf="selectedWorkOrderIds.length">({{ selectedWorkOrderIds.length }})</span>
          </button>
        </div>
      </div>

      <div class="col-12 rescheduleWorkOrder-table table-responsive table-card-view">
        <table class="table table-bordered table-hover" aria-describedby="Workorder List">
          <thead>
            <tr>
              <th
                *ngIf="
                  selectedStatus !== PMTableStatusStr.Total &&
                  selectedStatus !== PMTableStatusStr.Unscheduled &&
                  selectedStatus !== PMTableStatusStr.Tentative &&
                  selectedStatus !== PMTableStatusStr.OnTime
                "
                class="text-center"
              >
                <nb-checkbox
                  id="select-all"
                  class="sfl-track-checkbox"
                  (change)="selectAllWo()"
                  [checked]="selectedAllWorkOrders"
                  [(ngModel)]="selectedAllWorkOrders"
                  [disabled]="!rescheduleData.pmTableDto?.length || isSelectAllDisabled"
                  name="selectallreschedule"
                >
                </nb-checkbox>
              </th>
              <th scope="col" class="text-start cursor-pointer" id="customerPortfolio" (click)="sort(pmTableSortingKey.customername)">
                <div class="d-flex justify-content-between align-items-center">
                  <span> Customer (Portfolio)</span>
                  <span
                    class="fa"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['customername'] === 'DESC',
                      'fa-arrow-down': sortOptionList['customername'] === 'ASC',
                      'icon-selected': filterModel.sortBy === 'customername'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="text-start cursor-pointer" id="site" (click)="sort(pmTableSortingKey.sitename)">
                <div class="d-flex justify-content-between align-items-center">
                  <span> Site</span>
                  <span
                    class="fa"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['sitename'] === 'DESC',
                      'fa-arrow-down': sortOptionList['sitename'] === 'ASC',
                      'icon-selected': filterModel.sortBy === 'sitename'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="text-start cursor-pointer" id="assessmentType" (click)="sort(pmTableSortingKey.assesmenttype)">
                <div class="d-flex justify-content-between align-items-center">
                  <span> Assessment Type</span>
                  <span
                    class="fa"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['assesmenttype'] === 'DESC',
                      'fa-arrow-down': sortOptionList['assesmenttype'] === 'ASC',
                      'icon-selected': filterModel.sortBy === 'assesmenttype'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="text-start cursor-pointer" id="frequency" (click)="sort(pmTableSortingKey.frequency)">
                <div class="d-flex justify-content-between align-items-center">
                  <span> Frequency</span>
                  <span
                    class="fa"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['frequency'] === 'DESC',
                      'fa-arrow-down': sortOptionList['frequency'] === 'ASC',
                      'icon-selected': filterModel.sortBy === 'frequency'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="text-start cursor-pointer" id="tentativeDate" (click)="sort(pmTableSortingKey.tentativemonth)">
                <div class="d-flex justify-content-between align-items-center">
                  <span> Tentative Month</span>
                  <span
                    class="fa"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['tentativemonth'] === 'DESC',
                      'fa-arrow-down': sortOptionList['tentativemonth'] === 'ASC',
                      'icon-selected': filterModel.sortBy === 'tentativemonth'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="text-start cursor-pointer" id="scheduleDate" (click)="sort(pmTableSortingKey.datescheduled)">
                <div class="d-flex justify-content-between align-items-center">
                  <span> Schedule Date</span>
                  <span
                    class="fa"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['datescheduled'] === 'DESC',
                      'fa-arrow-down': sortOptionList['datescheduled'] === 'ASC',
                      'icon-selected': filterModel.sortBy === 'datescheduled'
                    }"
                  ></span>
                </div>
              </th>
              <th *ngIf="selectedStatus === PMTableStatusStr.Total" scope="col" id="status" class="text-start">
                <span> Status</span>
              </th>
            </tr>
            <tr class="header-filter">
              <th
                scope="col"
                class="text-start"
                *ngIf="
                  selectedStatus !== PMTableStatusStr.Total &&
                  selectedStatus !== PMTableStatusStr.Unscheduled &&
                  selectedStatus !== PMTableStatusStr.Tentative &&
                  selectedStatus !== PMTableStatusStr.OnTime
                "
              ></th>
              <th scope="col" class="text-start">
                <div class="position-relative">
                  <input
                    id="wo-search"
                    class="form-control search-textbox deviceSearch sfl-track-input"
                    type="text"
                    name="search"
                    autocomplete="off"
                    [(ngModel)]="searchParams.customerName"
                    (ngModelChange)="onSearchChanged()"
                  />
                  <span
                    class="clear-icon position-absolute cursor-pointer"
                    *ngIf="searchParams.customerName"
                    (click)="searchParams.customerName = null; onSearchChanged()"
                    >&times;</span
                  >
                </div>
              </th>
              <th scope="col" class="text-start">
                <div class="position-relative">
                  <input
                    id="wo-search"
                    class="form-control search-textbox deviceSearch sfl-track-input"
                    type="text"
                    name="search"
                    autocomplete="off"
                    [(ngModel)]="searchParams.siteName"
                    (ngModelChange)="onSearchChanged()"
                  />
                  <span
                    class="clear-icon position-absolute cursor-pointer"
                    *ngIf="searchParams.siteName"
                    (click)="searchParams.siteName = null; onSearchChanged()"
                    >&times;</span
                  >
                </div>
              </th>
              <th scope="col" class="text-start">
                <div class="position-relative">
                  <input
                    id="wo-search"
                    class="form-control search-textbox deviceSearch sfl-track-input"
                    type="text"
                    name="search"
                    autocomplete="off"
                    [(ngModel)]="searchParams.assesmentType"
                    (ngModelChange)="onSearchChanged()"
                  />
                  <span
                    class="clear-icon position-absolute cursor-pointer"
                    *ngIf="searchParams.assesmentType"
                    (click)="searchParams.assesmentType = null; onSearchChanged()"
                    >&times;</span
                  >
                </div>
              </th>
              <th scope="col" class="text-start">
                <div class="position-relative">
                  <input
                    id="wo-search"
                    class="form-control search-textbox deviceSearch sfl-track-input"
                    type="text"
                    name="search"
                    autocomplete="off"
                    [(ngModel)]="searchParams.frequency"
                    (ngModelChange)="onSearchChanged()"
                  />
                  <span
                    class="clear-icon position-absolute cursor-pointer"
                    *ngIf="searchParams.frequency"
                    (click)="searchParams.frequency = null; onSearchChanged()"
                    >&times;</span
                  >
                </div>
              </th>
              <th scope="col" class="text-start">
                <div class="position-relative">
                  <input
                    id="wo-search"
                    class="form-control search-textbox deviceSearch sfl-track-input"
                    type="text"
                    name="search"
                    autocomplete="off"
                    [(ngModel)]="searchParams.tentativeMonth"
                    (ngModelChange)="onSearchChanged()"
                  />
                  <span
                    class="clear-icon position-absolute cursor-pointer"
                    *ngIf="searchParams.tentativeMonth"
                    (click)="searchParams.tentativeMonth = null; onSearchChanged()"
                    >&times;</span
                  >
                </div>
              </th>
              <th scope="col" class="text-start">
                <div class="position-relative">
                  <input
                    id="wo-search"
                    class="form-control search-textbox deviceSearch sfl-track-input"
                    type="text"
                    name="search"
                    autocomplete="off"
                    [(ngModel)]="searchParams.dateScheduled"
                    (ngModelChange)="onSearchChanged()"
                  />
                  <span
                    class="clear-icon position-absolute cursor-pointer"
                    *ngIf="searchParams.dateScheduled"
                    (click)="searchParams.dateScheduled = null; onSearchChanged()"
                    >&times;</span
                  >
                </div>
              </th>
              <th scope="col" class="text-start" *ngIf="selectedStatus === PMTableStatusStr.Total"></th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let workOrder of rescheduleData.pmTableDto
                  | paginate
                    : {
                        itemsPerPage: filterModel.itemsCount,
                        currentPage: paginationParams.currentPage,
                        totalItems: rescheduleData.total
                      };
                let i = index;
                trackBy: trackByFunc
              "
            >
              <td
                *ngIf="
                  selectedStatus !== PMTableStatusStr.Total &&
                  selectedStatus !== PMTableStatusStr.Unscheduled &&
                  selectedStatus !== PMTableStatusStr.Tentative &&
                  selectedStatus !== PMTableStatusStr.OnTime
                "
                data-title="Action"
                class="text-center"
              >
                <nb-checkbox
                  id="select-wo"
                  class="sfl-track-checkbox"
                  name="selectsite"
                  (change)="woCheckboxChanged($event, i)"
                  [checked]="workOrder.isSelected"
                  [(ngModel)]="workOrder.isSelected"
                  [disabled]="workOrder?.isCheckboxDisabled"
                >
                </nb-checkbox>
              </td>
              <td data-title="customerPortfolio" class="text-start">{{ workOrder?.customerPortFolio }}</td>
              <td data-title="Site" class="text-start">{{ workOrder?.siteName }}</td>
              <td data-title="Assessment Type" class="text-start">{{ workOrder?.assesmentType }}</td>
              <td data-title="Frequency" class="text-start">{{ workOrder?.frequency }}</td>
              <td data-title="Tentative Month" class="text-start">{{ workOrder?.tentativeMonthStr || '-' }}</td>
              <td data-title="Schedule Date" class="text-start">{{ (workOrder?.dateScheduled | date : fullDateFormat) || '-' }}</td>
              <td data-title="Status" *ngIf="selectedStatus === PMTableStatusStr.Total" class="text-start">{{ workOrder?.woStatus }}</td>
            </tr>
            <tr *ngIf="!rescheduleData.pmTableDto.length">
              <td colspan="7" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
        <div class="mt-2 d-md-flex align-items-center" *ngIf="rescheduleData.pmTableDto?.length">
          <div class="d-flex align-items-center">
            <label class="mb-0">Items per page: </label>
            <ng-select
              class="ms-2"
              [(ngModel)]="paginationParams.pageSize"
              [clearable]="false"
              [searchable]="false"
              (change)="onChangeSize()"
              appendTo="body"
            >
              <ng-option value="5">5</ng-option>
              <ng-option value="10">10</ng-option>
              <ng-option value="50">50</ng-option>
              <ng-option value="100">100</ng-option>
            </ng-select>
          </div>
          <strong class="ms-md-3">Total: {{ rescheduleData.total }}</strong>
          <div class="ms-md-auto ms-sm-0">
            <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
