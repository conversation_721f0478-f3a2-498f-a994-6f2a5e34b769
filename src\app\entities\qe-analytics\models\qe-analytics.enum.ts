import { HeatMapColor } from './qe-analytics.model';

export enum QEAnalyticsPasswordOperationEnum {
  PASSWORD = 1,
  CHANGE_PASSWORD = 2,
  RESET_PASSWORD = 3
}

export enum QEAnalyticsPasswordAuthEnum {
  NOT_OPENED = 1,
  USER_AUTHENTICATED = 2,
  USER_NOT_AUTHENTICATED = 3
}

export enum QE_MODULE_ENUM {
  SITE_INFO = 1,
  PM = 2,
  CM = 3
}

export enum QE_ANALYTICS_HEAT_MAP_COLOR_ENUM {
  P0 = 0,
  P5 = 5,
  P10 = 10,
  P15 = 15,
  P20 = 20,
  P25 = 25,
  P30 = 30,
  P35 = 35,
  P40 = 40,
  P45 = 45,
  P50 = 50,
  P55 = 55,
  P60 = 60,
  P65 = 65,
  P70 = 70,
  P75 = 75,
  P80 = 80,
  P85 = 85,
  P90 = 90,
  P95 = 95,
  P100 = 100
}

export const QE_ANALYTICS_HEAT_MAP_COLOR_MAP: Record<QE_ANALYTICS_HEAT_MAP_COLOR_ENUM, HeatMapColor> = {
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P0]: { hexBGColor: '#F2F2F2', colorName: 'Gray 0%', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P5]: { hexBGColor: '#E6E6E6', colorName: 'Gray 5%', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P10]: { hexBGColor: '#D9D9D9', colorName: 'Gray 10%', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P15]: { hexBGColor: '#CCCCCC', colorName: 'Gray 15%', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P20]: { hexBGColor: '#BFBFBF', colorName: 'Gray 20%', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P25]: { hexBGColor: '#B3B3B3', colorName: 'Gray 25%', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P30]: { hexBGColor: '#A6A6A6', colorName: 'Gray 30%', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P35]: { hexBGColor: '#999999', colorName: 'Gray 35%', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P40]: { hexBGColor: '#8C8C8C', colorName: 'Gray 40%', hexTextColor: '#FFFFFF' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P45]: { hexBGColor: '#808080', colorName: 'Gray 45%', hexTextColor: '#FFFFFF' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P50]: { hexBGColor: '#737373', colorName: 'Gray 50%', hexTextColor: '#FFFFFF' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P55]: { hexBGColor: '#66FF66', colorName: 'Light Green', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P60]: { hexBGColor: '#80FF66', colorName: 'Green-Yellow', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P65]: { hexBGColor: '#99FF66', colorName: 'Lime', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P70]: { hexBGColor: '#B2FF66', colorName: 'Yellow-Green', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P75]: { hexBGColor: '#CCFF33', colorName: 'Lime-Yellow', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P80]: { hexBGColor: '#FFCC00', colorName: 'Amber', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P85]: { hexBGColor: '#FF9933', colorName: 'Orange', hexTextColor: '#000000' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P90]: { hexBGColor: '#FF6600', colorName: 'Dark Orange', hexTextColor: '#FFFFFF' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P95]: { hexBGColor: '#FF3300', colorName: 'Reddish-Orange', hexTextColor: '#FFFFFF' },
  [QE_ANALYTICS_HEAT_MAP_COLOR_ENUM.P100]: { hexBGColor: '#FF0000', colorName: 'Red', hexTextColor: '#FFFFFF' }
} as const;
