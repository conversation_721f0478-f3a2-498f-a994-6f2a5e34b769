import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as CryptoJS from 'crypto-js';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import {
  SflOtpInputComponentOptions,
  SflOtpInputComponentOptionsClass
} from '../../@shared/components/otp-input/models/otp-input-default.config';
import { OtpInputComponent } from '../../@shared/components/otp-input/otp-input.component';
import { APP_ROUTES, AppConstants } from '../../@shared/constants';
import { SecurityQuestionItem, User } from '../../@shared/models/user.model';
import { AlertService } from '../../@shared/services';
import { CommonService } from '../../@shared/services/common.service';
import { StorageService } from '../../@shared/services/storage.service';
import { CustomerService } from '../../entities/customer-management/customer.service';
import { PortfolioService } from '../../entities/portfolio-management/portfolio.service';
import { QEMenuModuleType } from '../../entities/qe-analytics/models/qe-analytics.model';
import { QEAnalyticsService } from '../../entities/qe-analytics/services/qe-analytics.service';
import { SiteService } from '../../entities/site-management/site.service';
import { AuthService } from '../index';
import { UserAuthenticationReq, UserAuthenticationRes } from '../models/user-authentication.model';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../../@shared/enums';

@Component({
  selector: 'qesolar-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class LoginComponent implements OnInit, OnDestroy {
  @ViewChild('codeEntryFailedButton') codeEntryFailedButton;
  @ViewChild('codeExpiredButton') codeExpiredButton;
  @ViewChild('accountLockedButton') accountLockedButton;
  @ViewChild('sflOtpInput') sflOtpInput: OtpInputComponent;

  user: any = {};
  userAuthenticationReqObj: UserAuthenticationReq = new UserAuthenticationReq();
  submitted = false;
  rememberMe = false;
  returnUrl: string;
  readonly ROUTES = APP_ROUTES;
  subscription: Subscription = new Subscription();
  loading = false;
  emailPassScreenHidden = false;
  userAuthenticationResObj: UserAuthenticationRes = new UserAuthenticationRes({});
  sflOtpInputOptions: SflOtpInputComponentOptions = new SflOtpInputComponentOptionsClass({
    hideInputValues: true,
    showInputValuesBtn: true,
    ariaLabels: ['*', '*', '*', '*', '*', '*']
  });
  securityQuestionList: SecurityQuestionItem[] = [];
  modalRef: BsModalRef;

  constructor(
    private readonly router: Router,
    private readonly authService: AuthService,
    private readonly route: ActivatedRoute,
    private readonly customerService: CustomerService,
    private readonly portfolioService: PortfolioService,
    private readonly siteService: SiteService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly modalService: BsModalService,
    private readonly qeAnalyticsService: QEAnalyticsService,
    private readonly alertService: AlertService
  ) {}

  ngOnInit() {
    this.subscription.add(
      this.commonService.autoLogout.subscribe({
        next: response => {
          if (response) {
            localStorage.clear();
            this.customerService.clearCustomer();
            this.portfolioService.clearPortfolio();
            this.siteService.clearSite();
          }
        }
      })
    );
    this.storageService.clear(AppConstants.authenticationToken);
    this.storageService.clear(AppConstants.refreshToken);
    this.storageService.clear(AppConstants.isMFAUserLoggedIn);
  }

  login(callback?) {
    this.loading = true;
    this.submitted = true;
    const cb = callback || function () {};
    return new Promise((resolve, reject) => {
      const userDetail = JSON.parse(JSON.stringify(this.userAuthenticationReqObj));
      if (this.userAuthenticationReqObj.password) {
        userDetail.password = this.setEncryption('sflEncryptedPassword', this.userAuthenticationReqObj.password);
      }
      this.subscription.add(
        this.authService.login(userDetail).subscribe({
          next: async (res: UserAuthenticationRes) => {
            this.submitted = false;
            if (res.isAuthenticated && res.id_Token && res.refresh_Token) {
              await this.isAuthenticatedUser();
            } else {
              this.manageUserAuthentication(res);
            }
            this.loading = false;
            return cb();
          },
          error: err => {
            this.submitted = false;
            this.reset();
            reject(err);
            this.loading = false;
            return cb(err);
          }
        })
      );
    });
  }

  async isAuthenticatedUser(): Promise<void> {
    this.setPermission();
    try {
      await Promise.all([this.getUserAccount(), this.getQEMenuModuleList()]);
      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || APP_ROUTES.DASHBOARD;
      this.router.navigateByUrl(this.returnUrl);
    } catch (error) {
      this.alertService.showErrorToast('Something went wrong. Please login again.');
      setTimeout(() => {
        this.router.navigate(['auth/login']);
        this.authService.clearCustomerCache().subscribe(res => {
          this.storageService.clearAll();
          window.location.reload();
        });
      }, 100);
    }
  }

  setEncryption(keys, value) {
    const keySize = 256;
    const salt = CryptoJS.lib.WordArray.random(16);
    const key = CryptoJS.PBKDF2(keys, salt, {
      keySize: keySize / 32,
      iterations: 100
    });
    const iv = CryptoJS.lib.WordArray.random(128 / 8);
    const encrypted = CryptoJS.AES.encrypt(value, key, {
      iv: iv,
      padding: CryptoJS.pad.Pkcs7,
      mode: CryptoJS.mode.CBC
    });
    const result = CryptoJS.enc.Base64.stringify(salt.concat(iv).concat(encrypted.ciphertext));
    return result;
  }

  setPermission() {
    const token = this.storageService.get(AppConstants.authenticationToken),
      tokenData = token.split('.')[1],
      decodedTokenJSONData = window.atob(tokenData),
      decodedTokenData = JSON.parse(decodedTokenJSONData);

    this.storageService.set('userID', decodedTokenData.UserID);
    if (decodedTokenData.role === AUTHORITY_ROLE_STRING[ROLE_TYPE.ADMIN]) {
      this.AddAdminPermission();
    } else if (decodedTokenData.role === AUTHORITY_ROLE_STRING[ROLE_TYPE.PORTFOLIOMANAGER]) {
      this.AddPortfolioPermission();
    } else if (decodedTokenData.role === AUTHORITY_ROLE_STRING[ROLE_TYPE.MANAGER]) {
      this.AddManagerPermission();
    } else if (decodedTokenData.role === AUTHORITY_ROLE_STRING[ROLE_TYPE.FIELDTECH]) {
      this.AddFieldPermission();
    } else if (decodedTokenData.role === AUTHORITY_ROLE_STRING[ROLE_TYPE.CUSTOMER]) {
      this.AddCustomerPermission();
    } else if (decodedTokenData.role === AUTHORITY_ROLE_STRING[ROLE_TYPE.ANALYST]) {
      this.AddAnalystPermission();
    } else if (decodedTokenData.role === AUTHORITY_ROLE_STRING[ROLE_TYPE.SUPPORT]) {
      this.AddSupportPermission();
    }
  }

  AddAdminPermission() {
    const data = {
      User: true,
      Site: true,
      Portfolio: true,
      Assessment: true,
      WO: true,
      Customer: true,
      Report: true,
      SiteAuditReport: true,
      SiteDashboard: true,
      PMDashboard: true,
      CMDashboard: true,
      AutomationDashboard: true,
      Availability: true,
      Equipments: true,
      Tickets: true,
      CMReports: true,
      AuditDispatchReport: true,
      MapReport: true,
      Exclusions: true,
      Performance: true,
      SiteDevice: true,
      TruckRollReport: true,
      BillingReport: true,
      PerformanceReport: true,
      RmaReport: true,
      DataTable: true,
      AvailabilityDataTable: true,
      AvailabilityExclusion: true,
      DataSource: true,
      apiErrorLog: true,
      AvailabilityReport: true,
      PerformanceReports: true,
      Safety: true,
      Settings: true,
      JHA: true,
      GeneralInfo: true,
      WorkType: true,
      WorkStep: true,
      Hazard: true,
      Barrier: true,
      LOTO: true,
      PerformanceDashboard: true,
      SiteCheckIn: true,
      PerformancePowerChart: true,
      PerformancePowerCards: true,
      SiteAuditJHA: true,
      PerformanceOutage: true,
      reportSchedule: true,
      CustomerAPIGateway: true,
      APIGatewayDashboard: true,
      EmailLog: true,
      reFetchSchedule: true,
      OperationsReports: true,
      OperationsRegionMapping: true,
      OperationsServices: true,
      contracts: true,
      CustomForms: true,
      NonConformance: true,
      Analytics: true
    };
    this.storageService.set('permission', data);
  }
  AddPortfolioPermission() {
    const data = {
      User: false,
      Portfolio: true,
      Site: true,
      Assessment: true,
      WO: true,
      Customer: true,
      Report: true,
      SiteAuditReport: true,
      SiteDashboard: true,
      PMDashboard: false,
      CMDashboard: true,
      AutomationDashboard: false,
      Availability: false,
      Equipments: true,
      Tickets: true,
      CMReports: true,
      AuditDispatchReport: true,
      MapReport: true,
      Exclusions: true,
      Performance: true,
      SiteDevice: true,
      BillingReport: true,
      TruckRollReport: true,
      PerformanceReport: true,
      RmaReport: true,
      DataTable: true,
      AvailabilityDataTable: true,
      AvailabilityExclusion: true,
      DataSource: false,
      apiErrorLog: false,
      AvailabilityReport: true,
      PerformanceReports: true,
      Safety: true,
      Settings: false,
      JHA: true,
      GeneralInfo: false,
      WorkType: false,
      WorkStep: false,
      Hazard: false,
      Barrier: false,
      LOTO: true,
      PerformanceDashboard: true,
      SiteCheckIn: true,
      PerformancePowerChart: true,
      PerformancePowerCards: true,
      SiteAuditJHA: true,
      PerformanceOutage: true,
      reportSchedule: true,
      CustomerAPIGateway: false,
      APIGatewayDashboard: false,
      EmailLog: false,
      reFetchSchedule: false,
      OperationsReports: false,
      OperationsRegionMapping: false,
      OperationsServices: false,
      contracts: false,
      CustomForms: false,
      NonConformance: false
    };

    this.storageService.set('permission', data);
  }

  AddAnalystPermission() {
    const data = {
      User: false,
      Portfolio: true,
      Site: true,
      Assessment: true,
      WO: true,
      Customer: true,
      Report: true,
      SiteAuditReport: true,
      SiteDashboard: true,
      PMDashboard: false,
      CMDashboard: true,
      AutomationDashboard: false,
      Availability: false,
      Equipments: true,
      Tickets: true,
      CMReports: true,
      AuditDispatchReport: true,
      MapReport: true,
      Exclusions: true,
      Performance: true,
      SiteDevice: true,
      BillingReport: true,
      TruckRollReport: true,
      PerformanceReport: true,
      RmaReport: true,
      DataTable: true,
      AvailabilityDataTable: true,
      AvailabilityExclusion: true,
      DataSource: false,
      apiErrorLog: false,
      AvailabilityReport: true,
      PerformanceReports: true,
      Safety: true,
      Settings: false,
      JHA: true,
      GeneralInfo: false,
      WorkType: false,
      WorkStep: false,
      Hazard: false,
      Barrier: false,
      LOTO: true,
      PerformanceDashboard: true,
      SiteCheckIn: true,
      PerformancePowerChart: true,
      PerformancePowerCards: true,
      SiteAuditJHA: true,
      PerformanceOutage: false,
      reportSchedule: false,
      CustomerAPIGateway: false,
      APIGatewayDashboard: false,
      EmailLog: false,
      reFetchSchedule: false,
      OperationsReports: false,
      OperationsRegionMapping: false,
      OperationsServices: false,
      contracts: false,
      CustomForms: false,
      NonConformance: false
    };

    this.storageService.set('permission', data);
  }

  AddManagerPermission() {
    const data = {
      User: true,
      Portfolio: true,
      Site: true,
      Assessment: true,
      WO: true,
      Customer: true,
      Report: true,
      SiteAuditReport: true,
      SiteDashboard: true,
      PMDashboard: false,
      CMDashboard: true,
      AutomationDashboard: false,
      Availability: false,
      Equipments: true,
      Tickets: true,
      CMReports: true,
      AuditDispatchReport: true,
      MapReport: true,
      Exclusions: true,
      Performance: true,
      SiteDevice: true,
      BillingReport: true,
      TruckRollReport: true,
      PerformanceReport: true,
      RmaReport: true,
      DataTable: true,
      AvailabilityDataTable: true,
      AvailabilityExclusion: true,
      DataSource: false,
      apiErrorLog: false,
      AvailabilityReport: true,
      PerformanceReports: true,
      Safety: true,
      Settings: false,
      JHA: true,
      GeneralInfo: false,
      WorkType: false,
      WorkStep: false,
      Hazard: false,
      Barrier: false,
      LOTO: true,
      PerformanceDashboard: true,
      SiteCheckIn: true,
      PerformancePowerChart: true,
      PerformancePowerCards: true,
      SiteAuditJHA: true,
      PerformanceOutage: true,
      reportSchedule: true,
      EmailLog: false,
      CustomerAPIGateway: false,
      APIGatewayDashboard: false,
      reFetchSchedule: false,
      OperationsReports: true,
      OperationsRegionMapping: false,
      OperationsServices: true,
      contracts: true,
      CustomForms: true,
      NonConformance: true
    };

    this.storageService.set('permission', data);
  }

  AddSupportPermission() {
    const data = {
      User: true,
      Portfolio: true,
      Site: true,
      Assessment: true,
      WO: true,
      Customer: true,
      Report: true,
      SiteAuditReport: true,
      SiteDashboard: true,
      PMDashboard: false,
      CMDashboard: true,
      AutomationDashboard: false,
      Availability: false,
      Equipments: true,
      Tickets: true,
      CMReports: true,
      AuditDispatchReport: true,
      MapReport: true,
      Exclusions: true,
      Performance: true,
      SiteDevice: true,
      BillingReport: true,
      TruckRollReport: true,
      PerformanceReport: true,
      RmaReport: true,
      DataTable: true,
      AvailabilityDataTable: true,
      AvailabilityExclusion: true,
      DataSource: false,
      apiErrorLog: false,
      AvailabilityReport: true,
      PerformanceReports: true,
      Safety: true,
      Settings: false,
      JHA: true,
      GeneralInfo: false,
      WorkType: false,
      WorkStep: false,
      Hazard: false,
      Barrier: false,
      LOTO: true,
      PerformanceDashboard: true,
      SiteCheckIn: true,
      PerformancePowerChart: true,
      PerformancePowerCards: true,
      SiteAuditJHA: true,
      PerformanceOutage: true,
      reportSchedule: true,
      EmailLog: false,
      CustomerAPIGateway: false,
      APIGatewayDashboard: false,
      reFetchSchedule: false,
      OperationsReports: true,
      OperationsRegionMapping: false,
      OperationsServices: true,
      contracts: true,
      CustomForms: true,
      NonConformance: true
    };

    this.storageService.set('permission', data);
  }

  AddFieldPermission() {
    const data = {
      User: false,
      Site: true,
      Portfolio: true,
      Assessment: false,
      WO: true,
      Customer: true,
      Report: true,
      SiteAuditReport: true,
      SiteDashboard: true,
      PMDashboard: false,
      CMDashboard: true,
      AutomationDashboard: false,
      Availability: false,
      Equipments: true,
      Tickets: true,
      CMReports: true,
      AuditDispatchReport: true,
      MapReport: true,
      Exclusions: true,
      Performance: true,
      SiteDevice: true,
      BillingReport: false,
      TruckRollReport: false,
      PerformanceReport: true,
      RmaReport: true,
      DataTable: true,
      AvailabilityDataTable: true,
      AvailabilityExclusion: true,
      DataSource: false,
      apiErrorLog: false,
      AvailabilityReport: true,
      PerformanceReports: true,
      Safety: true,
      Settings: false,
      JHA: true,
      GeneralInfo: false,
      WorkType: false,
      WorkStep: false,
      Hazard: false,
      Barrier: false,
      LOTO: true,
      PerformanceDashboard: true,
      SiteCheckIn: true,
      PerformancePowerChart: true,
      PerformancePowerCards: true,
      SiteAuditJHA: true,
      PerformanceOutage: true,
      reportSchedule: false,
      CustomerAPIGateway: false,
      APIGatewayDashboard: false,
      EmailLog: false,
      reFetchSchedule: false,
      OperationsReports: false,
      OperationsRegionMapping: false,
      OperationsServices: false,
      contracts: false,
      CustomForms: true,
      NonConformance: false
    };
    this.storageService.set('permission', data);
  }
  AddCustomerPermission() {
    const data = {
      User: false,
      Site: true,
      Portfolio: true,
      Assessment: false,
      WO: false,
      Customer: true,
      Report: true,
      SiteAuditReport: false,
      SiteDashboard: false,
      PMDashboard: false,
      CMDashboard: false,
      AutomationDashboard: false,
      Availability: false,
      Equipments: false,
      Tickets: true,
      MyTickets: false,
      CMReports: false,
      AuditDispatchReport: false,
      MapReport: false,
      Exclusions: false,
      Performance: true,
      SiteDevice: true,
      BillingReport: false,
      TruckRollReport: true,
      PerformanceReport: false,
      RmaReport: false,
      DataTable: false,
      AvailabilityDataTable: false,
      AvailabilityExclusion: false,
      DataSource: false,
      apiErrorLog: false,
      AvailabilityReport: false,
      PerformanceReports: true,
      Safety: true,
      Settings: false,
      JHA: true,
      GeneralInfo: false,
      WorkType: false,
      WorkStep: false,
      Hazard: false,
      Barrier: false,
      LOTO: true,
      PerformanceDashboard: true,
      SiteCheckIn: false,
      SiteAuditJHA: false,
      PerformancePowerChart: true,
      PerformancePowerCards: false,
      PerformanceOutage: false,
      reportSchedule: false,
      CustomerAPIGateway: false,
      APIGatewayDashboard: false,
      EmailLog: false,
      reFetchSchedule: false,
      OperationsReports: false,
      OperationsRegionMapping: false,
      OperationsServices: false,
      contracts: false,
      CustomForms: false,
      NonConformance: false
    };
    this.storageService.set('permission', data);
  }

  getUserAccount() {
    return new Promise<void>(resolve => {
      this.authService.getAccount().subscribe({
        next: (users: User) => {
          this.user = users;
          this.storageService.set(AppConstants.userKey, this.user);
          this.storageService.set('userDefaultFilter', this.user.userFilterSelection);
          resolve();
        }
      });
    });
  }

  getQEMenuModuleList(): Promise<void> {
    return new Promise<void>(resolve => {
      this.qeAnalyticsService.getQEMenuModuleList().subscribe({
        next: (res: QEMenuModuleType[]) => {
          resolve();
        },
        error: () => {
          resolve();
        }
      });
    });
  }

  getSecurityQuestionList(): void {
    this.loading = true;
    this.subscription.add(
      this.authService.getSecurityQuestionList().subscribe({
        next: (res: SecurityQuestionItem[]) => {
          this.securityQuestionList = res;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  reset(): void {
    if (this.sflOtpInput) {
      this.sflOtpInput.reset();
      this.userAuthenticationReqObj.mfaCode = null;
      this.userAuthenticationReqObj.questionID = null;
      this.userAuthenticationReqObj.question = '';
      this.userAuthenticationReqObj.questionAnswer = '';
    }
  }

  manageUserAuthentication(res: UserAuthenticationRes): void {
    this.userAuthenticationResObj = new UserAuthenticationRes(res);

    const removableKeys = ['isAuthenticated', 'id_Token', 'refresh_Token', 'isMFAUserLoggedIn', 'isForcedToChangePassword'];

    removableKeys.forEach(key => delete res[key]);

    this.emailPassScreenHidden = Object.values(res).some(value => typeof value === 'boolean' && value);

    const actionMap: { [key: string]: () => void } = {
      isMFARequired: () => {},
      isWrongMFACode: () =>
        setTimeout(() => {
          if (!this.userAuthenticationResObj.isAccountLocked) {
            this.codeEntryFailedButton.nativeElement.click();
          }
        }, 1),
      isMFACodeExpire: () =>
        setTimeout(() => {
          if (!this.userAuthenticationResObj.isAccountLocked) {
            this.codeExpiredButton.nativeElement.click();
          }
        }, 1),
      isAccountLocked: () => setTimeout(() => this.accountLockedButton.nativeElement.click(), 1),
      isShowSecurityQuestion: () => {
        this.reset();
        this.getSecurityQuestionList();
      }
    };

    Object.entries(res).forEach(([key, value]) => {
      if (typeof value === 'boolean') {
        this.userAuthenticationResObj[key] = value;
      }
      if (value && actionMap[key]) {
        actionMap[key]();
      }
    });
  }

  openModal(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      ignoreBackdropClick: false
    };
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  reloadWindow(): void {
    window.location.reload();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
