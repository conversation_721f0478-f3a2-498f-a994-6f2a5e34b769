<nb-card class="portfolioSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Portfolios</h6>
        <div class="ms-auto">
          <button
            *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT]"
            nbButton
            [nbContextMenu]="bulkSitesItems"
            [nbContextMenuTag]="bulkSitesMenuTag"
            status="primary"
            size="small"
            class="linear-mode-button ms-2 button-h-100"
            (click)="onBulkSitesMenuOpen()"
          >
            <em class="fa-solid fa-bars"></em>
          </button>
          <input
            type="file"
            #uploadSiteUpdateFileInput
            class="d-none"
            accept=".xlsx"
            (change)="uploadSiteUpdateFile($event.target.files)"
          />
          <button
            *ngIf="portfolios?.length"
            class="linear-mode-button ms-2"
            nbButton
            status="primary"
            size="small"
            type="button"
            (click)="exportData()"
            [disabled]="loading"
          >
            <span class="d-none d-lg-inline-block">Export</span>
            <i class="d-inline-block d-lg-none fa fa-file-export"></i>
          </button>
          <button
            class="linear-mode-button ms-2"
            nbButton
            status="primary"
            size="small"
            [disabled]="loading"
            routerLink="add"
            type="button"
            *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT]"
          >
            <span class="d-none d-lg-inline-block">Add Portfolio</span>
            <i class="d-inline-block d-lg-none fa-solid fa-plus"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 portfolioFilter appFilter mb-3">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (clearParentList)="portfolios = []"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Portfolio List">
          <thead>
            <tr>
              <th (click)="sort('CustomerName', sortOptionList['CustomerName'])" id="customerName">
                <div class="d-flex align-items-center">
                  Customer
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['CustomerName'] === 'desc',
                      'fa-arrow-down': sortOptionList['CustomerName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'CustomerName'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('PortfolioName', sortOptionList['PortfolioName'])" id="portfolioName">
                <div class="d-flex align-items-center">
                  Portfolio
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['PortfolioName'] === 'desc',
                      'fa-arrow-down': sortOptionList['PortfolioName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'PortfolioName'
                    }"
                  ></span>
                </div>
              </th>
              <th class="text-end" id="noOfSite">Number of Sites</th>
              <th class="text-end" id="dcSize">kWdc</th>
              <th class="text-center" id="active" *ngIf="!checkAuthorisationsFn([roleType.FIELDTECH])">Active</th>
              <th class="text-center" id="action" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER, roleType.FIELDTECH])">
                {{ filterModel?.isArchive ? 'Archive' : 'Action' }}
              </th>
            </tr>
          </thead>
          <tbody *ngIf="portfolios?.length">
            <tr
              *ngFor="
                let portfolio of portfolios
                  | paginate
                    : {
                        itemsPerPage: filterModel.itemsCount,
                        currentPage: currentPage,
                        totalItems: total
                      };
                let i = index
              "
            >
              <td data-title="Customer">
                <a class="text-primary" (click)="gotoCustomer(portfolio.customerId)">{{ portfolio?.customerName }}</a>
              </td>
              <td data-title="Portfolio">
                <a [routerLink]="['detail/' + portfolio?.id]">{{ portfolio?.name }}</a>
              </td>
              <td data-title="Number of Sites" class="text-end">
                <a
                  (click)="gotoSite(portfolio?.customerId, portfolio?.id, portfolio?.noOfSite, portfolio?.isArchive)"
                  class="text-primary"
                  [ngClass]="{
                    'cursor-pointer': portfolio?.noOfSite > 0,
                    'not-allowed': portfolio?.noOfSite === 0 || portfolio?.isArchive
                  }"
                  >{{ portfolio?.noOfSite }}</a
                >
              </td>
              <td data-title="kWdc" class="text-end">{{ portfolio?.sumDcSize | sflRound | sflNumberWithCommas }}</td>
              <td data-title="Active" *ngIf="!checkAuthorisationsFn([roleType.FIELDTECH])" class="text-center">
                <qesolar-toggle
                  [rowData]="portfolio"
                  [isDisable]="!portfolio.isParentActive || portfolio.isArchive"
                  class="toggleicon"
                ></qesolar-toggle>
              </td>
              <td data-title="Action" class="text-center" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER, roleType.FIELDTECH])">
                <div class="d-md-flex justify-content-center" *ngIf="!portfolio.isArchive && isArchivedModal">
                  <a class="px-2 listgrid-icon" [routerLink]="['edit/' + portfolio?.id]">
                    <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                </div>
                <div class="d-flex justify-content-center" *ngIf="portfolio.isArchive">
                  <nb-toggle
                    (checkedChange)="archiveToggleChange($event, portfolio)"
                    [(checked)]="portfolio.isArchive"
                    [disabled]="portfolio.isParentArchive"
                    [nbTooltip]="portfolio.isParentArchive ? 'customer is archived' : 'UnArchive'"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    status="primary"
                    class="me-3"
                  ></nb-toggle>
                </div>
              </td>
            </tr>
          </tbody>
          <tbody *ngIf="!portfolios?.length">
            <tr>
              <td colspan="6" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="portfolios?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()" appendTo="body">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
