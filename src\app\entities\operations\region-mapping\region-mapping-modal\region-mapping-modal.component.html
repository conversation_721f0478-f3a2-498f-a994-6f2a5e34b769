<div class="modal-content" id="exclusion" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header ModalBody">
    <div class="container">
      <div class="row">
        <div class="col-8 ps-0">
          <h6>{{ type }} {{ title }}</h6>
        </div>
        <div class="col-4 text-end pe-0">
          <button type="button" class="close" aria-label="Close" (click)="closeModal()">
            <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
          </button>
        </div>
      </div>
    </div>
  </div>
  <form [formGroup]="regionMappingForm" (ngSubmit)="onSave()">
    <div class="modal-body ModalBody add-device-modal-body">
      <div class="row">
        <div class="col-12 mb-2" *ngIf="regionMappingForm.contains('regionName') || regionMappingForm.contains('regionId')">
          <ng-container *ngIf="!regionMappingForm.contains('subRegionName')">
            <label class="label" for="input-address">
              Region
              <span class="ms-1 text-danger" *ngIf="!regionMappingForm.contains('countyName')">*</span>
            </label>
            <input
              nbInput
              name="regionName"
              class="form-control"
              fullWidth
              placeholder="Region"
              formControlName="regionName"
              [readonly]="regionMappingForm.contains('countyName')"
              noLeadingSpace
            />
            <sfl-error-msg [control]="regionMappingForm.controls.regionName" fieldName="Region"></sfl-error-msg>
          </ng-container>
          <ng-container *ngIf="regionMappingForm.contains('subRegionName')">
            <label class="label" for="input-address">
              Region
              <span class="ms-1 text-danger">*</span>
            </label>
            <ng-select
              id="region"
              name="region"
              formControlName="regionId"
              [clearable]="false"
              notFoundText="No Region Found"
              placeholder="Select Region"
              bindLabel="name"
              bindValue="id"
              [items]="regionList"
              (change)="setFOMBasedOnRegion()"
            >
            </ng-select>
            <sfl-error-msg [control]="regionMappingForm.controls.regionId" fieldName="Region"></sfl-error-msg>
          </ng-container>
        </div>
        <div class="col-12 mb-2">
          <ng-container *ngIf="regionMappingForm.contains('subRegionName') || regionMappingForm.contains('countyName')">
            <label class="label" for="input-address"> FOM </label>
            <input nbInput name="fom" class="form-control" fullWidth placeholder="FOM" formControlName="userId" readonly />
          </ng-container>
          <ng-container *ngIf="!(regionMappingForm.contains('subRegionName') || regionMappingForm.contains('countyName'))">
            <label class="label" for="input-address">
              FOM
              <span class="ms-1 text-danger">*</span>
            </label>
            <ng-select
              id="fom"
              name="fom"
              formControlName="userId"
              [clearable]="false"
              notFoundText="No FOM Found"
              placeholder="Select FOM"
              bindLabel="name"
              bindValue="id"
              [items]="usersList"
            >
            </ng-select>
            <sfl-error-msg [control]="regionMappingForm.controls.userId" fieldName="FOM"></sfl-error-msg>
          </ng-container>
        </div>
        <div class="col-12 mb-2" *ngIf="regionMappingForm.contains('subRegionName') || regionMappingForm.contains('countyName')">
          <ng-container *ngIf="!regionMappingForm.contains('countyName')">
            <label class="label" for="input-address">
              Subregion
              <span class="ms-1 text-danger">*</span>
            </label>
            <input
              nbInput
              name="subRegionName"
              class="form-control"
              fullWidth
              placeholder="Subregion"
              formControlName="subRegionName"
              noLeadingSpace
            />
            <sfl-error-msg [control]="regionMappingForm.controls.subRegionName" fieldName="Subregion"></sfl-error-msg>
          </ng-container>
          <ng-container *ngIf="regionMappingForm.contains('countyName')">
            <label class="label" for="input-address">
              Subregion
              <span class="ms-1 text-danger">*</span>
            </label>
            <ng-select
              id="subRegionName"
              name="subRegionName"
              formControlName="subRegionId"
              [clearable]="false"
              notFoundText="No Subregion Found"
              placeholder="Select Subregion"
              bindLabel="name"
              bindValue="id"
              [items]="subRegionList"
              (change)="setRegionBasedOnSubRegion()"
            >
            </ng-select>
            <sfl-error-msg [control]="regionMappingForm.controls.subRegionId" fieldName="Subregion"></sfl-error-msg>
          </ng-container>
        </div>
        <div class="col-12 mb-2" *ngIf="regionMappingForm.contains('countyName')">
          <label class="label" for="input-address">
            County
            <span class="ms-1 text-danger">*</span>
          </label>
          <input
            nbInput
            name="countyName"
            class="form-control"
            fullWidth
            placeholder="County"
            formControlName="countyName"
            noLeadingSpace
          />
          <sfl-error-msg [control]="regionMappingForm.controls.countyName" fieldName="County"></sfl-error-msg>
        </div>
        <!-- <div class="col-12 mb-2" *ngIf="!(title === 'Region' && type === 'Add')">
          <label class="label" for="input-address"> Site Count </label>
          <input nbInput name="siteCount" class="form-control" fullWidth placeholder="Site Count" formControlName="siteCount" readonly />
        </div> -->
      </div>
    </div>
    <div class="modal-footer ModalFooter">
      <button nbButton status="primary" size="medium" id="deviceSubmit" type="submit" [disabled]="regionMappingForm.invalid">Save</button>
      <button nbButton status="basic" size="medium" type="button" (click)="closeModal()">Cancel</button>
    </div>
  </form>
</div>
