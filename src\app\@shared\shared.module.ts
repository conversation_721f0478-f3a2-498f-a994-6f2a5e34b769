import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule, DecimalPipe } from '@angular/common';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NbAuthModule } from '@nebular/auth';
import { NbDateFnsDateModule } from '@nebular/date-fns';
import { NbEvaIconsModule } from '@nebular/eva-icons';
import { NbSecurityModule } from '@nebular/security';
import { TableModule } from 'primeng/table';

import {
  NbAccordionModule,
  NbActionsModule,
  NbAlertModule,
  NbButtonModule,
  NbCalendarModule,
  NbCardModule,
  NbCheckboxModule,
  NbContextMenuModule,
  NbDatepickerModule,
  NbIconModule,
  NbInputModule,
  NbLayoutModule,
  NbListModule,
  NbMenuModule,
  NbProgressBarModule,
  NbRadioModule,
  NbSearchModule,
  NbSelectModule,
  NbSidebarModule,
  NbSpinnerModule,
  NbTabsetModule,
  NbTagModule,
  NbThemeModule,
  NbToggleModule,
  NbTooltipModule,
  NbUserModule
} from '@nebular/theme';
import { NgSelectModule } from '@ng-select/ng-select';
import { ContextMenuModule } from '@perfectmemory/ngx-contextmenu';
import { SignaturePadModule } from 'angular2-signaturepad';
import 'hammerjs';
import { NgFileDragDropModule } from 'ng-file-drag-drop';
import { Ng2OrderModule } from 'ng2-order-pipe';
import { CarouselModule } from 'ngx-bootstrap/carousel';
import { ModalModule } from 'ngx-bootstrap/modal';
import { CurrencyMaskInputMode, NgxCurrencyModule } from 'ngx-currency';
import { NgxEchartsModule } from 'ngx-echarts';
import { ImageCropperModule } from 'ngx-image-cropper';
import { NgxMaskModule } from 'ngx-mask';
import { NgxPaginationModule } from 'ngx-pagination';
import { GalleriaModule } from 'primeng/galleria';
import { GMapModule } from 'primeng/gmap';
import { PanelMenuModule } from 'primeng/panelmenu';
import { SidebarModule } from 'primeng/sidebar';
import { TreeModule } from 'primeng/tree';
import { TreeTableModule } from 'primeng/treetable';
import { CustomerSiteInfoArchiveComponent } from '../entities/customer-management/customer-add-edit/customer-site-info-archive/customer-site-info-archive.component';
import { ModalDashboardComponent } from '../entities/dashboard/modal-dashboard/modal-dashboard.component';
import { ModalLogsComponent } from '../entities/logs/modal-logs/modal-logs.component';
import { EditRotateImagesComponent } from '../entities/report/edit-rotate-images/edit-rotate-images.component';
import { GeneralImagesViewGuidelinesComponent } from '../entities/report/general-images-view-guidelines/general-images-view-guidelines.component';
import { ModelComponent } from '../entities/report/model/model.component';
import { MvpmChecklistViewDetailComponent } from '../entities/report/mvpm-report/mvpm-checklist/mvpm-checklist-view-detail/mvpm-checklist-view-detail.component';
import { ShareModelComponent } from '../entities/report/share-model/share-model.component';
import { ChecklistViewDetailsComponent } from '../entities/report/sitevisit-report/checklist/checklist-view-details/checklist-view-details.component';
import { JobStepsTypesComponent } from '../entities/report/sitevisit-report/job-steps-types/job-steps-type.component';
import { AddEditNonconformanceComponent } from '../entities/report/sitevisit-report/nonconformance/add-edit-nonconformance/add-edit-nonconformance.component';
import { DeleteMarkModalComponent } from '../entities/report/sitevisit-report/nonconformance/delete-mark-modal/delete-mark-modal.component';
import { NcSiteReportImagesComponent } from '../entities/report/sitevisit-report/nonconformance/nc-site-report-images/nc-site-report-images.component';
import { NcUserSiteReportImagesComponent } from '../entities/report/sitevisit-report/nonconformance/nc-user-site-report-images/nc-user-site-report-images.component';
import { GenerateReportSiteMapComponent } from '../entities/report/sitevisit-report/sitevisit-report-add-edit/generate-report-site-map/generate-report-site-map.component';
import { SitevisitUploadReportComponent } from '../entities/report/sitevisit-report/sitevisit-report-add-edit/sitevisit-upload-report/sitevisit-upload-report.component';
import { AddNewReportComponent } from '../entities/reports/add-new-report/add-new-report.component';
import { ViewReportDetailComponent } from '../entities/reports/view-report-detail/view-report-detail.component';
import { ImageGalleryModelComponent } from '../entities/site-management/image-gallery-model/image-gallery-model.component';
import { CommentComponent } from '../entities/ticket-management/comment/comment.component';
import { DeviceModelComponent } from '../entities/ticket-management/device-model/device-model.component';
import { ExclusionModelComponent } from '../entities/ticket-management/exclusion-model/exclusion-model.component';
import { ReadMoreComponent } from '../entities/ticket-management/read-more/read-more.component';
import { UserChangePasswordComponent } from '../entities/user-management/user-change-password/user-change-password.component';
import { ReschedulerModalComponent } from '../entities/workorder-management/rescheduler-modal/rescheduler-modal.component';
import { FooterComponent, HeaderComponent } from './components';
import { ShowMessageComponent } from './components/Show-Message/show-message/show-message.component';
import { BulkCreateNonConformanceTicketsComponent } from './components/bulk-create-non-conformance-tickets/bulk-create-non-conformance-tickets.component';
import { ChunkFileUploadComponent } from './components/chunk-file-upload/chunk-file-upload.component';
import { ChunkUploadProgressComponent } from './components/chunk-upload-progress/chunk-upload-progress.component';
import { CommonDropboxFileUploadComponent } from './components/common-dropbox-file-upload/common-dropbox-file-upload.component';
import { CompanyLogoComponent } from './components/company-logo/company-logo.component';
import { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';
import { DataNotFoundComponent } from './components/data-not-found/data-not-found.component';
import { ErrorMsgComponent } from './components/error-msg/error-msg.component';
import { FilterComponent } from './components/filter/filter.component';
import { ImageCropperComponent } from './components/image-cropper/image-cropper.component';
import { ImageDropboxGalleryComponent } from './components/image-dropbox-gallery/image-dropbox-gallery.component';
import { NotFoundComponent } from './components/not-found/not-found.component';
import { NotificationDetailsScreenComponent } from './components/notification-details-screen/notification-details-screen.component';
import { AriaLabelsDirective } from './components/otp-input/directives/aria-labels.directive';
import { AutoBlurDirective } from './components/otp-input/directives/auto-blur.directive';
import { AutoFocusDirective } from './components/otp-input/directives/auto-focus.directive';
import { InputNavigationsDirective } from './components/otp-input/directives/input-navigations.directive';
import { PasteDirective } from './components/otp-input/directives/paste.directive';
import { OtpInputComponent } from './components/otp-input/otp-input.component';
import { ShowerrorComponent } from './components/showerror/showerror.component';
import { SiteCheckinOutComponent } from './components/site-checkin-out/site-checkin-out.component';
import { ToggleComponent } from './components/toggle/toggle.component';
import { UserAvatarComponent } from './components/user-avatar/user-avatar.component';
import { AppConstants } from './constants';
import { AutofocusDirective } from './directives/auto-focus.directive';
import { ImageLoaderDirective } from './directives/image-loader.directive';
import { MobDirective } from './directives/mask.directive';
import { MultiPhoneNumbers } from './directives/multiPhone.directive';
import { NumbersOnlyDirective } from './directives/numbers-only.directive';
import { OnlyNumberDirective } from './directives/only-number.directive';
import { SetTableHeightDirective } from './directives/set-table-height.directive';
import { StrictnumbersDirective } from './directives/strictnumbers.directive';
import {
  EmailValidator,
  NbDateTimePickerValidatorValidator,
  NeedDifferentValueValidator,
  NeedSameValueValidator,
  NoLeadingSpaceDirective,
  PhoneValidator,
  RequiredNoSpaceValidator,
  ValidatorsDirective
} from './directives/validators.directive';
import { WhitespaceValidatorDirective } from './directives/whitespace-validator.directive';
import { OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent } from './layouts';
import { FileViewComponent } from './modals/file-view-modal/file-view.modal';
import { DateToUsersTimezonePipe } from './pipes/date-to-users-timezone.pipe';
import { TowDecimalPipe, TwoDecimalPlacesPipe } from './pipes/decimal.pipe';
import { GroupByPipe } from './pipes/groupby.pipe';
import { InverterHeatmapSortPipe } from './pipes/inverter-heatmap-sort.pipe';
import { DateWithCommaPipe, NamesWithBreakPipe } from './pipes/names-comma-separated.pipe';
import { NumberWithCommasPipe } from './pipes/number-with-commas.pipe';
import { RoundPipe } from './pipes/round.pipe';
import { SafeHTMLPipe } from './pipes/safe-html.pipe';
import { ScreenSizePipe } from './pipes/screensize.pipe';
import { SearchFilterPipe } from './pipes/search-filter.pipe';
import { SortByMonthPipe } from './pipes/sort-by-month.pipe';
import { ArraySortPipe } from './pipes/sort.pipe';
import { InterceptorService } from './services';
import { CORPORATE_THEME } from './styles/theme.corporate';
import { COSMIC_THEME } from './styles/theme.cosmic';
import { DARK_THEME } from './styles/theme.dark';
import { DEFAULT_THEME } from './styles/theme.default';
import { AutoSearchWithKeyboardDirective } from './directives/auto-search-with-keyboard.directive';
import { IpAddressDirective } from './directives/ip-address.directive';
import { LatitudeLongitudeDirective } from './directives/latitude-longitude.directive';
import { HasPermissionDirective } from './directives/has-permission.directive';

const NB_MODULES = [
  TreeTableModule,
  NbLayoutModule,
  NbMenuModule,
  NbUserModule,
  NbActionsModule,
  NbSearchModule,
  NbSidebarModule,
  NbContextMenuModule,
  NbSecurityModule,
  NbButtonModule,
  NbSelectModule,
  NbIconModule,
  NbEvaIconsModule,
  NbAlertModule,
  NbInputModule,
  NbCheckboxModule,
  NbAuthModule,
  NbCardModule,
  NbToggleModule,
  NbSpinnerModule,
  NbCalendarModule,
  NbTooltipModule,
  NbAccordionModule,
  NbTabsetModule,
  NbDatepickerModule,
  ModalModule,
  NgxPaginationModule,
  Ng2OrderModule,
  NbListModule,
  NbRadioModule,
  NgSelectModule,
  NbTagModule,
  NbProgressBarModule,
  GMapModule,
  GalleriaModule
];
const COMPONENTS = [
  HeaderComponent,
  FooterComponent,
  OneColumnLayoutComponent,
  ThreeColumnsLayoutComponent,
  TwoColumnsLayoutComponent,
  CompanyLogoComponent,
  NotFoundComponent,
  ToggleComponent,
  ErrorMsgComponent,
  ReadMoreComponent,
  UserAvatarComponent,
  DataNotFoundComponent,
  ChunkFileUploadComponent
];

const PIPES = [
  RoundPipe,
  NumberWithCommasPipe,
  GroupByPipe,
  SafeHTMLPipe,
  SearchFilterPipe,
  DateToUsersTimezonePipe,
  ArraySortPipe,
  ScreenSizePipe,
  TowDecimalPipe,
  TwoDecimalPlacesPipe,
  InverterHeatmapSortPipe,
  SortByMonthPipe,
  NamesWithBreakPipe,
  DateWithCommaPipe
];

const SFL_OTP_INPUT_DIRECTIVE = [PasteDirective, InputNavigationsDirective, AutoFocusDirective, AutoBlurDirective, AriaLabelsDirective];

const PRIME_MODULES = [TableModule, GMapModule, SidebarModule, TreeModule, PanelMenuModule];

export const customCurrencyMaskConfig = {
  align: 'left',
  allowNegative: true,
  allowZero: true,
  decimal: '.',
  precision: 2,
  prefix: '$ ',
  suffix: '',
  thousands: ',',
  nullable: true,
  min: null,
  max: null,
  inputMode: CurrencyMaskInputMode.FINANCIAL
};
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ...NB_MODULES,
    ModalModule.forChild(),
    NgxMaskModule.forRoot(),
    CarouselModule,
    NgFileDragDropModule,
    DragDropModule,
    RouterModule,
    NbDateFnsDateModule.forRoot({ format: AppConstants.fullDateFormat }),
    NgxCurrencyModule.forRoot(customCurrencyMaskConfig),
    ContextMenuModule,
    ImageCropperModule,
    SignaturePadModule,
    NgxEchartsModule.forRoot({
      echarts: () => import('echarts')
    }),
    ...PRIME_MODULES
  ],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ...COMPONENTS,
    ...PIPES,
    ...NB_MODULES,
    ValidatorsDirective,
    ImageLoaderDirective,
    AutofocusDirective,
    HasPermissionDirective,
    NumbersOnlyDirective,
    StrictnumbersDirective,
    MobDirective,
    RequiredNoSpaceValidator,
    NoLeadingSpaceDirective,
    PhoneValidator,
    NeedDifferentValueValidator,
    NeedSameValueValidator,
    NbDateTimePickerValidatorValidator,
    NgxMaskModule,
    NgFileDragDropModule,
    DragDropModule,
    RouterModule,
    NbDateFnsDateModule,
    SetTableHeightDirective,
    OnlyNumberDirective,
    IpAddressDirective,
    LatitudeLongitudeDirective,
    NgxCurrencyModule,
    EmailValidator,
    FilterComponent,
    ImageDropboxGalleryComponent,
    CommonDropboxFileUploadComponent,
    ContextMenuModule,
    ImageCropperModule,
    SignaturePadModule,
    NgxEchartsModule,
    ...PRIME_MODULES,
    MultiPhoneNumbers,
    OtpInputComponent,
    WhitespaceValidatorDirective,
    AutoSearchWithKeyboardDirective
  ],
  declarations: [
    ...COMPONENTS,
    ...PIPES,
    ...SFL_OTP_INPUT_DIRECTIVE,
    ToggleComponent,
    ConfirmDialogComponent,
    ValidatorsDirective,
    ImageLoaderDirective,
    AutofocusDirective,
    HasPermissionDirective,
    ModalDashboardComponent,
    CustomerSiteInfoArchiveComponent,
    ModalLogsComponent,
    ModelComponent,
    ShareModelComponent,
    NumbersOnlyDirective,
    StrictnumbersDirective,
    OnlyNumberDirective,
    IpAddressDirective,
    LatitudeLongitudeDirective,
    MobDirective,
    RequiredNoSpaceValidator,
    NoLeadingSpaceDirective,
    NeedDifferentValueValidator,
    NeedSameValueValidator,
    NbDateTimePickerValidatorValidator,
    PhoneValidator,
    AddEditNonconformanceComponent,
    NcSiteReportImagesComponent,
    NcUserSiteReportImagesComponent,
    GenerateReportSiteMapComponent,
    DeleteMarkModalComponent,
    SitevisitUploadReportComponent,
    UserChangePasswordComponent,
    ChecklistViewDetailsComponent,
    GeneralImagesViewGuidelinesComponent,
    EditRotateImagesComponent,
    ViewReportDetailComponent,
    AddNewReportComponent,
    CommentComponent,
    SetTableHeightDirective,
    ExclusionModelComponent,
    DeviceModelComponent,
    EmailValidator,
    FilterComponent,
    CommonDropboxFileUploadComponent,
    JobStepsTypesComponent,
    FileViewComponent,
    MvpmChecklistViewDetailComponent,
    ImageCropperComponent,
    ShowerrorComponent,
    ShowMessageComponent,
    ImageGalleryModelComponent,
    MultiPhoneNumbers,
    NotificationDetailsScreenComponent,
    ReschedulerModalComponent,
    ImageDropboxGalleryComponent,
    CommonDropboxFileUploadComponent,
    ChunkFileUploadComponent,
    ChunkUploadProgressComponent,
    SiteCheckinOutComponent,
    OtpInputComponent,
    BulkCreateNonConformanceTicketsComponent,
    WhitespaceValidatorDirective,
    AutoSearchWithKeyboardDirective
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: InterceptorService,
      multi: true
    },
    DateToUsersTimezonePipe,
    DecimalPipe
  ]
})
export class SharedModule {
  static forRoot() {
    return {
      ngModule: SharedModule,
      providers: [
        ...NbThemeModule.forRoot(
          {
            name: 'dark'
          },
          [DEFAULT_THEME, COSMIC_THEME, CORPORATE_THEME, DARK_THEME]
        ).providers
      ]
    };
  }
}
