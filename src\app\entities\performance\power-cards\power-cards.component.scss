.card-title {
  font-size: 1.1rem;
}

.card-subtitle {
  font-size: 1.25rem;
}

.badge {
  font-size: 0.75rem;
}

.bg-dark-theme {
  background-color: #100c2a !important;
  color: #ffffff !important;
}

.bg-light-theme {
  background-color: #ffffff !important;
  color: #000000;
}

/* Override Bootstrap text colors if needed */
.bg-dark-theme .card-title,
.bg-dark-theme .card-subtitle {
  color: #ffffff !important;
}

.bg-light-theme .card-title,
.bg-light-theme .card-subtitle {
  color: #000000 !important;
}

.status-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 4px;
}

.status-dot:hover {
  transform: scale(1.2);
}

.cursor-pointer {
  cursor: pointer;
}

.alert-details-body {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.min-h-80 {
  min-height: 80px;
}

/* Skeleton Loading Styles */
.skeleton-text {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  height: 16px;
  margin-bottom: 8px;
}

.skeleton-title {
  width: 70%;
  height: 20px;
  margin-bottom: 12px;
}

.skeleton-subtitle {
  width: 50%;
  height: 18px;
  margin-bottom: 4px;
}

.skeleton-small {
  width: 30%;
  height: 12px;
}

.skeleton-badge {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 12px;
  width: 60px;
  height: 24px;
}

.skeleton-icon {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  width: 24px;
  height: 24px;
}

.skeleton-dots {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.skeleton-dot {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: inline-block;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Dark theme skeleton styles */
.bg-dark-theme .skeleton-text,
.bg-dark-theme .skeleton-badge,
.bg-dark-theme .skeleton-icon,
.bg-dark-theme .skeleton-dot {
  background: linear-gradient(90deg, #2a2d3e 25%, #1a1d2e 50%, #2a2d3e 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}
