import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { AlertService } from '../../../../@shared/services';
import { AddRegionSubRegionResponse } from '../region-mapping.model';
import { RegionMappingService } from './../region-mapping.service';

@Component({
  selector: 'sfl-region-mapping-modal',
  templateUrl: './region-mapping-modal.component.html',
  styleUrls: ['./region-mapping-modal.component.scss']
})
export class RegionMappingModalComponent implements OnInit {
  public onClose: Subject<boolean>;
  @Input() type: 'Add' | 'Update' = 'Add';
  @Input() title: 'Region' | 'Subregion' | 'County' = 'Region';
  @Input() id: number;
  @Input() subRegionList: any[];
  @Input() regionList: any[];
  @Input() usersList: any[];
  recordDetails: any;
  regionMappingForm: FormGroup;

  loading = false;

  constructor(
    private readonly bsModalRef: BsModalRef,
    private readonly regionMappingService: RegionMappingService,
    private readonly alertService: AlertService
  ) {}

  ngOnInit() {
    this.onClose = new Subject();
    this.initializeForm();
  }

  initializeForm() {
    if (this.type === 'Add') {
      switch (this.title) {
        case 'Region':
          this.regionMappingForm = new FormGroup({
            regionName: new FormControl('', Validators.required),
            userId: new FormControl(null, Validators.required),
            siteCount: new FormControl('')
          });
          break;

        case 'Subregion':
          this.regionMappingForm = new FormGroup({
            regionId: new FormControl(null, Validators.required),
            userId: new FormControl('', Validators.required),
            siteCount: new FormControl(''),
            subRegionName: new FormControl('', Validators.required)
          });
          break;

        default:
          this.regionMappingForm = new FormGroup({
            region: new FormControl('', Validators.required),
            userId: new FormControl(null, Validators.required),
            siteCount: new FormControl(''),
            subRegionName: new FormControl('', Validators.required),
            county: new FormControl('', Validators.required)
          });
          break;
      }
    } else {
      switch (this.title) {
        case 'Region':
          this.regionMappingForm = new FormGroup({
            regionName: new FormControl('', Validators.required),
            userId: new FormControl(null, Validators.required),
            siteCount: new FormControl('')
          });
          break;

        case 'Subregion':
          this.regionMappingForm = new FormGroup({
            regionId: new FormControl(null, Validators.required),
            userId: new FormControl(null, Validators.required),
            siteCount: new FormControl(''),
            subRegionName: new FormControl('', Validators.required)
          });
          break;

        default:
          this.regionMappingForm = new FormGroup({
            regionName: new FormControl('', Validators.required),
            userId: new FormControl(null, Validators.required),
            siteCount: new FormControl(''),
            subRegionId: new FormControl('', Validators.required),
            countyName: new FormControl('', Validators.required)
          });
          break;
      }

      this.getRecordDetailsById();
    }
  }

  closeModal() {
    this.onClose.next(false);
    this.bsModalRef.hide();
  }

  setFOMBasedOnRegion() {
    const data = this.regionList.find(region => region.id === this.regionMappingForm.value.regionId);
    this.regionMappingForm.get('userId').setValue(data.userName || '');
    this.regionMappingForm.get('siteCount').setValue(data.siteCount);
  }

  setRegionBasedOnSubRegion() {
    const data = this.subRegionList.find(subRegion => subRegion.id === this.regionMappingForm.value.subRegionId);
    this.regionMappingForm.get('regionName').setValue(data.regionName || '');
    this.regionMappingForm.get('userId').setValue(data.userName || '');
    this.regionMappingForm.get('siteCount').setValue(data.siteCount);
  }

  getRecordDetailsById() {
    this.loading = true;

    switch (this.title) {
      case 'Region':
        this.regionMappingService.getRegionDetailsById(this.id).subscribe({
          next: response => {
            this.regionMappingForm.patchValue({
              regionName: response.regionName,
              userId: response.userId,
              siteCount: response.siteCount
            });

            this.loading = false;
          },
          error: () => (this.loading = false)
        });
        break;

      case 'Subregion':
        this.regionMappingService.getSubRegionDetailsById(this.id).subscribe({
          next: response => {
            this.regionMappingForm.patchValue({
              regionId: response.regionId,
              userId: response.userName,
              siteCount: response.siteCount,
              subRegionName: response.subRegionName
            });

            this.loading = false;
          },
          error: () => (this.loading = false)
        });
        break;

      default:
        this.regionMappingService.getCountyDetailsById(this.id).subscribe({
          next: response => {
            this.regionMappingForm.patchValue({
              countyName: response.countyName,
              subRegionId: response.subRegionId || null,
              regionName: response.regionName,
              userId: response.userName,
              siteCount: response.siteCount
            });

            this.loading = false;
          },
          error: () => (this.loading = false)
        });
        break;
    }
  }

  onSave() {
    this.regionMappingForm.markAllAsTouched();
    let dataToBeSent;
    this.loading = true;

    if (this.type === 'Update') {
      switch (this.title) {
        case 'Region':
          dataToBeSent = {
            id: this.id,
            userId: this.regionMappingForm.value.userId,
            regionName: this.regionMappingForm.value.regionName
          };

          this.regionMappingService.updateRegion(dataToBeSent).subscribe({
            next: (response: AddRegionSubRegionResponse) => {
              this.alertService.showSuccessToast(response.message);
              this.onClose.next(true);
              this.bsModalRef.hide();
              this.loading = false;
            },
            error: () => (this.loading = true)
          });
          break;

        case 'Subregion':
          dataToBeSent = {
            id: this.id,
            regionId: this.regionMappingForm.value.regionId,
            subRegionName: this.regionMappingForm.value.subRegionName
          };

          this.regionMappingService.updateSubRegion(dataToBeSent).subscribe({
            next: (response: AddRegionSubRegionResponse) => {
              this.alertService.showSuccessToast(response.message);
              this.onClose.next(true);
              this.bsModalRef.hide();
              this.loading = false;
            },
            error: () => (this.loading = true)
          });
          break;

        default:
          dataToBeSent = {
            id: this.id,
            subRegionId: this.regionMappingForm.value.subRegionId,
            countyName: this.regionMappingForm.value.countyName
          };

          this.regionMappingService.updateCounty(dataToBeSent).subscribe({
            next: (response: AddRegionSubRegionResponse) => {
              this.alertService.showSuccessToast(response.message);
              this.onClose.next(true);
              this.bsModalRef.hide();
              this.loading = false;
            },
            error: () => (this.loading = true)
          });
          break;
      }
    } else {
      switch (this.title) {
        case 'Region':
          dataToBeSent = {
            id: 0,
            userId: this.regionMappingForm.value.userId,
            regionName: this.regionMappingForm.value.regionName
          };

          this.regionMappingService.createRegion(dataToBeSent).subscribe({
            next: (response: AddRegionSubRegionResponse) => {
              this.alertService.showSuccessToast(response.message);
              this.onClose.next(true);
              this.bsModalRef.hide();
              this.loading = false;
            },
            error: () => (this.loading = true)
          });
          break;

        case 'Subregion':
          dataToBeSent = {
            id: 0,
            regionId: this.regionMappingForm.value.regionId,
            subRegionName: this.regionMappingForm.value.subRegionName
          };

          this.regionMappingService.createSubRegion(this.regionMappingForm.value).subscribe({
            next: (response: AddRegionSubRegionResponse) => {
              this.alertService.showSuccessToast(response.message);
              this.onClose.next(true);
              this.bsModalRef.hide();
              this.loading = false;
            },
            error: () => (this.loading = true)
          });
          break;

        default:
          this.regionMappingForm = new FormGroup({
            region: new FormControl('', Validators.required),
            userId: new FormControl(null, Validators.required),
            siteCount: new FormControl(''),
            subRegionName: new FormControl('', Validators.required),
            county: new FormControl('', Validators.required)
          });
          break;
      }
    }
  }
}
