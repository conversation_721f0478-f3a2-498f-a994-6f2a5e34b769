import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, Renderer2, Template<PERSON>ef, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { AppConstants } from '../../../@shared/constants';
import { StorageService } from '../../../@shared/services/storage.service';
import { QEAnalyticsPasswordAuthEnum, QEAnalyticsPasswordOperationEnum } from '../models/qe-analytics.enum';
import { QEAnalyticsPasswordObj } from '../models/qe-analytics.model';
import { QEAnalyticsService } from '../services/qe-analytics.service';

@Component({
  selector: 'sfl-qe-analytics-password',
  templateUrl: './qe-analytics-password.component.html',
  styleUrls: ['./qe-analytics-password.component.scss']
})
export class QEAnalyticsPasswordComponent implements OnInit, OnDestroy {
  @ViewChild('qeAnalyticsPasswordButton', { static: true }) qeAnalyticsPasswordButton;

  private subscription = new Subscription();
  modalRef: BsModalRef;
  qeAnalyticsPasswordObj: QEAnalyticsPasswordObj = new QEAnalyticsPasswordObj();
  pwdOperation = QEAnalyticsPasswordOperationEnum;
  loading = false;
  environment = environment.env;

  constructor(
    private readonly modalService: BsModalService,
    private readonly renderer: Renderer2,
    private readonly router: Router,
    private readonly storageService: StorageService,
    private readonly qeAnalyticsService: QEAnalyticsService
  ) {}

  ngOnInit(): void {
    setTimeout(() => {
      this.qeAnalyticsPasswordButton.nativeElement.click();
      return;
    }, 1);
  }

  openModal(template: TemplateRef<any>, pwdOperation: QEAnalyticsPasswordOperationEnum): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }

    const ngModalOptions: ModalOptions = {
      backdrop: true,
      ignoreBackdropClick: true,
      keyboard: false,
      animated: true,
      class: 'qe-analytics-password-modal-dialog'
    };

    this.modalRef = this.modalService.show(template, ngModalOptions);

    this.qeAnalyticsPasswordObj = new QEAnalyticsPasswordObj(pwdOperation);

    this.addCustomClassToModal();
  }

  private addCustomClassToModal(): void {
    const modalElement = document.querySelector('.modal');
    if (modalElement?.querySelector('.qe-analytics-password-modal-dialog')) {
      this.renderer.setStyle(modalElement, 'z-index', '99999');
    }
  }

  onPasswordVerified(isRouteActivated: boolean): void {
    this.modalRef.hide();
    if (isRouteActivated) {
      this.storageService.set(AppConstants.qeAnalyticsPasswordAuthKey, QEAnalyticsPasswordAuthEnum.USER_AUTHENTICATED);
      this.router.navigate(['entities/admin/analytics']);
    } else {
      this.router.navigate(['entities/admin/users']);
    }
  }

  resetQEAnalyticsPassword(pwdOperation: QEAnalyticsPasswordOperationEnum): void {
    const qeAnalyticsPasswordObj = new QEAnalyticsPasswordObj(pwdOperation);
    this.loading = true;
    this.subscription.add(
      this.qeAnalyticsService.resetQEAnalyticsPassword(qeAnalyticsPasswordObj).subscribe({
        next: res => {
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  updateQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): void {
    this.loading = true;
    this.subscription.add(
      this.qeAnalyticsService.updateQEAnalyticsPassword(qeAnalyticsPasswordObj).subscribe({
        next: res => {
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  authenticateQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): void {
    this.loading = true;
    this.subscription.add(
      this.qeAnalyticsService.authenticateQEAnalyticsPassword(qeAnalyticsPasswordObj).subscribe({
        next: res => {
          this.onPasswordVerified(true);
          this.loading = false;
        },
        error: e => {
          this.onPasswordVerified(false);
          this.loading = false;
        }
      })
    );
  }

  onUpdateQEAnalyticsPassword(qeAnalyticsPasswordForm: NgForm): void {
    if (qeAnalyticsPasswordForm.form.valid) {
      this.updateQEAnalyticsPassword(this.qeAnalyticsPasswordObj);
    } else {
      qeAnalyticsPasswordForm.form.markAllAsTouched();
    }
  }

  onSubmit(qeAnalyticsPasswordForm: NgForm): void {
    if (qeAnalyticsPasswordForm.form.valid) {
      this.authenticateQEAnalyticsPassword(this.qeAnalyticsPasswordObj);
    } else {
      qeAnalyticsPasswordForm.form.markAllAsTouched();
    }
  }

  ngOnDestroy(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.subscription.unsubscribe();
  }
}
