import { QE_MENU_MODULE_ENUM } from '../enums';

export enum QE_MENU_ENUMS {
  QE_NO_MENU = 0,
  QE_HEADER_MENU = 1,
  JPT_SI_CUSTOMERS,
  JPT_SI_SITES,
  JPT_PM_WORK_ORDERS,
  JPT_CM_ALL_TICKETS,
  JPT_PER_DASHBOARD,
  JPT_PER_POWER_CARDS,
  JPT_PER_POWER_CHARTS
}

export enum FIELD_VALIDATION_TYPE {
  MIN = 'min',
  MAX = 'max',
  EXACT = 'exact'
}

export const JUMP_TO_MENU_LIST = {
  [QE_MENU_ENUMS.QE_HEADER_MENU]: {
    [QE_MENU_MODULE_ENUM.SITE_INFO]: [
      QE_MENU_MODULE_ENUM.SI_DASHBOARD,
      QE_MENU_MODULE_ENUM.SI_CUSTOMERS,
      QE_MENU_MODULE_ENUM.SI_PORTFOLIOS,
      QE_MENU_MODULE_ENUM.SI_SITES,
      QE_MENU_MODULE_ENUM.SI_DEVICES,
      QE_MENU_MODULE_ENUM.SI_EQUIPMENT
    ],
    [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.PM_DASHBOARD,
      QE_MENU_MODULE_ENUM.PM_SCOPE,
      QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
      QE_MENU_MODULE_ENUM.PM_REPORTS,
      QE_MENU_MODULE_ENUM.PM_SITE_AUDIT,
      QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE
    ],
    [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.CM_DASHBOARD,
      QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
      QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT,
      QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT,
      QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
      QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT,
      QE_MENU_MODULE_ENUM.CM_MAP_REPORT,
      QE_MENU_MODULE_ENUM.CM_RMA_REPORT
    ],
    [QE_MENU_MODULE_ENUM.AVAILABILITY]: [
      QE_MENU_MODULE_ENUM.AVB_DASHBOARD,
      QE_MENU_MODULE_ENUM.AVB_REPORTS,
      QE_MENU_MODULE_ENUM.AVB_DATA_TABLE,
      QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS
    ],
    [QE_MENU_MODULE_ENUM.PERFORMANCE]: [
      QE_MENU_MODULE_ENUM.PER_DASHBOARD,
      QE_MENU_MODULE_ENUM.PER_POWER_CHARTS,
      QE_MENU_MODULE_ENUM.PER_POWER_CARDS,
      QE_MENU_MODULE_ENUM.PER_REPORTS,
      QE_MENU_MODULE_ENUM.PER_DATA_TABLE,
      QE_MENU_MODULE_ENUM.PER_ALERTS
    ],
    [QE_MENU_MODULE_ENUM.SAFETY]: [
      QE_MENU_MODULE_ENUM.SF_JHA,
      QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN,
      QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA,
      {
        [QE_MENU_MODULE_ENUM.SF_SETTINGS]: [
          QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO,
          QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE,
          QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP,
          QE_MENU_MODULE_ENUM.SF_SET_HAZARD,
          QE_MENU_MODULE_ENUM.SF_SET_BARRIER,
          QE_MENU_MODULE_ENUM.SF_SET_JHA,
          QE_MENU_MODULE_ENUM.SF_SET_LOTO
        ]
      }
    ],
    [QE_MENU_MODULE_ENUM.OPERATIONS]: [
      QE_MENU_MODULE_ENUM.OP_REPORTS,
      QE_MENU_MODULE_ENUM.OP_REGION_MAPPING,
      QE_MENU_MODULE_ENUM.OP_SERVICES,
      QE_MENU_MODULE_ENUM.OP_CONTRACTS,
      QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS
    ],
    [QE_MENU_MODULE_ENUM.ADMIN]: [
      QE_MENU_MODULE_ENUM.AD_USERS,
      QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING,
      QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG,
      QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER,
      QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY,
      QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD,
      QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER,
      QE_MENU_MODULE_ENUM.AD_EMAIL_LOG,
      QE_MENU_MODULE_ENUM.AD_ANALYTICS
    ]
  },

  // jump to menu
  [QE_MENU_ENUMS.JPT_SI_CUSTOMERS]: {
    [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
      QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
      QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT
    ],
    [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.PM_DASHBOARD,
      QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
      QE_MENU_MODULE_ENUM.PM_SCOPE
    ],
    [QE_MENU_MODULE_ENUM.PERFORMANCE]: [
      QE_MENU_MODULE_ENUM.PER_DASHBOARD,
      QE_MENU_MODULE_ENUM.PER_POWER_CHARTS,
      QE_MENU_MODULE_ENUM.PER_POWER_CARDS,
      QE_MENU_MODULE_ENUM.PER_REPORTS
    ]
  },
  [QE_MENU_ENUMS.JPT_SI_SITES]: {
    [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
      QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
      QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT
    ],
    [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.PM_DASHBOARD,
      QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
      QE_MENU_MODULE_ENUM.PM_SCOPE
    ],
    [QE_MENU_MODULE_ENUM.PERFORMANCE]: [
      QE_MENU_MODULE_ENUM.PER_DASHBOARD,
      QE_MENU_MODULE_ENUM.PER_POWER_CHARTS,
      QE_MENU_MODULE_ENUM.PER_POWER_CARDS,
      QE_MENU_MODULE_ENUM.PER_REPORTS
    ]
  },
  [QE_MENU_ENUMS.JPT_PM_WORK_ORDERS]: {
    [QE_MENU_MODULE_ENUM.SITE_INFO]: [
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST
    ],
    [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
      QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
      QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT
    ],
    [QE_MENU_MODULE_ENUM.PERFORMANCE]: [
      QE_MENU_MODULE_ENUM.PER_DASHBOARD,
      QE_MENU_MODULE_ENUM.PER_POWER_CHARTS,
      QE_MENU_MODULE_ENUM.PER_POWER_CARDS,
      QE_MENU_MODULE_ENUM.PER_REPORTS
    ]
  },
  [QE_MENU_ENUMS.JPT_CM_ALL_TICKETS]: {
    [QE_MENU_MODULE_ENUM.SITE_INFO]: [
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST
    ],
    [QE_MENU_MODULE_ENUM.PERFORMANCE]: [
      QE_MENU_MODULE_ENUM.PER_DASHBOARD,
      QE_MENU_MODULE_ENUM.PER_POWER_CHARTS,
      QE_MENU_MODULE_ENUM.PER_POWER_CARDS,
      QE_MENU_MODULE_ENUM.PER_REPORTS
    ]
  },
  [QE_MENU_ENUMS.JPT_PER_DASHBOARD]: {
    [QE_MENU_MODULE_ENUM.SITE_INFO]: [
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST
    ],
    [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
      QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
      QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT
    ],
    [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.PM_DASHBOARD,
      QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
      QE_MENU_MODULE_ENUM.PM_SCOPE
    ]
  },
  [QE_MENU_ENUMS.JPT_PER_POWER_CHARTS]: {
    [QE_MENU_MODULE_ENUM.SITE_INFO]: [
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST
    ],
    [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
      QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
      QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT
    ],
    [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.PM_DASHBOARD,
      QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
      QE_MENU_MODULE_ENUM.PM_SCOPE
    ]
  },
  [QE_MENU_ENUMS.JPT_PER_POWER_CARDS]: {
    [QE_MENU_MODULE_ENUM.SITE_INFO]: [
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION,
      QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST
    ],
    [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
      QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
      QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT
    ],
    [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: [
      QE_MENU_MODULE_ENUM.PM_DASHBOARD,
      QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
      QE_MENU_MODULE_ENUM.PM_SCOPE
    ]
  }
};

export const JUMP_TO_MENU_REQUIRED_FIELDS = {
  [QE_MENU_ENUMS.JPT_SI_CUSTOMERS]: ['customerId'],
  [QE_MENU_ENUMS.JPT_SI_SITES]: ['siteId'],
  [QE_MENU_ENUMS.JPT_PM_WORK_ORDERS]: ['customerId', 'siteId', 'portfolioId'],
  [QE_MENU_ENUMS.JPT_CM_ALL_TICKETS]: ['customerId', 'siteId', 'portfolioId'],
  [QE_MENU_ENUMS.JPT_PER_DASHBOARD]: ['siteId'],
  [QE_MENU_ENUMS.JPT_PER_POWER_CHARTS]: ['siteIds'],
  [QE_MENU_ENUMS.JPT_PER_POWER_CARDS]: ['siteIds']
};

export const JUMP_TO_MENU_OPTIONAL_FIELDS = {
  [QE_MENU_ENUMS.JPT_SI_CUSTOMERS]: [],
  [QE_MENU_ENUMS.JPT_SI_SITES]: [],
  [QE_MENU_ENUMS.JPT_PM_WORK_ORDERS]: [],
  [QE_MENU_ENUMS.JPT_CM_ALL_TICKETS]: [],
  [QE_MENU_ENUMS.JPT_PER_DASHBOARD]: [],
  [QE_MENU_ENUMS.JPT_PER_POWER_CHARTS]: [],
  [QE_MENU_ENUMS.JPT_PER_POWER_CARDS]: []
};

export const JUMP_TO_MENU_FIELD_VALIDATIONS = {
  [QE_MENU_ENUMS.JPT_PER_POWER_CHARTS]: {
    siteIds: { [FIELD_VALIDATION_TYPE.EXACT]: 1 }
  },
  [QE_MENU_ENUMS.JPT_PER_POWER_CARDS]: {
    siteIds: { [FIELD_VALIDATION_TYPE.EXACT]: 1 }
  }
};
