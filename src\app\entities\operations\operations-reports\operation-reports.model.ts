import { SafeHtml } from '@angular/platform-browser';

export class OperationsReportsMenu {
  public label: string;
  public icon: string;
  public routerLink: string;
}
export class FilterModel {
  public customerIds: number[] = [];
  public portfolioIds: number[] = [];
  public siteIds: number[] = [];
  public regionIds: number[] = [];
  public subregionIds: number[] = [];
  public assessmentTypes: number[] = [];
  public year: number = new Date().getFullYear();
  public quarter: string[] = [];
  public month: number[] = [];
  public thresholdDays?: number = 0;
  public isAllCustomerSelected: boolean = true;
  public isAllPortfolioSelected: boolean = true;
  public isAllSiteSelected: boolean = true;
  public isAllAssessmentTypeSelected: boolean = true;
  public isAllRegionSelected: boolean = true;
  public isAllSubregionSelected: boolean = true;
  public pmTableStatus = 0;
  public sortBy = PMTableSortingKey.customername;
  public direction = 'ASC';
  public page = 0;
  public itemsCount = 5;
  public search = new PmWoTableSearch();
}

export enum PMTableSortingKey {
  customername = 'customername',
  sitename = 'sitename',
  assesmenttype = 'assesmenttype',
  tentativemonth = 'tentativemonth',
  datescheduled = 'datescheduled',
  frequency = 'frequency'
}

export class PmWoTableSearch {
  public customerName = '';
  public siteName = '';
  public assesmentType = '';
  public frequency = '';
  public tentativeMonth = '';
  public dateScheduled = '';
}

export interface PMCompletionResponse {
  assessmentType: string;
  chartLabel: string;
  workOrderPlanInfo: EChartObject[];
  workOrderPlanChart: ForecastApiResponseModal[];
  completionForecastData: ForecastApiResponseModal[];
}

export interface WorkOrderPlanModal {
  planInfoData: EChartObject[];
  PlanChartData: ForecastApiResponseModal[];
  assessmentType: string;
}
export interface PMCompletionForecastModal {
  PMCompletionChart: ForecastApiResponseModal[];
  assessmentType: string;
}

export interface ForecastApiResponseModal {
  chartHeader?: string;
  dataOf: string[];
  labels: string[];
  eChartObjects: EChartObject[];
}

export interface ReschedulePivotResponseModal {
  chartLabel: string;
  byCategoryChartData: ForecastApiResponseModal;
  pivotTableData: PivotTableData;
}

export interface Columns {
  data: string[];
}

export interface PivotTableData {
  headers: string[];
  columnsData: Columns[];
}
export interface EChartObject {
  name: string;
  data: number[];
}
export class AllReportDropdown {
  public ids: number[] = [];
  public customerIds?: number[] = [];
  public isActive = true;
}

export class ReportType {
  public abbreviation: string;
  public id: number;
  public isActive: boolean;
  public name: string;
  public item_id: number;
  public item_text: string;
}

export class PivotApiRequestModal {
  public customerIds: number[] = [];
  public portfolioIds: number[] = [];
  public siteIds: number[] = [];
  public regionIds: number[] = [];
  public subregionIds: number[] = [];
  public assessmentTypes: number[] = [];
  public year: number = new Date().getFullYear();
  public quarter: string[];
  public month: number[];
  public primaryMetric: number;
  public secondaryMetric: number;
  public isAllCustomerSelected: boolean = true;
  public isAllPortfolioSelected: boolean = true;
  public isAllSiteSelected: boolean = true;
  public isAllAssessmentTypeSelected: boolean = true;
  public isAllRegionSelected: boolean = true;
  public isAllSubregionSelected: boolean = true;
}

export interface DashboardData {
  id: number;
  menuTitle: string;
  content: string | SafeHtml;
}

export enum PMTableStatus {
  Total,
  Unscheduled,
  Tentative,
  'On Time',
  Late,
  Pending,
  'Past Due',
  'Pending Reschedule'
}

export enum PMTableStatusStr {
  Total = 'Total',
  Unscheduled = 'Unscheduled',
  Tentative = 'Tentative',
  OnTime = 'On Time',
  Late = 'Late',
  Pending = 'Pending',
  PastDue = 'Past Due',
  PendingReschedule = 'Pending Reschedule'
}

export class PMTableData {
  public pmTableDto: PmTableDto[] = [];
  public total: number = 0;
}

export interface PmTableDto {
  workOrderId: number;
  assesmentType: string;
  customerName: string;
  customerPortFolio: string;
  portfolioName: string;
  siteName: string;
  frequency: string;
  tentativeMonth: number;
  tentativeMonthStr: string;
  isReschedule: boolean;
  dateScheduled: string;
  isSelected: boolean;
  isCheckboxDisabled: boolean;
  woStatus: string;
  rescheduleDate: string;
  workOrderStatus: number;
  reportCompleteDate: string;
}

export interface PmCompletionWoPaginationParam {
  pageSize: number;
  currentPage: number;
  woStatus: number;
}

export interface PmEmitEvent {
  filter: FilterModel;
  reload: boolean;
}
