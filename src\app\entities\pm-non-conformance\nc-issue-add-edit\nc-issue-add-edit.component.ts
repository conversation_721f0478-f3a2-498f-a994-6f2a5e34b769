import { Component, EventEmitter, Input, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { ADD_EDIT_COPY_MODE, CmpFilterModel } from '../pm-non-conformance.model';
import { PmNonConformanceService } from '../pm-non-conformance.service';

@Component({
  selector: 'sfl-nc-issue-add-edit',
  templateUrl: './nc-issue-add-edit.component.html',
  styleUrls: ['./nc-issue-add-edit.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class NcIssueAddEditComponent implements OnInit, OnDestroy {
  public onClose: Subject<boolean>;
  @Input() issueItem;
  @Input() issueMode;
  componentList = [];
  loading = false;
  subscription: Subscription = new Subscription();
  public event: EventEmitter<any> = new EventEmitter();

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly storageService: StorageService,
    private readonly alertService: AlertService,
    private readonly pmNonConformanceService: PmNonConformanceService
  ) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
    this.getComponentsList();
    if (this.issueMode === ADD_EDIT_COPY_MODE.COPY) {
      this.issueItem = { ...this.issueItem, id: 0, issueObservation: 'Copy of ' + this.issueItem.issueObservation };
    }
  }

  getComponentsList(): void {
    const params = new CmpFilterModel();
    this.loading = true;
    this.subscription.add(
      this.pmNonConformanceService.getNcComponentsList(params).subscribe({
        next: (res: any[]) => {
          this.componentList = this.issueMode === 'EDIT' ? res : res.filter(item => item.isActive);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  public onConfirm(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  onAddEditIssue() {
    this.loading = true;
    this.subscription.add(
      this.pmNonConformanceService.addUpdateIssueObservation(this.issueItem).subscribe({
        next: (res: any) => {
          this.alertService.showSuccessToast(res.message);
          this.loading = false;
          this.event.emit(true);
          this._bsModalRef.hide();
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
