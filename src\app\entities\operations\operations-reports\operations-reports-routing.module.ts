import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CustomDashboardComponent } from './custom-dashboard/custom-dashboard.component';
import { OperationsReportsComponent } from './operations-reports.component';
import { PmCompletionComponent } from './pm-completion/pm-completion.component';
import { ReschedulePivotComponent } from './reschedule-pivot/reschedule-pivot.component';

const routes: Routes = [
  {
    path: '',
    component: OperationsReportsComponent,
    children: [
      {
        path: '',
        redirectTo: 'pm-completion-report',
        pathMatch: 'full'
      },
      {
        path: 'pm-completion-report',
        component: PmCompletionComponent
      },
      {
        path: 'reschedule-pivot-reports',
        component: ReschedulePivotComponent
      },
      {
        path: 'custom-dashboard/:id',
        component: CustomDashboardComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class OperationsReportsRoutingModule {}
