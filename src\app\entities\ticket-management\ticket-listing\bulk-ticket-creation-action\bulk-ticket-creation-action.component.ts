import { DatePipe } from '@angular/common';
import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { fork<PERSON>oin, Subject, Subscription } from 'rxjs';
import { CommonFilter } from '../../../../@shared/components/filter/common-filter.model';
import { AppConstants } from '../../../../@shared/constants';
import { Dropdown } from '../../../../@shared/models/dropdown.model';
import { Site, SiteFilterData } from '../../../../@shared/models/site.model';
import { AlertService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { SiteDevices, SiteDevicesList } from '../../../site-device/site-device.model';
import { SiteDeviceService } from '../../../site-device/site-device.service';
import { SiteService } from '../../../site-management/site.service';
import { fieldNameMap, idMappings, ProductionLossList, TicketBulkCreation, TicketPriorityMapping } from '../../ticket.model';
import { TicketService } from '../../ticket.service';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../../@shared/components/filter/filter.model';
import { ROLE_TYPE } from '../../../../@shared/enums';
import { checkAuthorisations } from '../../../../@shared/utils';

@Component({
  selector: 'sfl-bulk-ticket-creation-action',
  templateUrl: './bulk-ticket-creation-action.component.html',
  styleUrls: ['./bulk-ticket-creation-action.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class BulkTicketCreationActionComponent implements OnInit {
  @Input() selectedBulkAction;
  @Input() total: number;
  filterModel: CommonFilter = new CommonFilter();
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  siteDeviceList: (SiteDevices | Site)[];
  selectedBulkCreateTickets: (SiteDevices | Site)[] = [];
  loading = false;
  subscription: Subscription = new Subscription();
  sortOptionList = {
    Site: 'asc',
    DeviceType: 'asc',
    DeviceName: 'asc',
    DeviceID: 'asc',
    QeSiteId: 'asc',
    SiteName: 'asc',
    ACSize: 'asc',
    SiteDeviceCount: 'asc',
    state: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc',
    XFMR: 'asc',
    INV: 'asc',
    DCSize: 'asc',
    CustomerPortfolio: 'asc'
  };
  modalRef: BsModalRef;
  currentStep: number = 1;
  totalSteps: number = 7;
  isMasterSel = false;
  ticketBulkCreationForm: TicketBulkCreation = new TicketBulkCreation();
  minDate = new Date();
  priorityList = TicketPriorityMapping;
  productionLossList = ProductionLossList;
  lossTypeList: Dropdown[] = [];
  filterDetails: FilterDetails = new FilterDetails();
  showCommonFilter = false;
  isFilterDisplay = true;
  viewPage = FILTER_PAGE_NAME.CM_BULK_TICKET_CREATION;
  selectedTicketType = null;
  completedCount = 0;
  apiCount = 0;
  failedEntityNumber = [];
  fieldNameMap = fieldNameMap;
  idMappings = idMappings;
  public onClose: Subject<boolean> = new Subject();
  constructor(
    public _bsModalRef: BsModalRef,
    private readonly datePipe: DatePipe,
    private readonly ticketService: TicketService,
    private readonly siteService: SiteService,
    private readonly siteDeviceService: SiteDeviceService,
    private readonly storageService: StorageService,
    private readonly alertService: AlertService
  ) {}

  ngOnInit(): void {
    const apiArray = [this.ticketService.getlossTypeList()];
    const tempObj = ['lossTypeList'];
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of tempObj.entries()) {
          this[value] = res[index];
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });

    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = 'SiteName';

    if (this.filterModel.direction && this.filterModel.sortBy) {
      this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
    }
    if (this.filterModel.page) {
      this.currentPage = this.filterModel.page + 1;
    }
    if (this.filterModel.itemsCount) {
      this.pageSize = this.filterModel.itemsCount;
    }
  }

  onTicketTypeChange() {
    this.storageService.clear(this.viewPage);
    this.siteDeviceList = [];
    this.isMasterSel = false;
    this.selectedBulkCreateTickets = [];
    this.completedCount = 0;
    this.apiCount = 0;
  }

  getTableData() {
    const excludedKeys = ['entityIds', 'isSendEmail', 'isSiteIds'];

    return Object.keys(this.ticketBulkCreationForm || {})
      .filter(key => this.shouldIncludeKey(key, excludedKeys))
      .map(key => ({
        fieldName: this.fieldNameMap[key] || key,
        value: this.getDisplayName(key, this.ticketBulkCreationForm[key]),
        action: key === 'comment' ? 'Added' : 'Updated'
      }))
      .filter(item => this.isValidValue(item.value));
  }

  private shouldIncludeKey(key: string, excludedKeys: string[]): boolean {
    return !excludedKeys.includes(key);
  }

  private isValidValue(value: any): boolean {
    return value !== null && value !== '' && value !== undefined;
  }

  getDisplayName(fieldName: string, fieldValue: any): string {
    if (this.idMappings[fieldName] && this.idMappings[fieldName][fieldValue] !== undefined) {
      return this.idMappings[fieldName][fieldValue];
    } else if (fieldName === 'lossType') {
      return this.getLossTypeName(fieldValue);
    } else if (fieldName === 'openedDate') {
      return this.datePipe.transform(fieldValue, AppConstants.fullDateFormat);
    } else if (fieldName === 'productionLoss') {
      return this.getProductionLossName(fieldValue);
    }
    return fieldValue;
  }

  getLossTypeName(lossTypeId: number) {
    const billingStatus = this.lossTypeList.find(lossType => lossType.id === lossTypeId);
    return billingStatus ? billingStatus.name : '';
  }

  getProductionLossName(productionLoss: number) {
    if (productionLoss === 1) {
      return 'No';
    } else if (productionLoss === 0) {
      return 'Yes';
    } else {
      return '--';
    }
  }

  initFilterDetails(selectedTicketType): void {
    this.filterDetails.filter_section_name = 'bulkTicketsCreationFilterSection';
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.SHOW_STATUS.show = true;
    filterItem.AUTOMATION_DATA_SOURCE.show = true;
    filterItem.AUTOMATION_SITE.show = true;
    if (!checkAuthorisations([ROLE_TYPE.CUSTOMER]) && selectedTicketType === 1) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
    }

    if (selectedTicketType === 1) {
      filterItem.SEARCH_BOX.show = true;
      filterItem.STATE.show = true;
      filterItem.STATE.multi = true;
      // filterItem.SHOW_NERC.show = true;
      filterItem.SHOW_NERC_SITE_TYPE.show = true;
    }

    if (selectedTicketType === 2) {
      filterItem.DEVICE_TYPE.show = true;
      filterItem.DEVICE_TYPE.multi = true;
      filterItem.DEVICE.show = true;
      filterItem.MODEL.show = true;
      filterItem.MFG.show = true;
      filterItem.SITE.show = true;
      filterItem.SITE.multi = true;
    }

    this.filterDetails.default_direction = 'desc';
    this.filterDetails.filter_item = filterItem;
    setTimeout(() => {
      this.showCommonFilter = true;
    }, 0);
  }

  openDateChanged() {
    if (this.ticketBulkCreationForm.openedDate) {
      this.minDate = new Date(this.ticketBulkCreationForm.openedDate);
      this.minDate.setHours(0, 0, 0, 0); // Sets the time to 00:00:00.000
    }
  }

  productionLossChange(event) {
    if (event && (event.id === 2 || event.id === 0)) {
      this.ticketBulkCreationForm.affectedkWac = null;
      this.ticketBulkCreationForm.lossType = null;
    }
  }
  refreshList(filterParams: CommonFilter) {
    this.currentPage = 1;
    this.filterModel = filterParams;
    this.getAllSiteDeviceList();
  }

  public onCancel(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  closeModel() {
    this._bsModalRef.hide();
    this.selectedBulkCreateTickets = [];
    this.ticketBulkCreationForm = new TicketBulkCreation();
    this.storageService.clear(this.viewPage);
  }

  proceedNextStep(bulkEditForm) {
    if (this.currentStep === 1) {
      this.onTicketTypeChange();
      this.moveToNextStep();
      this.initFilterDetails(this.selectedTicketType);
      this.getAllSiteDeviceList();
    } else if (this.currentStep === 3) {
      if (bulkEditForm?.form?.valid) {
        this.moveToNextStep();
      } else {
        this.markFormControlsAsTouched(bulkEditForm);
        return;
      }
    } else {
      this.moveToNextStep();
    }
  }

  moveToNextStep() {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  markFormControlsAsTouched(form) {
    if (form && form.form && form.form.controls) {
      Object.keys(form.form.controls).forEach(key => {
        const control = form.form.controls[key];
        control.markAsTouched();
        control.updateValueAndValidity();
      });
    }
  }

  proceedBackStep() {
    if (this.currentStep === 2) {
      this.onTicketTypeChange();
    }
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.closeModel();
    }
  }

  sort(sortBy: string, changeSort: string) {
    if (this.siteDeviceList?.length) {
      if (changeSort === 'asc') {
        changeSort = 'desc';
      } else {
        changeSort = 'asc';
      }
      this.sortOptionList[sortBy] = changeSort;
      this.filterModel.sortBy = sortBy;
      this.filterModel.direction = changeSort;
      this.currentPage = 1;
      this.getAllSiteDeviceList();
    }
  }

  onPageChange(obj) {
    this.currentPage = obj;
  }

  getAllSiteDeviceList() {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    model.itemsCount = 1000;
    if (this.selectedTicketType === 1) {
      this.siteService.getAllSitesByfilter(model).subscribe({
        next: (res: SiteFilterData) => {
          const commonResponse = { totalItems: res.totalSite, itemList: res.sites };
          this.bindSiteDeviceList(commonResponse);
          this.storageService.set(this.viewPage, model);
        },
        error: e => {
          this.loading = false;
        }
      });
    } else {
      this.siteDeviceService.getAllSiteDeviceList(model).subscribe({
        next: (res: SiteDevicesList) => {
          const commonResponse = { totalItems: res.totalSiteDevices, itemList: res.siteDevices };
          this.bindSiteDeviceList(commonResponse);
          this.storageService.set(this.viewPage, model);
        },
        error: e => {
          this.loading = false;
        }
      });
    }
  }

  bindSiteDeviceList(res: { totalItems: number; itemList: SiteDevices[] | Site[] }) {
    this.siteDeviceList =
      res?.itemList?.map(siteDeviceItem => ({
        ...siteDeviceItem,
        show: false,
        isSelected: this.selectedBulkCreateTickets.some(selectedFile => selectedFile.id === siteDeviceItem.id)
      })) || [];

    this.total = this.siteDeviceList.length;
    this.isMasterSel = this.siteDeviceList.every(ticket => ticket.isSelected);
    this.loading = false;
  }

  selectDeselectAll() {
    const selectedIds = new Set(this.selectedBulkCreateTickets.map(file => file.id));

    this.siteDeviceList.forEach(siteDeviceItem => {
      siteDeviceItem.isSelected = this.isMasterSel;

      if (this.isMasterSel) {
        if (!selectedIds.has(siteDeviceItem.id)) {
          this.selectedBulkCreateTickets.push(siteDeviceItem);
        }
      } else {
        this.selectedBulkCreateTickets = this.selectedBulkCreateTickets.filter(file => file.id !== siteDeviceItem.id);
      }
    });
  }

  singleSiteDeviceCheckChanged(siteDeviceItem: SiteDevices | Site) {
    if (siteDeviceItem.isSelected) {
      if (!this.selectedBulkCreateTickets.some(file => file.id === siteDeviceItem.id)) {
        this.selectedBulkCreateTickets.push(siteDeviceItem);
      }
    } else {
      this.selectedBulkCreateTickets = this.selectedBulkCreateTickets.filter(file => file.id !== siteDeviceItem.id);
    }

    this.isMasterSel = this.siteDeviceList.every(siteDeviceItem => siteDeviceItem.isSelected);
  }

  SaveBulkCreateAction() {
    this.currentStep = 6;
    const siteOrDeviceId = this.selectedBulkCreateTickets.map(item => item.id);

    const requestModel = {
      entityIds: siteOrDeviceId ? siteOrDeviceId : [],
      isSiteIds: this.selectedTicketType === 1,
      openedDate: this.datePipe.transform(this.ticketBulkCreationForm.openedDate, AppConstants.fullDateFormat),
      priority: this.ticketBulkCreationForm.priority,
      issueTxt: this.ticketBulkCreationForm.issueTxt,
      productionLoss: this.ticketBulkCreationForm.productionLoss,
      lossType: this.ticketBulkCreationForm.lossType,
      estKWHLoss: this.ticketBulkCreationForm.estKWHLoss,
      isSendEmail: this.ticketBulkCreationForm.isSendEmail
    };
    this.subscription.add(
      this.ticketService.saveBulkTicketCreateAction(requestModel).subscribe({
        next: res => {
          if (res.status === 1 || res.status === -1) {
            this.apiCount = this.selectedBulkCreateTickets.length;
            this.completedCount = res.entryid;
            res.status === 1 ? this.alertService.showSuccessToast(res.message) : this.alertService.showErrorToast(res.message);
            if (res.entityIds.length > 0) {
              this.failedEntityNumber = this.selectedBulkCreateTickets
                .filter(item => res.entityIds.includes(item.id))
                .map(item => (this.selectedTicketType === 1 ? item.siteName : item.site));
            }
          } else {
            this.createSingleTicket(requestModel);
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  createSingleTicket(requestModel) {
    const failedId = [];
    for (const element of this.selectedBulkCreateTickets) {
      const singleModel = {
        ...requestModel,
        entityIds: [element.id]
      };
      this.subscription.add(
        this.ticketService.saveBulkTicketCreateAction(singleModel).subscribe({
          next: res => {
            this.apiCount++;
            if (res.status === 1) {
              this.completedCount++;
            } else {
              if (res.entityIds.length > 0) {
                failedId.push(res.entityIds);
              }
            }
            if (this.completedCount === this.selectedBulkCreateTickets.length) {
              this.alertService.showSuccessToast('Bulk Ticket Create Successfully.');
            }
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    }
    if (failedId.length > 0) {
      this.failedEntityNumber = this.selectedBulkCreateTickets
        .filter(item => failedId.includes(item.id))
        .map(item => (this.selectedTicketType === 1 ? item.siteName : item.site));
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
