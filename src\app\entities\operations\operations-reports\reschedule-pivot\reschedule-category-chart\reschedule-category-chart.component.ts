import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { NbThemeService } from '@nebular/theme';

@Component({
  selector: 'sfl-reschedule-category-chart',
  templateUrl: './reschedule-category-chart.component.html',
  styleUrls: ['./reschedule-category-chart.component.scss']
})
export class RescheduleCategoryChartComponent implements OnInit, OnChanges {
  @Input() byCategoryChartConfig: any = {};
  @Input() chartLabel: string;
  chartsData: any;
  currentTheme = 'dark';
  constructor(private readonly themeService: NbThemeService) {}

  ngOnInit(): void {
    this.themeService.onThemeChange().subscribe(themeName => {
      this.currentTheme = themeName.name;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.byCategoryChartConfig && changes.byCategoryChartConfig.currentValue) {
      const data = {
        title: {
          text: `${this.chartLabel}`,
          left: 'center',
          top: 45,
          textStyle: {
            fontSize: '0.9375rem'
          }
        },
        color: ['#4472C4', '#ED7D31', '#A5A5A5', '#FFC000', '#5B9BD5'],
        grid: {
          y: 100,
          containLabel: true,
          bottom: 100
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: [],
          type: 'scroll',
          bottom: 10
        },
        toolbox: {
          show: true,
          orient: 'horizontal',
          feature: {
            dataZoom: { show: true },
            dataView: {
              show: true,
              readOnly: true,
              optionToContent: opt => {
                let series = opt.series;
                let axisData = opt.xAxis[0]['data'];

                let table = '<table id="site-performance-table" class="table table-bordered"><thead><tr>' + '<th>Month</th>';
                for (let i = 0, l = series.length; i < l; i++) {
                  table += `<th>${series[i].name}</th>`;
                }
                table += '</tr></thead><tbody style="color:#000000">';
                for (let i = 0, l = series[0].data.length; i < l; i++) {
                  table += '<tr>';
                  table += `<td style="color:#000000">${axisData[i]}</td>`;
                  for (let j = 0, m = series.length; j < m; j++) {
                    table += `<td style="text-align:right; color:#000000;">${series[j].data[i].toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}</td>`;
                  }
                  table += '</tr>';
                }
                table += '</thead></table>';
                return table;
              }
            },
            restore: {},
            saveAsImage: {}
          },
          top: 7,
          right: 40
        },
        xAxis: [
          {
            type: 'category',
            axisLabel: {
              interval: 0,
              rotate: this.byCategoryChartConfig['labels'].length > 12 ? 30 : 0
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: `Reschedule Category`,
            nameTextStyle: {
              fontWeight: 'bolder',
              align: 'right',
              padding: [0, 6, 0, 0]
            }
          }
        ],
        series: []
      };
      data.legend.data = this.byCategoryChartConfig['dataOf'];
      data.xAxis[0]['data'] = this.byCategoryChartConfig['labels'];

      this.byCategoryChartConfig['eChartObjects']?.forEach((item, index) => {
        data.series.push({
          ...item,
          type: 'bar',
          emphasis: {
            focus: 'series'
          },
          barGap: 0,
          tooltip: {
            valueFormatter: function (value: any) {
              return (
                (value as number).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' Res..'
              );
            }
          }
        });

        if (this.byCategoryChartConfig['eChartObjects']?.length - 1 === index) this.chartsData = data;
      });
    }
  }
}
