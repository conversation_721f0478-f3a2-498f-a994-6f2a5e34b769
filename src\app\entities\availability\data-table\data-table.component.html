<nb-card class="dataTableSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Data Table</h6>
        <div
          class="ms-auto d-flex align-items-center"
          *ngIf="generated && dataTableInformationList?.devices?.length && dataTableInformationList?.devicesData?.length"
        >
          <button
            *ngIf="dataTableInformationList"
            class="linear-mode-button"
            nbButton
            status="primary"
            size="small"
            type="button"
            (click)="exportData()"
            [disabled]="loading"
          >
            <span class="d-none d-lg-inline-block">Export</span>
            <i class="d-inline-block d-lg-none fa fa-file-export"></i>
          </button>
          <div ngFileDragDrop (fileDropped)="importData($event)">
            <input type="file" #file class="d-none" accept=".xlsx" (change)="importData($event.target.files)" />
            <button
              class="linear-mode-button ms-2"
              nbButton
              status="primary"
              size="small"
              type="button"
              (click)="selectFile()"
              [disabled]="loading"
              *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
            >
              <span class="d-none d-lg-inline-block">Import</span>
              <i class="d-inline-block d-lg-none fa fa-file-import"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 performanceFilter">
        <div class="form-control-group mb-3">
          <div class="row align-items-center">
            <div class="col-12 col-md-4 col-lg-2 pe-0">
              <label class="label" for="customer">Customer</label>
              <ng-select
                id="availability-customer-drop-down"
                name="customer"
                [items]="customerList"
                (change)="onCustomerSelect()"
                (clear)="onCustomerDeSelect()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.customerId"
                notFoundText="No Customer Found"
                placeholder="Select Customer"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 pe-0">
              <label class="label" for="portfolio">Portfolio</label>
              <ng-select
                id="availability-portfolio-drop-down"
                name="portfolio"
                [items]="portfolioList"
                (change)="onPortfolioSelect()"
                (clear)="onPortfolioDeSelect()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.portfolioId"
                notFoundText="No Portfolio Found"
                placeholder="Select Portfolio"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 pe-0">
              <label class="label" for="site">Site</label>
              <ng-select
                id="availability-site-drop-down"
                name="site"
                [items]="siteList"
                (change)="onSiteSelectOrDeSelect()"
                (clear)="onSiteSelectOrDeSelect()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.siteId"
                notFoundText="No Site Found"
                placeholder="Select Site"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-md-4 col-lg-2 pe-0">
              <label class="label" for="year">Date</label>
              <div class="d-flex align-items-center">
                <button
                  id="availability-prev-date-btn"
                  class="linear-mode-button nextPreviousIcon previousRadius"
                  nbButton
                  status="primary"
                  type="button"
                  (click)="previousDate()"
                  nbTooltip="Previous Day"
                  nbTooltipPlacement="top"
                  *ngIf="generated"
                  nbTooltipStatus="primary"
                >
                  <nb-icon icon="arrowhead-left-outline"></nb-icon>
                </button>
                <input
                  class="form-control search-textbox"
                  [ngClass]="{ dateBox: generated }"
                  [nbDatepicker]="availabilityDate"
                  [(ngModel)]="filterModel.datePerformed"
                  name="availabilityDate"
                  placeholder="Select Date"
                  id="input-availabilityDate"
                  readonly
                  autocomplete="off"
                />
                <nb-datepicker #availabilityDate [max]="max"></nb-datepicker>
                <button
                  id="availability-next-date-btn"
                  class="linear-mode-button nextPreviousIcon nextRadius"
                  nbButton
                  status="primary"
                  type="button"
                  (click)="nextDate()"
                  nbTooltip="Next Day"
                  nbTooltipPlacement="top"
                  *ngIf="generated && filterModel.datePerformed < max"
                  nbTooltipStatus="primary"
                >
                  <nb-icon icon="arrowhead-right-outline"></nb-icon>
                </button>
              </div>
            </div>
            <div class="col-12 col-md-8 col-lg-4 mt-4 d-flex">
              <button
                id="availability-view-data-btn"
                class="linear-mode-button"
                nbButton
                status="primary"
                size="small"
                type="button"
                [disabled]="!filterModel.customerId || !filterModel.portfolioId || !filterModel.siteId || !filterModel.datePerformed"
                (click)="generateData()"
              >
                View Data
              </button>
              <button
                id="availability-view-exclusion-btn"
                class="linear-mode-button ms-2"
                nbButton
                status="primary"
                size="small"
                type="button"
                *ngIf="generated"
                (click)="showExclusion()"
              >
                View Exclusions
              </button>
              <button
                id="availability-clear-btn"
                class="linear-mode-button ms-auto"
                nbButton
                status="primary"
                size="small"
                type="button"
                [disabled]="loading"
                (click)="clearFilter()"
              >
                clear
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12" *ngIf="generated && !checkAuthorisationsFn([roleType.CUSTOMER])">
        <nb-tabset fullWidth (changeTab)="changeTab($event)">
          <nb-tab tabTitle="Data Table Information" [nbSpinner]="loadDataTable">
            <sfl-data-table-information
              [informationList]="dataTableInformationList"
              [siteId]="siteId"
              [dataTableFilter]="filterModel"
              (fetchingStart)="reCheckFetchStatus($event)"
              (deviceFetchingStart)="reCheckDeviceFetchStatus($event)"
              (loadingStart)="loadDataTable = $event"
              (refreshList)="getAllPerformanceDetails()"
            ></sfl-data-table-information>
          </nb-tab>
          <nb-tab
            tabTitle="Edit History"
            [nbSpinner]="loadHistory"
            [disabled]="!dataTableInformationList?.devices?.length && !dataTableInformationList?.devicesData?.length"
          >
            <sfl-edit-history [editHistoryData]="editHistoryData" (pageChange)="editHistoryPageChange($event)"></sfl-edit-history>
          </nb-tab>
          <nb-tab
            tabTitle="Re-Fetch Status"
            [nbSpinner]="loadREFetchHistory"
            [disabled]="!dataTableInformationList?.devices?.length && !dataTableInformationList?.devicesData?.length"
          >
            <sfl-re-fetch-status [reFetchData]="reFetchData" (pageChanges)="refetchStatusEmit($event)"></sfl-re-fetch-status>
          </nb-tab>
        </nb-tabset>
      </div>
      <div class="col-12" *ngIf="generated && checkAuthorisationsFn([roleType.CUSTOMER])">
        <sfl-data-table-information
          [informationList]="dataTableInformationList"
          [siteId]="siteId"
          [dataTableFilter]="filterModel"
          (refreshList)="getAllPerformanceDetails()"
          (fetchingStart)="reCheckFetchStatus($event)"
          (deviceFetchingStart)="reCheckDeviceFetchStatus($event)"
        ></sfl-data-table-information>
      </div>
    </div>
  </nb-card-body>
</nb-card>
