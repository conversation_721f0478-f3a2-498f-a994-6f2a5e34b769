import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { SharedModule } from '../../../@shared/shared.module';
import { OperationServicesRoutingModule } from './operation-services-routing.module';
import { OperationServicesComponent } from './operation-services.component';
import { AddEditOperationServicesComponent } from './add-edit-operation-services/add-edit-operation-services.component';

@NgModule({
  declarations: [OperationServicesComponent, AddEditOperationServicesComponent],
  imports: [CommonModule, OperationServicesRoutingModule, SharedModule]
})
export class OperationServicesModule {}
