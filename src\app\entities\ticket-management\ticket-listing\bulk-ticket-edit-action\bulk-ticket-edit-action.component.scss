.error-message-section {
  height: 20px;
}

.modal-full-view-dialog {
  margin: 0px !important;
  min-width: 100vw;
  min-height: 100vh;
  max-width: 100% !important;
  .modal-content {
    padding: 10px;
    min-height: 100vh;
    min-width: 100vw;
  }
}

nb-card-body {
  overflow: unset !important;
}
::ng-deep .bulk-edit-form {
  .ng-dropdown-panel {
    width: 100% !important;
  }
}

.close {
  border: none;
  background-color: transparent;
  color: #fff;
}

nb-card-header.border-bottom-0 {
  border-bottom: 0px !important;
  padding-bottom: 0px !important;
}

.mobile-view-buttons {
  display: none;
}

.tablet-view-buttons {
  display: inline-flex;
}

::ng-deep .ticketSpinner nb-spinner {
  align-items: flex-start !important;
  padding: 8% !important;
}

::ng-deep nb-card-header {
  padding: 0.75rem 1.25rem !important;
}

.pointerTicketNumberLink {
  color: rgb(89, 139, 255) !important;
  cursor: pointer !important;
  text-decoration-line: underline;
}

.mw-150 {
  min-width: 150px;
}

.truckroll-ul {
  list-style: none;
  padding: 0px;
  .truckroll-li {
    border-bottom: 1px solid #53596a;
    padding: 0.75rem;
    span {
      cursor: pointer;
      text-decoration: underline;
    }
  }
  .truckroll-li:last-child {
    border-bottom: none;
  }
}

.scale-2 {
  scale: 2.4;
}
.multi-column-list {
  columns: 10;
  column-gap: 20px;

  li {
    line-height: 1.5rem;
  }
}
