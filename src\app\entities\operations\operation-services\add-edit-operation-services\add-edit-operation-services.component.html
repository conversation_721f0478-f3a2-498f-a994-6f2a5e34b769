<nb-card class="spinnerDisplay" [nbSpinner]="isApiCallInProgress" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="d-flex align-items-center">
      <h6 class="m-0">{{ isEdit ? 'Edit Service' : 'Add Service' }}</h6>
      <div class="d-flex ms-auto">
        <button nbButton status="basic" type="button" (click)="goBack()" size="small" class="float-end">
          <span class="d-none d-lg-inline-block">Back</span>
          <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
        </button>
        <button
          nbButton
          (click)="addUpdateService(addUpdateForm)"
          status="primary"
          size="small"
          type="button"
          id="siteSubmit"
          class="float-end ms-2"
        >
          <span class="d-none d-lg-inline-block">Save</span>
          <i class="d-inline-block d-lg-none fa-solid fa-save"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <form
      name="addUpdateForm"
      #addUpdateForm="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="addUpdateService(addUpdateForm)"
    >
      <div class="form-group row">
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="classList"> Class <span class="ms-1 text-danger">*</span></label>
          <ng-select
            name="classList"
            [items]="classDropDownList"
            bindLabel="name"
            bindValue="id"
            #classList="ngModel"
            [(ngModel)]="serviceModel.class"
            notFoundText="No Class Found"
            placeholder="Select Class"
            appendTo="body"
            [clearable]="true"
            [closeOnSelect]="true"
            required
          >
          </ng-select>
          <sfl-error-msg [control]="classList" [isFormSubmitted]="addUpdateForm?.submitted" fieldName="Class"></sfl-error-msg>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="category"> Category <span class="ms-1 text-danger">*</span></label>
          <ng-select
            name="category"
            [items]="categoryDropDownList"
            bindLabel="name"
            bindValue="id"
            #category="ngModel"
            [(ngModel)]="serviceModel.category"
            notFoundText="No Category Found"
            placeholder="Select Category"
            appendTo="body"
            [clearable]="true"
            [closeOnSelect]="true"
            required
          >
          </ng-select>
          <sfl-error-msg [control]="category" [isFormSubmitted]="addUpdateForm?.submitted" fieldName="Category"></sfl-error-msg>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="rateType">Rate Type<span class="ms-1 text-danger">*</span></label>
          <ng-select
            name="rateType"
            [items]="rateTypeDropDownList"
            bindLabel="name"
            bindValue="id"
            #rateType="ngModel"
            [(ngModel)]="serviceModel.rateTypeId"
            notFoundText="No Rate Type Found"
            placeholder="Select Rate Type"
            appendTo="body"
            [clearable]="true"
            [closeOnSelect]="true"
            required
          >
          </ng-select>
          <sfl-error-msg [control]="rateType" [isFormSubmitted]="addUpdateForm?.submitted" fieldName="Rate Type"></sfl-error-msg>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="service">Service<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            name="serviceText"
            #serviceText="ngModel"
            class="form-control"
            fullWidth
            placeholder="Service"
            [(ngModel)]="serviceModel.service"
            noLeadingSpace
            required
          />
          <sfl-error-msg [control]="serviceText" [isFormSubmitted]="addUpdateForm?.submitted" fieldName="Service"></sfl-error-msg>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="mfgSpecific"> Mfg Specific</label>
          <div class="d-flex">
            <nb-toggle status="primary" [(checked)]="serviceModel.mfgSpecific"></nb-toggle>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="mfgCertTraining"> Mfg Cert/Training</label>
          <div class="d-flex">
            <nb-toggle status="primary" [(checked)]="serviceModel.mfgCertTraining"></nb-toggle>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="prevailingWage"> Prevailing Wage</label>
          <div class="d-flex">
            <nb-toggle status="primary" [(checked)]="serviceModel.prevailingWage"></nb-toggle>
          </div>
        </div>
      </div>
    </form>
    <div class="row mt-3" *ngIf="isEdit">
      <div class="col-12">
        <nb-tabset fullWidthv class="row">
          <nb-tab tabTitle="History" [nbSpinner]="historyLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
            <div class="row">
              <div class="col-12 history-section">
                <nb-accordion class="mb-2" *ngFor="let item of serviceHistory">
                  <nb-accordion-item
                    (collapsedChange)="accordionChange($event, 'deviceInformation')"
                    [expanded]="historyAccordion"
                    class="border-bottom"
                  >
                    <nb-accordion-item-header class="accordion_head">
                      <div class="d-flex align-items-center">
                        <strong>{{ item?.userName }}</strong>
                        <div class="label mb-0 ms-2">{{ item?.action }}</div>
                        <span class="label mb-0 ms-2"> - </span>
                        <span class="label mb-0 ms-2">{{ item?.logDate | date : dateTimeFormat }}</span>
                      </div>
                    </nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div class="mt-2" *ngIf="item?.auditLogDetails">
                        <div class="row">
                          <div class="col-2"><label class="label mb-0">Field</label></div>
                          <div class="col-5 text-center"><label class="label mb-0">Original Value</label></div>
                          <div class="col-5 text-center"><label class="label mb-0">New Value</label></div>
                        </div>
                        <div class="mt-2" *ngFor="let actionSummary of item?.auditLogDetails">
                          <div class="row">
                            <div class="col-2">{{ actionSummary?.fieldName }}</div>
                            <div class="col-5 text-center">{{ actionSummary?.oldValue || '-' }}</div>
                            <div class="col-5 text-center">{{ actionSummary?.newValue || '-' }}</div>
                          </div>
                        </div>
                      </div>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>
            </div>
            <div class="row col-12" *ngIf="!serviceHistory.length">
              <label>No actions found</label>
            </div>
          </nb-tab>
        </nb-tabset>
      </div>
    </div>
  </nb-card-body>
</nb-card>
