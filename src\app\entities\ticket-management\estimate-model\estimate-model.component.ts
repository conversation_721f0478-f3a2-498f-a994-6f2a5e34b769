import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { TreeNode } from 'primeng/api';
import { Subject, Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AppConstants } from '../../../@shared/constants';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { TicketRMAs, statusDropDownList } from '../ticket.model';
import { TicketService } from '../ticket.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';
interface CustomTreeNode extends TreeNode {
  id: number;
}
@Component({
  selector: 'sfl-estimate-model',
  templateUrl: './estimate-model.component.html',
  styleUrls: ['./estimate-model.component.scss'],
  encapsulation: ViewEncapsulation?.None
})
export class EstimateModelComponent implements OnInit {
  subscription: Subscription = new Subscription();
  public onClose: Subject<boolean>;
  addUpdateEstimateForm: FormGroup;
  @Input() deviceItem;
  @Input() ticketNumber;
  @Input() ticketId;
  @Input() siteId;
  @Input() ticketdetail;
  @Input() estDetails;
  @Input() lineIndex;
  @Input() mode;
  @Input() userRole;
  @Input() isTicketCreate;
  deviceSerialImageFiles: any;
  returnTrackingImageFiles: any;
  dependentFields = [
    'isReturnRequired',
    'startDate',
    'completeDate',
    'deviceMFG',
    'rmaNumber',
    'deviceSerialNumber',
    'returnTrackingNumber'
  ];
  returnTrackingFiles: any = [];
  deviceSerialFiles: any;
  needToUpdateRmaObj: TicketRMAs;
  isRmaCompleteToggleDisabled: boolean = true;
  modalRef: BsModalRef;
  loading = false;
  maskDecimal = 'separator.2';
  dateFormat = AppConstants.fullDateFormat;
  statusDropDownList = [
    {
      id: 1,
      name: 'Pending Approval',
      disabled: false
    },
    {
      id: 2,
      name: 'Declined',
      disabled: false
    },
    {
      id: 3,
      name: 'Approved',
      disabled: false
    }
  ];
  allStatusList = statusDropDownList;
  markForAttachmentUpload = false;
  deleteAttachmentObject: any = {
    itemId: 0,
    index: 0,
    markForAttachmentRemove: false
  };
  estimateOriginalStatus = false;

  constructor(
    private fb: FormBuilder,
    public _bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly commonService: CommonService,
    private readonly ticketService: TicketService
  ) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
    this.ticketdetail;
    this.initFormGroup();
    if (this.mode !== 'create') {
      this.editEST();
    } else {
      this.addUpdateEstimateForm.controls['ticketId'].setValue(this.ticketId);
    }

    this.addUpdateEstimateForm.get('estimateStatus').valueChanges.subscribe(() => {
      this.isStatusFiledDisabled();
    });
  }

  resetStatusDropDown() {
    const currentStatus = this.estDetails.estimateStatus;
    if (currentStatus === 4) {
      this.statusDropDownList = this.allStatusList.filter(item => item.id !== 2 && item.id !== 3);
    } else if (currentStatus === 2) {
      this.statusDropDownList = this.allStatusList.filter(item => item.id === 1 || item.id === 2);
    } else if (currentStatus === 5 || currentStatus === 6) {
      this.statusDropDownList = this.allStatusList.filter(item => item.id !== 1 && item.id !== 2 && item.id !== 3);
    }
  }

  editEST() {
    this.resetStatusDropDown();
    this.addUpdateEstimateForm.controls['ticketId'].setValue(this.estDetails.ticketId);
    this.addUpdateEstimateForm.controls['ticketEstId'].setValue(this.estDetails.ticketEstId);
    this.addUpdateEstimateForm.controls['estNumber'].setValue(this.estDetails.estNumber);
    this.addUpdateEstimateForm.controls['additionalHours'].setValue(this.estDetails.additionalHours);
    this.addUpdateEstimateForm.controls['estimateTotal'].setValue(this.estDetails.estimateTotal);
    this.addUpdateEstimateForm.controls['estimateTotal'].setValue(this.estDetails.estimateTotal);
    this.addUpdateEstimateForm.controls['randomGUID'].setValue(this.estDetails.randomGUID);
    this.addUpdateEstimateForm.controls['estimateStatus'].setValue(this.estDetails.estimateStatus ? this.estDetails.estimateStatus : 1);
    this.addUpdateEstimateForm.controls['customerPO'].setValue(this.estDetails.customerPO);
    this.addUpdateEstimateForm.controls['approvedBy'].setValue(this.estDetails.approvedBy);
    this.addUpdateEstimateForm.controls['approvedByName'].setValue(this.estDetails.approvedByName);
    this.addUpdateEstimateForm.controls['approvedOn'].setValue(this.estDetails.approvedOn);
    this.estimateOriginalStatus = JSON.parse(JSON.stringify(this.estDetails.estimateStatus ? this.estDetails.estimateStatus : 1));
    if (this.estDetails.ticketEstimateAttachments) {
      if (this.estDetails.ticketEstimateAttachments.length > 0) {
        this.returnTrackingFiles.push(this.estDetails.ticketEstimateAttachments[0]);
      }
    }
  }

  initFormGroup() {
    let GUID = this.generateRandomGuid();
    this.addUpdateEstimateForm = this.fb.group({
      ticketId: [0],
      ticketEstId: [0],
      estNumber: ['', Validators.required],
      additionalHours: ['', Validators.max(1000)],
      estimateTotal: ['', Validators.compose([Validators.required, Validators.max(1000000000000)])],
      isDeleted: [false],
      ticketEstimateAttachments: [],
      randomGUID: [GUID],
      lineIndex: [this.lineIndex],
      estimateStatus: [1],
      customerPO: [null],
      approvedBy: [null],
      approvedByName: [null],
      approvedOn: [null]
    });
  }
  onConfirm(): void {
    if (this.addUpdateEstimateForm.invalid) {
      this.alertService.showErrorToast('Please fill the mandatory fields to submit the form.');
      return;
    }
    this.loading = true;
    // check if estimate status got degraded from billing to pending billing
    if (
      this.mode === 'edit' &&
      (this.addUpdateEstimateForm.value.estimateStatus === 1 || this.addUpdateEstimateForm.value.estimateStatus === 2)
    ) {
      this.addUpdateEstimateForm.controls['customerPO'].setValue(null);
    }
    const estimateModalObj = {
      ...this.addUpdateEstimateForm.value,
      ticketEstimateAttachments: this.returnTrackingFiles,
      additionalHours:
        this.addUpdateEstimateForm.value.additionalHours === '' ? 0 : Number(this.addUpdateEstimateForm.value.additionalHours),
      estimateTotal: Number(this.addUpdateEstimateForm.value.estimateTotal)
    };
    if (this.isTicketCreate) {
      this.onClose.next(estimateModalObj);
      this.loading = false;
      this._bsModalRef.hide();
    } else {
      this.addUpdateTicketEstimate(estimateModalObj);
    }
  }

  addUpdateTicketEstimate(estimateModalObj) {
    const modal = estimateModalObj;
    this.subscription.add(
      this.ticketService.addUpdateTicketEstimate(modal).subscribe({
        next: async (res: any) => {
          const ticketEstimateRes = {
            ...res.ticketEstimates[0],
            lineIndex: this.lineIndex,
            ticketEstimateAttachments: res.ticketEstimates[0].ticketEstimateAttachments.length
              ? res.ticketEstimates[0].ticketEstimateAttachments
              : this.returnTrackingFiles.length
              ? this.returnTrackingFiles
              : []
          };
          if (this.deleteAttachmentObject.markForAttachmentRemove) {
            await this.deleteESTImage(this.deleteAttachmentObject.itemId, this.deleteAttachmentObject.index);
          }
          if (this.markForAttachmentUpload || (this.mode === 'create' && this.returnTrackingFiles.length)) {
            this.estDetails.ticketEstId = res.ticketEstimates[0].ticketEstId;
            await this.estUploadFile(this.returnTrackingFiles[0].file, false);
          }
          this.onClose.next(ticketEstimateRes);
          this.alertService.showSuccessToast(
            this.mode === 'create' ? `Ticket estimate created successfully` : 'Ticket estimate updated successfully'
          );
          this.loading = false;
          this._bsModalRef.hide();
        },
        error: e => {
          this.loading = false;
        }
      })
    );
    err => {
      this.loading = false;
    };
  }

  onCancel(): void {
    // this.onClose.next(this.estDetails ? this.addUpdateEstimateForm.value : false);
    this._bsModalRef.hide();
  }

  getUploadedFiles(files, isSerialFiles) {
    if (this.isEditableByQEUsers()) {
      return;
    }
    this.markForAttachmentUpload = false;
    this.loading = true;
    const file = files.item(0);
    if (file && file.size <= 10485760) {
      if (this.estDetails.ticketEstId) {
        this.markForAttachmentUpload = true;
        this.loading = false;
        this.returnTrackingFiles.push(this.prepareFileObj(file, '', 2));
      } else {
        this.returnTrackingFiles.push(this.prepareFileObj(file, '', 2));
        this.loading = false;
      }
    } else {
      this.alertService.showErrorToast('File size should not exceed 10MB');
      this.loading = false;
      return;
    }
  }
  prepareFileObj(file, dataUrl, docType) {
    return {
      id: 0,
      ticketEstimateId: 0,
      documentUrl: '',
      isDeleted: false,
      size: file.size,
      fileName: file.name,
      fileExtension: file.type.split('/')[1],
      fileType: file.type.split('/')[0] && file.type.split('/')[0] === 'application' ? 'document' : file.type.split('/')[0],
      ticketId: this.ticketId,
      file: file
    };
  }
  deleteUploadedAttachment(index: number, item) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          this.returnTrackingFiles.splice(index, 1);
          if (this.markForAttachmentUpload) this.markForAttachmentUpload = false;
          if (this.mode === 'edit' && item.id !== 0) {
            this.deleteAttachmentObject.itemId = item.id;
            this.deleteAttachmentObject.index = index;
            this.deleteAttachmentObject.markForAttachmentRemove = true;
          }
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  estUploadFile(file, showSuccessMessage = true): Promise<void> {
    return new Promise((resolve, reject) => {
      this.loading = true;
      const formData: FormData = new FormData();
      formData.append('file', file as File);
      formData.append(
        'fileType',
        file.type.split('/')[0] && file.type.split('/')[0] === 'application' ? 'document' : file.type.split('/')[0]
      );
      formData.append('fileExtension', file.type.split('/')[1]);
      formData.append('id', '0');
      formData.append('ticketId', `${this.ticketId}`);
      formData.append('documentUrl', '');
      formData.append('isDeleted', 'false');
      formData.append('ticketEstId', this.estDetails.ticketEstId);
      this.ticketService.uploadESTFiles(this.ticketId, formData).subscribe({
        next: res => {
          if (showSuccessMessage) this.alertService.showSuccessToast(`File uploaded.`);
          this.returnTrackingFiles.push({
            id: res.id,
            ticketEstimateId: res.ticketEstimateId,
            documentUrl: '',
            isDeleted: false,
            size: file.size,
            fileName: file.name,
            ticketEstId: res.ticketEstId,
            fileExtension: file.type.split('/')[1],
            fileType: file.type.split('/')[0] && file.type.split('/')[0] === 'application' ? 'document' : file.type.split('/')[0],
            ticketId: this.ticketId
          });
          this.loading = false;
          resolve();
        },
        error: e => {
          this.alertService.showWarningToast('Fail to upload file.');
          this.loading = false;
          reject();
        }
      });
    });
  }
  deleteESTImage(id: number, index): Promise<void> {
    return new Promise(resolve => {
      this.loading = true;
      this.subscription.add(
        this.ticketService.deleteESTFiles(id).subscribe({
          next: () => {
            this.loading = false;
            resolve();
          },
          error: e => {
            this.loading = false;
          }
        })
      );
      err => {
        this.loading = false;
      };
    });
  }
  downloadAttachment(url, fileName, rmaDocId) {
    if (!rmaDocId) {
      fetch(url)
        .then(response => response.blob())
        .then(blob => {
          const link = this.commonService.createObject(blob, '');
          link.download = fileName;
          link.click();
        })
        .catch(error => console.error('Error downloading image:', error));
    } else {
      this.loading = true;
      this.ticketService.downloadRmaAttachment(rmaDocId).subscribe({
        next: data => {
          if (data) {
            const link = this.commonService.createObject(data, data.type);
            link.download = fileName;
            link.click();
            this.loading = false;
          } else {
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      });
    }
  }
  generateRandomGuid() {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const guidLength = 12;
    let guid = '';
    for (let i = 0; i < guidLength; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      guid += characters.charAt(randomIndex);
    }
    return guid;
  }

  getStatusLabel(statusId: number) {
    const statusObj = this.statusDropDownList.find(item => item.id === statusId);
    return statusObj ? statusObj.name : '';
  }

  isStatusFiledDisabled() {
    const estimateStatus = this.addUpdateEstimateForm.value.estimateStatus;
    const isEstNumberPopulated = this.addUpdateEstimateForm.value.estNumber ? true : false;

    switch (estimateStatus) {
      case 1: // Pending Approval
        return; // this.addUpdateEstimateForm.controls['customerPO'].setValue(null);
      case 2: // Declined
        return; // this.addUpdateEstimateForm.controls['customerPO'].setValue(null);
      case 3: // Approved
        return !(this.isUserCustomerOrCamPlus(this.userRole) && isEstNumberPopulated);
      case 4: // Pending Billing
        return !(this.isPlPlusUser(this.userRole) && isEstNumberPopulated);
      case 5: // Billing
        this.removeBillingFromStatus();
        return !(this.isAdminOrCamPlus(this.userRole) && isEstNumberPopulated);
      case 6: // Billed
        return this.estimateOriginalStatus === estimateStatus && true; // Assuming Billed status should always be disabled
      default:
        return true;
    }
  }

  removeBillingFromStatus() {
    if (!this.isAdminOrCamPlus(this.userRole)) {
      this.statusDropDownList = this.statusDropDownList.filter(element => element.id !== 4 && element.id !== 5);
    }
  }

  isCustomerOrAnalystUser(userRole: string): boolean {
    return checkAuthorisations([ROLE_TYPE.ANALYST, ROLE_TYPE.CUSTOMER]);
  }

  isCustomerAnalystOrPl(userRole: string): boolean {
    return checkAuthorisations([ROLE_TYPE.CUSTOMER, ROLE_TYPE.ANALYST, ROLE_TYPE.PORTFOLIOMANAGER]);
  }

  isPlPlusUser(userRole: string): boolean {
    return checkAuthorisations([ROLE_TYPE.PORTFOLIOMANAGER, ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.SUPPORT]);
  }

  isUserCustomerOrCamPlus(userRole: string): boolean {
    return checkAuthorisations([ROLE_TYPE.CUSTOMER, ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.SUPPORT]);
  }

  isAdminOrCamPlus(userRole: string): boolean {
    return checkAuthorisations([ROLE_TYPE.ADMIN, ROLE_TYPE.MANAGER, ROLE_TYPE.SUPPORT]);
  }

  isEditableByQEUsers(): string | null {
    const estimateStatus = this.addUpdateEstimateForm.value.estimateStatus;
    return (([2, 3, 4, 5, 6].includes(this.estDetails.estimateStatus) || [2, 3, 4, 5, 6].includes(estimateStatus)) && this.isQEUser()) ||
      ([1, 2, 3, 4, 5, 6].includes(estimateStatus) && checkAuthorisations([ROLE_TYPE.CUSTOMER]))
      ? 'disabled'
      : null;
  }

  isQEUser(): boolean {
    return !checkAuthorisations([ROLE_TYPE.CUSTOMER]);
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
