import { Location } from '@angular/common';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AppConstants } from '../../../../@shared/constants/app.constant';
import { AlertService } from '../../../../@shared/services';
import { OperationServicesList, ServiceAuditHistory, ServicesRateDropDownList } from '../operation-services.modal';
import { OperationServicesService } from '../operation-services.service';

@Component({
  selector: 'sfl-add-edit-operation-services',
  templateUrl: './add-edit-operation-services.component.html',
  styleUrls: ['./add-edit-operation-services.component.scss'],
  encapsulation: ViewEncapsulation?.None
})
export class AddEditOperationServicesComponent implements OnInit {
  isApiCallInProgress = false;
  historyLoading = false;
  isEdit = false;
  isCreate = false;
  isDisableForm = false;
  serviceHistory: ServiceAuditHistory[] = [];
  subscription: Subscription = new Subscription();
  serviceModel: OperationServicesList = new OperationServicesList();
  dateTimeFormat = AppConstants.dateTimeFormat;
  classDropDownList = [
    { id: 1, name: 'CM' },
    { id: 2, name: 'Commissioning' },
    { id: 3, name: 'PM' }
  ];
  rateTypeDropDownList: ServicesRateDropDownList[] = [];
  categoryDropDownList = [
    { id: 1, name: 'Aerial' },
    { id: 2, name: 'Array' },
    { id: 3, name: 'BESS' },
    { id: 4, name: 'DAS' },
    { id: 5, name: 'Electrical' },
    { id: 6, name: 'Electrical/Mechanical' },
    { id: 7, name: 'Engineering' },
    { id: 8, name: 'General' },
    { id: 9, name: 'Inverter' },
    { id: 10, name: 'Mechanical' },
    { id: 11, name: 'Medium Voltage' }
  ];
  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly alertService: AlertService,
    private readonly operationServices: OperationServicesService,
    private readonly _location: Location
  ) {}

  ngOnInit(): void {
    this.getServiceRateTypeList();
    this.route.params.subscribe(params => {
      if (params && params.id) {
        this.serviceModel.id = params.id;
        this.isEdit = true;
        this.isCreate = false;
        this.getServiceDetail();
        this.getSiteHistoryData();
      } else {
        this.isEdit = false;
        this.isCreate = true;
      }
    });
  }

  getServiceDetail() {
    this.isApiCallInProgress = true;
    this.subscription.add(
      this.operationServices.getServiceDetail(this.serviceModel.id).subscribe({
        next: (res: OperationServicesList) => {
          this.serviceModel = res;
          this.isApiCallInProgress = false;
        },
        error: e => {
          this.isApiCallInProgress = false;
        }
      })
    );
  }

  getServiceRateTypeList() {
    this.isApiCallInProgress = true;
    this.subscription.add(
      this.operationServices.getServiceDropDownList().subscribe({
        next: (res: ServicesRateDropDownList[]) => {
          this.rateTypeDropDownList = res;
          this.isApiCallInProgress = false;
        },
        error: e => {
          this.isApiCallInProgress = false;
        }
      })
    );
  }

  goBack() {
    this._location.back();
  }

  addUpdateService(addUpdateForm) {
    if (addUpdateForm.invalid) {
      this.alertService.showErrorToast('Please fill the mandatory fields to submit the form.');
      return;
    }
    this.isApiCallInProgress = true;
    this.subscription.add(
      this.operationServices.createUpdateServices(this.serviceModel).subscribe({
        next: () => {
          this.isApiCallInProgress = false;
          this.alertService.showSuccessToast(`${this.serviceModel.id ? 'Service Updated Successfully.' : 'Service Created Successfully.'}`);
          this.router.navigate(['entities/operations/services']);
        },
        error: e => {
          this.isApiCallInProgress = false;
        }
      })
    );
  }

  getSiteHistoryData() {
    this.historyLoading = true;
    this.subscription.add(
      this.operationServices.getServiceHistoryData(Number(this.serviceModel.id)).subscribe({
        next: (res: ServiceAuditHistory[]) => {
          if (res) {
            this.serviceHistory = res;
            this.historyLoading = false;
          }
        },
        error: e => {
          this.historyLoading = false;
        }
      })
    );
  }
}
