<nb-card class="siteSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>{{ selectedDashboard?.menuTitle }}</h6>
        <div class="ms-auto">
          <button class="linear-mode-button" nbButton status="primary" size="small" type="button" (click)="addUpdateDashboard('Update')">
            <span class="d-none d-lg-inline-block">Edit</span>
            <i class="d-inline-block d-lg-none fa fa-pen"></i>
          </button>
          <button
            class="linear-mode-button ms-2"
            nbButton
            status="danger"
            size="small"
            type="button"
            (click)="onDelete(selectedDashboard?.id)"
          >
            <span class="d-none d-lg-inline-block">Delete</span>
            <i class="d-inline-block d-lg-none fa fa-trash"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12" [innerHTML]="selectedDashboard?.content"></div>
    </div>
  </nb-card-body>
</nb-card>
