export enum ROLE_TYPE {
  PORTFOLIOMANAGER = 1,
  <PERSON>IE<PERSON><PERSON>CH = 2,
  ADMIN = 3,
  CUSTOMER = 4,
  CONTRACTOR = 5,
  ANALYST = 6,
  MANAGER = 7,
  SUPPORT = 8
}

export const ROLE_NAME = {
  [ROLE_TYPE.PORTFOLIOMANAGER]: 'PORTFOLIOMANAGER',
  [ROLE_TYPE.FIELDTECH]: 'FIELDTECH',
  [ROLE_TYPE.ADMIN]: 'ADMIN',
  [ROLE_TYPE.CUSTOMER]: 'CUSTOMER',
  [ROLE_TYPE.CONTRACTOR]: 'CONTRACTOR',
  [ROLE_TYPE.ANALYST]: 'ANALYST',
  [ROLE_TYPE.MANAGER]: 'MANAGER',
  [ROLE_TYPE.SUPPORT]: 'SUPPORT'
};

export const ROLE_DISPLAY_NAME = {
  [ROLE_TYPE.PORTFOLIOMANAGER]: 'Portfolio Lead',
  [ROLE_TYPE.FIELDTECH]: 'Field Tech',
  [ROLE_TYPE.ADMIN]: 'Admin',
  [ROLE_TYPE.CUSTOMER]: 'Customer',
  [ROLE_TYPE.CONTRACTOR]: 'Contractor',
  [ROLE_TYPE.ANALYST]: 'Analyst',
  [ROLE_TYPE.MANAGER]: 'Manager',
  [ROLE_TYPE.SUPPORT]: 'Support'
};

export const AUTHORITY_ROLE_STRING = {
  [ROLE_TYPE.PORTFOLIOMANAGER]: 'portfolioManager',
  [ROLE_TYPE.FIELDTECH]: 'fieldTech',
  [ROLE_TYPE.ADMIN]: 'admin',
  [ROLE_TYPE.CUSTOMER]: 'customer',
  [ROLE_TYPE.CONTRACTOR]: 'contractor',
  [ROLE_TYPE.ANALYST]: 'analyst',
  [ROLE_TYPE.MANAGER]: 'manager',
  [ROLE_TYPE.SUPPORT]: 'support'
};
