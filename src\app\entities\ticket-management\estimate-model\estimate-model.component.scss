.modal-dialog-right {
  position: fixed;
  margin: auto;
  width: 600px;
  right: 0px;
  height: 100%;
}

.modal-content {
  height: 100%;
}

.add-device-modal-body {
  max-height: calc(100vh - 150px);
  overflow-y: auto;
  overflow-x: hidden;
}

.add-device-modal-body::-webkit-scrollbar {
  background: #101426;
  cursor: pointer;
  border-radius: 0.15625rem;
  width: 0.3125rem;
  height: 0.3125rem;
}

.cdk-global-overlay-wrapper,
.cdk-overlay-container {
  z-index: 99999 !important;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.dropZone {
  height: 100% !important;
  padding: 0.5rem;
  em {
    font-size: 20px !important;
  }
  h5 {
    font-size: 16px !important;
    margin-bottom: 0px !important;
  }
  label {
    font-size: 13px !important;
    margin-bottom: 0px !important;
  }
}
.image-container {
  .imageFilename {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
