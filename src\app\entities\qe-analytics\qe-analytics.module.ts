import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { QEAnalyticsRoutingModule } from './qe-analytics-routing.module';
import { QEAnalyticsComponent } from './qe-analytics/qe-analytics.component';
import { SharedModule } from '../../@shared/shared.module';
import { QEAnalyticsPasswordComponent } from './qe-analytics-password/qe-analytics-password.component';

@NgModule({
  declarations: [QEAnalyticsComponent, QEAnalyticsPasswordComponent],
  imports: [CommonModule, QEAnalyticsRoutingModule, SharedModule]
})
export class QEAnalyticsModule {}
