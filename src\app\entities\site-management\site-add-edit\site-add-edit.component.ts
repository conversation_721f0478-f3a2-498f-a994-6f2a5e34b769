import { DatePipe, Location } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormControl, FormGroup, NgForm } from '@angular/forms';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { NbDateService } from '@nebular/theme';
import JSZip from 'jszip';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription, debounceTime, forkJoin } from 'rxjs';
import * as uuid from 'uuid';
import { CommonDropboxFileUploadComponent } from '../../../@shared/components/common-dropbox-file-upload/common-dropbox-file-upload.component';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { TagListResponseModel } from '../../../@shared/components/image-dropbox-gallery/drop-box.model';
import { APP_ROUTES, AppConstants, ErrorMessages } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { ReportModel, ReportType } from '../../../@shared/models/report.model';
import {
  ArrayTypesList,
  AutomationSiteDetail,
  CMFilter,
  CustomerContact,
  DataSource,
  ListOfAutomationDataSource,
  ListOfAutomationDataSourcePartner,
  ListOfAutomationSitePerformance,
  Month,
  MonthFullName,
  MonthID,
  PMFilter,
  PhotoGalleryImages,
  PrimaryDevice,
  ProductionExpectation,
  SITE_ADD_EDIT_SCREEN_TABS_ENUM,
  SITE_ADD_EDIT_SCREEN_TABS_NAME,
  QECategoryType,
  QEServiceCategoryTypeList,
  Site,
  SiteAccess,
  SiteAuditHistory,
  SiteImageTypeAPIEnum,
  SiteImageTypes,
  SiteLayout,
  SitePhotoCMTableModal,
  SitePhotoPMTableModal,
  SitePortfolioCustomerContactMap,
  SitesOutageResponse,
  Zones
} from '../../../@shared/models/site.model';
import { TimeZone } from '../../../@shared/models/user.model';
import { AlertService, SharedCPSDataService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { DropboxImageGalleryService } from '../../../@shared/services/dropbox-image-gallery.service';
import { StateService } from '../../../@shared/services/states.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { CustomerSiteInfoArchiveComponent } from '../../customer-management/customer-add-edit/customer-site-info-archive/customer-site-info-archive.component';
import { CustomerService } from '../../customer-management/customer.service';
import { DataSharingService } from '../../customer-management/datasharing.service';
import { NotesEntityName, NotesEntityType } from '../../notes-management/notes-management.model';
import { ContractService } from '../../operations/contracts/contract.service';
import { ContractSiteDDL } from '../../operations/contracts/contracts.model';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { ProfileService } from '../../profile/profile.service';
import { ModelComponent } from '../../report/model/model.component';
import { ReportService } from '../../report/report.service';
import { SiteDeviceService } from '../../site-device/site-device.service';
import { AttachmentListResponse, FileListPaginationParams, TicketPriorityMapping } from '../../ticket-management/ticket.model';
import { UserService } from '../../user-management/user.service';
import { ImageGalleryModelComponent } from '../image-gallery-model/image-gallery-model.component';
import { SiteService } from '../site.service';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';

@Component({
  selector: 'sfl-site-add-edit',
  templateUrl: './site-add-edit.component.html',
  styleUrls: ['./site-add-edit.component.scss']
})
export class SiteAddEditComponent implements OnInit, OnDestroy {
  loading = false;
  sitePhotoLoading = false;
  siteDetail: Site = new Site();
  site: Site = new Site();
  photoGalleryImage: PhotoGalleryImages[] = [];
  regex = AppConstants.regex;
  dataSource: DataSource = new DataSource();
  fullDateFormat = AppConstants.fullDateFormat;
  pmFilterModel: PMFilter = new PMFilter();
  sitePhotoPMTableData: SitePhotoPMTableModal[] = [];
  sitePhotoCMTableData: SitePhotoCMTableModal[] = [];
  CMfilterModel: CMFilter = new CMFilter();
  sourceList: ListOfAutomationDataSource[] = [];
  partner: ListOfAutomationDataSourcePartner[] = [];
  partnerList: any = [];
  sourceId: number[] = [];
  years = [];
  siteLayoutDetail: SiteLayout;
  states: Dropdown[];
  subscription: Subscription = new Subscription();
  isEdit = false;
  isDetail = false;
  isCreate = false;
  id: number;
  user: string;
  deviceMeter: Dropdown[] = [];
  deviceWeather: Dropdown[] = [];
  portData: any = [];
  customerData: any = [];
  partnerId: number[] = [];
  modalRef: BsModalRef;
  arrayTypeList = ArrayTypesList;
  contactNoFormat = AppConstants.phoneNumberMask;
  files: File[] = [];
  siteLayoutImg: File[] = [];
  zoneMapImg: File[] = [];
  masterImg: File[] = [];
  automationSiteName = [];
  siteDisplay: boolean = true;
  timeZoneList: TimeZone[] = [];
  siteOutage: SitesOutageResponse = new SitesOutageResponse();
  oldSiteOutageSetting: SitesOutageResponse = new SitesOutageResponse();
  siteAccessInformation: SiteAccess = new SiteAccess();
  isOutageAlert = false;
  eGaugeCredentials = {
    automationUserName: '',
    automationPassword: '',
    eGaugeIndex: null
  };
  currentIsActiveStatusInBE = false;
  public inverterTypes = [
    { name: 'Central', id: 1 },
    { name: 'String', id: 2 },
    { name: 'Micro', id: 4 }
  ];
  public replenishmentTypes = [
    { name: 'NTP Date', id: 1 },
    { name: 'Fiscal Year', id: 2 }
  ];
  month = Month;
  monthId = MonthID;
  siteLoader = [];
  activeTab: string;
  activeTabTitle: string;
  loadAutomationTable = true;
  siteExpectedTableHeader: number[] = [];
  performanceExpectation: ProductionExpectation[] = [];
  productionLoading = false;
  performanceRecordUpdated = false;
  reportsData: ReportModel[] = [];
  reportTypeData: ReportType[];
  ticketPriority = TicketPriorityMapping;
  dragDropRegion: boolean = false;
  selectedImages: any[] = [];
  isarchiveFilter: string;
  isArchiveFilter: string;
  updatedData: any[] = [];
  noticeDetailsArray = [
    { value: '24', name: '24 Hours' },
    { value: '48', name: '48 Hours' },
    { value: '>48', name: '>48 Hours' }
  ];
  ladderDetailesArray = [
    { value: '17', name: "17'" },
    { value: '22', name: "22'" },
    { value: '>22', name: ">22'" }
  ];
  userMomentDateTimeFormat = AppConstants.momentDateTimeFormat;
  siteAuditHistory: SiteAuditHistory[] = [];
  historyAccordion = false;
  exclusionsLoading = false;
  filteredSiteTypeIds: number[] = [];

  // Date properties for replenishment type
  fiscalYearStart: Date;
  fiscalYearEnd: Date;

  filteredInverterTypeIds: number[] = [];
  filteredAssessmentTypeIds: number[] = [];
  filteredPriorityIds: number[] = [];
  filteredPrimaryMeterDeviceIds: number[] = [];
  filteredAggregateMeterDeviceIds: number[] = [];
  filteredSubMeterDeviceIds: number[] = [];
  customerContracts: ContractSiteDDL[] = [];
  attachmentsLoading = false;
  createFileUploadList = [];
  fileAttachments: AttachmentListResponse = new AttachmentListResponse();
  filesPaginationParams: FileListPaginationParams = new FileListPaginationParams();
  notesPaginationParams: FileListPaginationParams = new FileListPaginationParams();
  sitePhotoLibraryTab = false;
  checkboxStates = {
    oilFilled: {
      utility: false,
      customer: false
    },
    bess: {
      utility: false,
      customer: false
    },
    dry: {
      utility: false,
      customer: false
    }
  };
  siteImageTypes = SiteImageTypes;
  addRemoveFilesTagsModalRef: BsModalRef;
  selectedFilesNames = '';
  @ViewChild('addRemoveFilesTagsModal', { static: false }) addRemoveFilesTagsModal: TemplateRef<any>;
  isSelectedAllFiles = false;
  filesTagList: TagListResponseModel[] = [];
  filteredAppliedTags = [];
  allSelectedFiles = [];
  fileTagIds = [];
  selectedFilesNamesString: string;
  sortOptionList = {
    fileName: 'asc',
    createdDate: 'asc'
  };
  sortBy = 'createdDate';
  fileSearchText = '';
  fileSearchModelChanged = new Subject<string>();
  selectedContract: ContractSiteDDL;
  filterModel: { page: number; items: number; sortBy: string; direction: string } = { page: 0, items: 10, direction: '', sortBy: '' };
  siteNotesLoading = false;
  entityTypeName = NotesEntityName[NotesEntityType.SITES];
  entityTypeId = NotesEntityType.SITES;
  nercDropDownOption: Dropdown[];
  clonedVgtCostData!: { id: number; cost: number; customerId: number };
  qeServiceTypeDropDownOption: QECategoryType[] = [];
  qeCategoryTypeList: QEServiceCategoryTypeList[] = [];
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;
  siteAddEditScreenTabsEnum = SITE_ADD_EDIT_SCREEN_TABS_ENUM;
  siteAddEditScreenTabsName = SITE_ADD_EDIT_SCREEN_TABS_NAME;

  constructor(
    private readonly siteService: SiteService,
    private readonly router: Router,
    private readonly alertService: AlertService,
    private readonly route: ActivatedRoute,
    private readonly portfolioService: PortfolioService,
    private readonly stateService: StateService,
    private readonly customerService: CustomerService,
    private readonly modalService: BsModalService,
    private readonly _location: Location,
    protected dateService: NbDateService<Date>,
    public readonly datePipe: DatePipe,
    private readonly commonService: CommonService,
    private readonly storageService: StorageService,
    private readonly siteDeviceService: SiteDeviceService,
    private readonly userService: UserService,
    private readonly reportService: ReportService,
    private readonly dataSharingService: DataSharingService,
    private readonly profileService: ProfileService,
    private readonly contractService: ContractService,
    private readonly dropBoxService: DropboxImageGalleryService,
    private readonly cdr: ChangeDetectorRef,
    public readonly sharedCPSDataService: SharedCPSDataService
  ) {
    this.dataSharingService.updatedData$.subscribe(updatedData => {
      if (updatedData && updatedData.length) {
        this.updatedData = updatedData;
        this.site.isArchive = updatedData[0].isArchive;
        this.site.isActive = updatedData[0].isActive;
      }
    });
  }

  ngOnInit(): void {
    this.getTimeZones();
    this.getDataSource();

    // Initialize fiscal year date range (Jan 1 to Dec 31 of current year)
    const currentYear = new Date().getFullYear();
    this.fiscalYearStart = new Date(currentYear, 0, 1); // January 1st
    this.fiscalYearEnd = new Date(currentYear, 11, 31); // December 31st

    this.route.params.subscribe(params => {
      if (params && params.id && params.mode) {
        this.id = params.id;
        this.isOutageAlert = true;
        if (params.mode === 'edit') {
          this.isEdit = true;
        } else {
          this.isDetail = true;
        }
      } else {
        const activeTab = this.siteDeviceService.getOpenTabName();
        if (activeTab) {
          this.activeTab = activeTab;
          this.activeTabTitle = activeTab;
          this.siteDeviceService.setOpenTabName(null);
        } else {
          this.activeTab = this.siteAddEditScreenTabsName[this.siteAddEditScreenTabsEnum.SITE_INFO];
        }
        this.isCreate = true;
        this.getCustomerAccess();
        this.site.siteAutomationDetails.listOfAutomationSitePerformanceModelTable = this.checkAndAddSitePerformance(this.site);
      }
    });
    this.route.queryParams.subscribe(qParams => {
      this.sitePhotoLibraryTab = qParams.isSiteLayout ? true : false;
      this.setActivatedTabForJumpToMenu(qParams);
    });
    this.getStates();
    this.getReportType();
    this.getNercSiteTypeValues();
    this.getQEServiceTypeDropDown();
    if (this.id) {
      this.getSiteHistoryData();
    }
    this.fileSearchModelChanged.pipe(debounceTime(1000)).subscribe(() => {
      this.getFilesAttachmentsList();
    });

    this.commonService.commonUploadFinish$.subscribe(res => {
      if (res) {
        this.getFilesAttachmentsList();
      }
    });
  }

  replenishmentTypeChange(event: { id: number }) {
    // Initialize fiscalDateRange if it doesn't exist
    if (!this.site.fiscalDateRange) {
      this.site.fiscalDateRange = { start: null, end: null };
    }

    if (event && event.id === 2) {
      // If Fiscal Year is selected, set the date range to current year
      this.site.fiscalDateRange.start = this.fiscalYearStart;
      this.site.fiscalDateRange.end = this.fiscalYearEnd;
    } else {
      // Reset the date range if NTP Date is selected
      this.site.fiscalDateRange.start = null;
      this.site.fiscalDateRange.end = null;
    }
  }

  handleServiceTypeStringId(qeServiceTypes: number[]): void {
    const clonedQEServiceTypes = JSON.parse(JSON.stringify(qeServiceTypes));
    const filteredIds = clonedQEServiceTypes.filter(item => typeof item !== 'string');
    this.setServiceAndCategoryTypeGroup(filteredIds);
  }

  setServiceAndCategoryTypeGroup(qeServiceTypes: number[] = []): void {
    const parentName = qeServiceTypes.length
      ? this.qeCategoryTypeList.find(item =>
          qeServiceTypes.every(qeServiceTypeItem => item.categoryList.map(item => item.id).includes(qeServiceTypeItem))
        )?.name
      : '';

    const parentId = this.qeServiceTypeDropDownOption.find(item => item.name === parentName)?.id;

    if (parentId) {
      qeServiceTypes.splice(0, 0, parentId as number);
    }

    this.site.qeServiceTypes = qeServiceTypes;

    this.handleCategoryTypeListSelection(qeServiceTypes);
  }

  handleCategoryTypeListSelectionRemove($event): void {
    const ids = this.site.qeServiceTypes.filter(item => typeof item === 'string');
    const typeOfId = typeof $event.value.id;

    if (typeOfId === 'string' || !ids.length) {
      this.site.qeServiceTypes = [];
    }
  }

  isRequiredQEServiceTypeGroup(groupName: string): boolean {
    return this.qeServiceTypeDropDownOption.find(item => item.groupName === groupName)?.isRequired ?? false;
  }

  handleCategoryTypeListSelection(qeServiceTypes: number[]): void {
    const ids = qeServiceTypes.filter(item => typeof item === 'string');

    if (!ids.length) {
      qeServiceTypes = [];
      this.site.qeServiceTypes = qeServiceTypes;
    }

    if (ids.length) {
      const gname = this.qeServiceTypeDropDownOption.find(item => item.id === ids[0])?.name;
      const list = this.qeCategoryTypeList.find(item => item.name === gname)?.categoryList ?? [];
      this.qeServiceTypeDropDownOption = this.qeServiceTypeDropDownOption.filter(item => typeof item.id === 'string');
      this.qeServiceTypeDropDownOption.push(...list);
    }

    this.handleServiceCategoryTypeSelection(qeServiceTypes);
  }

  handleServiceCategoryTypeSelection(qeServiceTypes: number[]): void {
    const singleSelectableGroupNames = this.qeServiceTypeDropDownOption.filter(item => item.isSingleSelectable).map(item => item.groupName);
    const singleSelectableGroupIds = this.qeServiceTypeDropDownOption.filter(item => item.isSingleSelectable).map(item => item.groupId);

    this.site.qeServiceTypes = qeServiceTypes;

    const shouldDisable = (item: QECategoryType) =>
      ((item?.parentGroupName && item.groupName !== item.parentGroupName) ||
        (item?.parentGroupId && item.groupId !== item.parentGroupId)) &&
      !qeServiceTypes.filter(item1 => typeof item1 === 'string').length;

    const resetAllOptions = () =>
      this.qeServiceTypeDropDownOption.map(item => ({
        ...item,
        disabled: shouldDisable(item)
      }));

    const groupSelectedByGroupId = (ids: number[]) => {
      const grouped = new Map<number, number[]>();
      ids.forEach(serviceId => {
        const selectedItem = this.qeServiceTypeDropDownOption.find(x => x.id === serviceId);
        if (selectedItem) {
          if (!grouped.has(selectedItem.groupId)) {
            grouped.set(selectedItem.groupId, []);
          }
          grouped.get(selectedItem.groupId).push(serviceId);
        }
      });
      return grouped;
    };

    const isSingleSelectGroup = (groupId: number, groupName: string) =>
      singleSelectableGroupNames.includes(groupName) || singleSelectableGroupIds.includes(groupId);

    const disableUnselectedInGroup = (groupId: number, selectedIds: number[]) =>
      this.qeServiceTypeDropDownOption.map(item =>
        item.groupId === groupId ? { ...item, disabled: !selectedIds.includes(item.id as number) } : item
      );

    this.qeServiceTypeDropDownOption = resetAllOptions();

    if (qeServiceTypes.length > 0) {
      const groupedSelections = groupSelectedByGroupId(qeServiceTypes);

      groupedSelections.forEach((selectedIds, groupId) => {
        const firstItem = this.qeServiceTypeDropDownOption.find(x => x.groupId === groupId);
        if (firstItem && isSingleSelectGroup(groupId, firstItem.groupName)) {
          this.qeServiceTypeDropDownOption = disableUnselectedInGroup(groupId, selectedIds);
        }
      });
    }
  }

  getReportType() {
    this.subscription.add(
      this.reportService.getReportTypeAll().subscribe({
        next: res => {
          this.reportTypeData = res;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getQEServiceTypeDropDown(): void {
    this.subscription.add(
      this.siteService.getQEServiceTypeDropDown().subscribe({
        next: (res: QEServiceCategoryTypeList[]) => {
          this.qeCategoryTypeList = res;
          this.qeServiceTypeDropDownOption = JSON.parse(JSON.stringify(res)).map(
            (item: QEServiceCategoryTypeList) => (delete item.categoryList, item)
          );
          this.qeServiceTypeDropDownOption.push(...res[0].categoryList);
          this.setServiceAndCategoryTypeGroup();
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getNercSiteTypeValues() {
    this.subscription.add(
      this.siteService.getSiteTypeNERCDropDown().subscribe({
        next: res => {
          this.nercDropDownOption = res;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getComponentTitle() {
    let result = 'Site Details';
    if (this.isDetail) {
      return result;
    } else if (this.isEdit) {
      result = `Site Edit`;
      return result;
    } else {
      result = `Site Create`;
      return result;
    }
  }

  onYearSelect(e) {
    this.reportsData = null;
    // this.exclusionReportsData = null;
  }

  getSiteTimeZoneBaseOnLongitudeLatitude(lang, lat) {
    if (lat.length >= 4 && lang.length >= 4) {
      const model = {
        logitude: Number(lang),
        latitude: Number(lat)
      };
      this.subscription.add(
        forkJoin([
          this.siteService.getSiteTimeZoneBaseOnLongitudeLatitude(model),
          this.siteService.getFIPSCodeBaseOnLongitudeLatitude(model)
        ])
          .pipe(debounceTime(1000))
          .subscribe({
            next: res => {
              this.site.siteTimeZoneOffset = res[0];
              this.site.siteLocation.countyId = res[1].countyId;
              this.site.siteLocation.countyName = res[1].countyName;
              this.site.siteLocation.countyFIPSCode = res[1].countyFIPSCode;
            },
            error: e => {
              this.loading = false;
            }
          })
      );
    } else {
      this.site.siteTimeZoneOffset = null;
      this.site.siteLocation.countyId = null;
      this.site.siteLocation.countyName = null;
      this.site.siteLocation.countyFIPSCode = null;
    }
  }

  selectDeselectContact(event, index) {
    this.site.customerContactEmails.forEach(i => (i.useAsJHAContact = false));
    this.site.portfolioCustomerContactId = null;
    if (event) {
      if (this.site.customerContactEmails[index].portfolioCustomerContactId) {
        this.site.portfolioCustomerContactId = Number(this.site.customerContactEmails[index].portfolioCustomerContactId);
      }
      this.site.customerContactEmails[index].useAsJHAContact = event;
    }
  }

  getMonthYear() {
    const month =
      this.siteDetail?.siteAutomationDetails.automationSitePerformanceDetails?.startMonth +
      ' ' +
      this.siteDetail?.siteAutomationDetails.automationSitePerformanceDetails?.startYear;
    if (month === null + ' ' + null) {
      return '-';
    } else {
      return month;
    }
  }

  gotoDevice(customerId, portfolioId, siteId) {
    this.router.navigate(['entities/site-device/list'], {
      queryParams: { customerId, portfolioId, siteId }
    });
  }

  onPortfolioSelect(event) {
    if (!this.isEdit && event && event.contactEmails && event.contactEmails.length) {
      const siteContacts: CustomerContact[] = [];
      for (const i of event.contactEmails) {
        const siteContact = new CustomerContact();
        siteContact.contactName = i.contactName;
        siteContact.customerTitle = i.customerTitle;
        siteContact.email = i.email;
        siteContact.phoneNumber = i.phoneNumber;
        siteContact.useAsTicketContact = i.useAsTicketContact;
        siteContact.portfolioCustomerContactId = i.id;
        siteContacts.push(siteContact);
      }
      this.site.customerContactEmails = siteContacts;
    }
    if (event.customerId && event.id && this.isCreate) {
      const getOutageParams = {
        customerId: event.customerId,
        portfolioId: event.id,
        siteId: null,
        settingType: 3
      };
      this.getSiteOutageDetails(getOutageParams, false);
      this.isOutageAlert = true;
    }
  }

  selectDeselectTicketContact(contact: CustomerContact, index: number, event) {
    if (contact.portfolioCustomerContactId) {
      const contactIndex = this.site.sitePortfolioCustomerContactMap.findIndex(
        x => x.portfolioCustomerContactId === contact.portfolioCustomerContactId
      );
      if (contactIndex > -1) {
        if (this.site.sitePortfolioCustomerContactMap[contactIndex].id) {
          this.site.sitePortfolioCustomerContactMap[contactIndex].useAsTicketContact = contact.useAsTicketContact;
        } else {
          this.site.sitePortfolioCustomerContactMap.splice(contactIndex, 1);
        }
      } else {
        const contactMap: SitePortfolioCustomerContactMap = new SitePortfolioCustomerContactMap();
        contactMap.portfolioCustomerContactId = contact.portfolioCustomerContactId;
        contactMap.siteId = Number(this.id);
        contactMap.useAsTicketContact = contact.useAsTicketContact;
        this.site.sitePortfolioCustomerContactMap.push(contactMap);
      }
    }
  }

  getStates() {
    this.loading = true;
    this.subscription.add(
      this.stateService.getStates().subscribe({
        next: res => {
          this.states = res;
          if (this.isEdit || this.isDetail) {
            this.getSite(this.id, false);
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getSiteOutageDetails(getOutageParams, isParentSetting) {
    this.loading = true;
    this.subscription.add(
      this.customerService.getAlertOutageDetail(getOutageParams).subscribe({
        next: res => {
          if (isParentSetting) {
            this.siteOutage = {
              ...res,
              isParentSetting: true
            };
            this.siteOutage = {
              ...res,
              isParentSetting: true,
              customerId: this.oldSiteOutageSetting.customerId,
              outageId: this.oldSiteOutageSetting.outageId,
              parentSettingId: this.oldSiteOutageSetting.parentSettingId,
              portfolioId: this.oldSiteOutageSetting.portfolioId,
              siteId: this.oldSiteOutageSetting.siteId,
              settingType: this.oldSiteOutageSetting.settingType,
              zeroGeneration: this.oldSiteOutageSetting.zeroGeneration,
              portfoliosSites: [],
              sites: []
            };
          } else {
            this.siteOutage = res;
            this.oldSiteOutageSetting = res;
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getMonthName(month: number): string {
    switch (month) {
      case 1:
        return MonthFullName.JANUARY;
      case 2:
        return MonthFullName.FEBRUARY;
      case 3:
        return MonthFullName.MARCH;
      case 4:
        return MonthFullName.APRIL;
      case 5:
        return MonthFullName.MAY;
      case 6:
        return MonthFullName.JUNE;
      case 7:
        return MonthFullName.JULY;
      case 8:
        return MonthFullName.AUGUST;
      case 9:
        return MonthFullName.SEPTEMBER;
      case 10:
        return MonthFullName.OCTOBER;
      case 11:
        return MonthFullName.NOVEMBER;
      case 12:
        return MonthFullName.DECEMBER;
    }
  }

  getObjectValues(obj: any): any[] {
    return Object.values(obj);
  }

  useParentSetting(isParentSetting) {
    if (isParentSetting) {
      const getOutageParams = {
        customerId: this.siteOutage.customerId,
        portfolioId: this.siteOutage.portfolioId,
        siteId: null,
        settingType: 2
      };
      this.getSiteOutageDetails(getOutageParams, true);
    }
  }

  addRemoveZoneItems(zoneItem = null, siteForm?): void {
    if (zoneItem) {
      if (zoneItem.id === 0) {
        const formGroup = siteForm.form;
        const controlKeys = Object.keys(formGroup.controls);
        const indexOfRemovedElement = this.site.zones.findIndex(obj => obj.customZoneAddEditId === zoneItem.customZoneAddEditId);
        if (indexOfRemovedElement >= 0 && indexOfRemovedElement < controlKeys.length) {
          const controlKeyToRemove = controlKeys[indexOfRemovedElement];
          // Remove the control using removeControl
          formGroup.removeControl(controlKeyToRemove);
        }
        this.site.zones = this.site.zones.filter(zone => zoneItem.customZoneAddEditId !== zone.customZoneAddEditId);
      }
    } else {
      // prompt if wo exist
      if (zoneItem?.zoneWOName) {
        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          initialState: {
            message: `The ${zoneItem?.zoneName} is associated with a ${zoneItem?.zoneWOName} workorder, are you sure you want to remove this zone?`
          }
        };
        this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
        this.modalRef.content.onClose.subscribe(result => {
          if (result) {
            const newZoneItem = { ...new Zones(), customZoneAddEditId: uuid.v4() };
            this.site.zones = [...this.site.zones, newZoneItem];
          }
        });
      } else {
        const newZoneItem = { ...new Zones(), customZoneAddEditId: uuid.v4() };
        this.site.zones = [...this.site.zones, newZoneItem];
      }
    }
  }

  zoneNameErrorCheckInit(form: NgForm): void {
    this.site.zones.forEach((zone, index) => {
      const formGroup = form.form.controls[index] as FormGroup;

      if (formGroup && formGroup.controls['zoneName']) {
        const ngModelControl = formGroup.controls['zoneName'] as FormControl;
        this.zoneNameErrorCheck(ngModelControl, index);
      }
    });
  }

  zoneNameErrorCheck(ngModelControl: FormControl, index: number): void {
    this.site.zones[index].hasError = ngModelControl.hasError('required') && !ngModelControl.value ? true : false;
    // this.cdr.detectChanges();
  }

  private setCheckboxStates(): void {
    const { utilityOwned, isUtilityDryXFMR, isUtilityBessXFMR, xfmr, dryXFMR, bessxfmr } = this.site.siteLayoutDetail;

    this.checkboxStates.oilFilled.utility = utilityOwned;
    this.checkboxStates.dry.utility = isUtilityDryXFMR;
    this.checkboxStates.bess.utility = isUtilityBessXFMR;

    this.checkboxStates.oilFilled.customer = xfmr ? !utilityOwned : false;
    this.checkboxStates.dry.customer = dryXFMR ? !isUtilityDryXFMR : false;
    this.checkboxStates.bess.customer = bessxfmr ? !isUtilityBessXFMR : false;
  }

  setActivatedTabForJumpToMenu(queryParams: Params): void {
    const openedTab = queryParams['openedTab'] as SITE_ADD_EDIT_SCREEN_TABS_ENUM;
    if (openedTab) {
      this.siteDeviceService.setOpenTabName(this.siteAddEditScreenTabsName[openedTab]);
      this.removeQueryParams(queryParams);
    }
  }

  removeQueryParams(queryParams: Params): void {
    const { openedTab, ...rest } = queryParams;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: rest,
      replaceUrl: true
    });
  }

  setCPSForJumpToMenu(res: Site): void {
    const qeMenuConfigs = this.route.snapshot.data['qeMenuConfigs'];
    this.sharedCPSDataService.setAndExtractSharedCPSData(qeMenuConfigs, { ...res, siteId: res.id });
  }

  getSite(id, isFromUpload) {
    this.loading = true;
    this.getExpectedPerformanceData(id);
    this.getSitePhotoImages(id);
    this.subscription.add(
      this.siteService.getById(id).subscribe({
        next: (res: Site) => {
          this.setCPSForJumpToMenu(res);
          this.id = id ? id : res.id;
          if (!isFromUpload) {
            if (!res.siteAutomationDetails.automationSiteDetail) {
              res.siteAutomationDetails.automationSiteDetail = [];
            } else {
              if (res.siteAutomationDetails.automationSiteDetail.length) {
                for (const [i, v] of res.siteAutomationDetails.automationSiteDetail.entries()) {
                  this.dataSourceSelect(v.automationDataSourceId, v.automationDataSourceName, i);
                  if (v.partnerNumber) {
                    this.partnerSelect(v.partnerNumber, v.automationDataSourceId, i);
                  }
                }
              }
            }
            res.siteAutomationDetails.listOfAutomationSitePerformanceModelTable = this.checkAndAddSitePerformance(res);
            this.site = JSON.parse(JSON.stringify(res));
            this.setServiceAndCategoryTypeGroup(this.site.qeServiceTypes ?? []);
            this.site.zones =
              this.site?.zones && this.site?.zones.length > 0
                ? this.site.zones.map(
                    item => new Zones(item.id, item.zoneName, item.zoneWOName, item.isSelected, item.isDeleted, uuid.v4(), false)
                  )
                : [];
            this.setCheckboxStates();
            if (this.site && this.site.contractStartDate) {
              this.site.contractStartDate = new Date(this.site.contractStartDate);
            }
            if (this.site.fiscalDateRange) {
              this.site.fiscalDateRange.start = this.site.fiscalDateRange.start ? new Date(this.site.fiscalDateRange.start) : null;
              this.site.fiscalDateRange.end = this.site.fiscalDateRange.end ? new Date(this.site.fiscalDateRange.end) : null;
            }
            this.siteDetail = JSON.parse(JSON.stringify(res));
            this.currentIsActiveStatusInBE = this.site.isActive;
            const activeTab = this.siteDeviceService.getOpenTabName();
            if (this.sitePhotoLibraryTab) {
              this.activeTab = this.siteAddEditScreenTabsName[this.siteAddEditScreenTabsEnum.SITE_PHOTO_LIBRARY];
            } else if (activeTab) {
              this.activeTab = activeTab;
              this.activeTabTitle = activeTab;
              this.siteDeviceService.setOpenTabName(null);
            } else {
              this.activeTab = this.siteAddEditScreenTabsName[this.siteAddEditScreenTabsEnum.SITE_INFO];
            }
            if (
              activeTab === this.siteAddEditScreenTabsName[this.siteAddEditScreenTabsEnum.SITE_PHOTO_LIBRARY] &&
              !this.checkAuthorisationsFn([this.roleType.CUSTOMER]) &&
              this.site.id &&
              this.site.siteAutomationDetails.automationSiteDetail &&
              this.site.siteAutomationDetails.automationSiteDetail.length &&
              this.site.siteAutomationDetails.automationSiteDetail[0].automationSiteDetailId
            ) {
              this.alertService.showInfoToast('Automation Site Device List is not available for this site.');
            }
            if (this.sitePhotoLibraryTab) {
              this.activeTabTitle = this.siteAddEditScreenTabsName[this.siteAddEditScreenTabsEnum.SITE_PHOTO_LIBRARY];
            }
            this.getCustomerAccess();
            this.getPrimaryDevice(this.site.id);
          } else {
            this.site.siteImages = res.siteImages;
            this.site.siteZoneImages = res.siteZoneImages;
          }
          this.siteOutage = res.outageSetting;
          this.oldSiteOutageSetting = res.outageSetting;
          this.getFilteredPmReports();
          this.getFilteredCmReports();
          this.getCusomerContracts();
          this.getFilesAttachmentsList();
          this.loading = false;
          this.clonedVgtCostData = JSON.parse(JSON.stringify({ id: this.site.id, cost: this.site.cost, customerId: this.site.customerId }));
          this.isVGTScopeAvailableForSite(this.site.customerId, this.site.id);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getSitePhotoImages(id: number) {
    this.subscription.add(
      this.siteService.getPhotoImageById(id).subscribe({
        next: (res: any) => {
          this.photoGalleryImage = res.map(images => {
            return {
              ...images,
              isSelected: false
            };
          });
          if (this.photoGalleryImage.length === 0) {
            this.dragDropRegion = false;
          } else {
            this.dragDropRegion = true;
          }
          const getOutageParams = {
            customerId: res.customerId,
            portfolioId: res.portfolioId,
            siteId: res.id,
            settingType: 3
          };
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getExpectedPerformanceData(id: number) {
    this.siteExpectedTableHeader = [];
    this.subscription.add(
      this.siteService.getExpectedPerformanceDataById(id).subscribe({
        next: (res: ProductionExpectation[]) => {
          for (const i of res) {
            this.siteExpectedTableHeader.push(i.year);
            let yearDif = 0;
            let color = this.getRandomColor();
            for (const i of res) {
              for (const j of i.siteProdBreak) {
                if (j.yearDifference === yearDif) {
                  j['yearDifferenceColor'] = color;
                } else {
                  yearDif = j.yearDifference;
                  color = this.getRandomColor();
                  j['yearDifferenceColor'] = color;
                }
              }
            }
            this.performanceExpectation = res;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getRandomColor() {
    const letters = 'BCDEF'.split('');
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * letters.length)];
    }
    return color;
  }

  checkAndAddSitePerformance(data: Site) {
    const performance: ListOfAutomationSitePerformance[] = [];
    for (const [i, v] of Month.entries()) {
      let temp = new ListOfAutomationSitePerformance();
      temp.monthName = v;
      temp.month = i + 1;
      const index = data.siteAutomationDetails.listOfAutomationSitePerformanceModelTable.findIndex(x => x.month === i + 1);
      if (data.siteAutomationDetails.listOfAutomationSitePerformanceModelTable.length === 0 || index === -1) {
        performance.push(temp);
      } else {
        temp = data.siteAutomationDetails.listOfAutomationSitePerformanceModelTable[index];
        temp['monthName'] = v;
        performance.push(temp);
      }
    }
    return performance;
  }

  uploadFiles(id, msg?: string, isSiteCreate?: boolean) {
    this.loading = true;
    const tempArray = [];
    if (this.createFileUploadList.length) {
      for (const fileObj of this.createFileUploadList) {
        const formData: FormData = new FormData();
        formData.append('files', fileObj.file as File);
        if (fileObj.fileTag.length) {
          for (const tag of fileObj.fileTag) {
            formData.append('fileTagIds', `${tag}`);
          }
        }
        formData.append('customerId', `${this.site.customerId}`);
        formData.append('id', '0');
        formData.append('portfolioId', `${this.site.portfolioId}`);
        formData.append('siteId', `${id}`);
        formData.append('entityId', `${id}`);
        formData.append('entityNumber', '');
        formData.append('moduleType', '9');
        formData.append('fileType', `${fileObj.fileType}`);
        if (fileObj.notes) {
          formData.append('notes', `${fileObj.notes}`);
        }
        tempArray.push(this.dropBoxService.uploadFilesToGallery(formData));
      }
    }
    if (this.files.length) {
      for (const i of this.files) {
        const formData: FormData = new FormData();
        formData.append('Image', i as File);
        formData.append('SiteId', id.toString());
        tempArray.push(this.siteService.getSiteUplodedImage(formData));
      }
    }

    this.subscription.add(
      forkJoin(tempArray).subscribe({
        next: res => {
          if (this.site.id) {
            if (isSiteCreate) {
              // once the site is created update the url and navigate the user to that specific site.
              this.router.navigateByUrl(`${APP_ROUTES.SITES}/edit/${this.site.id}`);
              this.alertService.showSuccessToast(msg);
            } else {
              this.alertService.showSuccessToast('Files uploaded.');
              this.getSite(id, true);
            }
          } else {
            this.alertService.showSuccessToast(msg);
          }
          this.files = [];
          this.siteLayoutImg = [];
          this.zoneMapImg = [];
          this.createFileUploadList = [];
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  // Create and Edit Site
  createSite(siteForm) {
    // TODO Remove once confirm
    // if (siteForm.form.value.emrPhone) {
    //   if (!siteForm.form.value.emrPhone.match(/^[0-9,]*$/g)) {
    //     siteForm.form.controls['emrPhone'].setErrors({ incorrect: true });
    //   }
    // }
    if (siteForm.form.valid) {
      this.site.siteLocation.latitude = Number(this.site.siteLocation.latitude);
      this.site.siteLocation.logitude = Number(this.site.siteLocation.logitude);
      this.site.siteLayoutDetail.acSize = Number(this.site.siteLayoutDetail.acSize);
      this.site.siteLayoutDetail.dcSize = Number(this.site.siteLayoutDetail.dcSize);
      this.site.siteLayoutDetail.inv = Number(this.site.siteLayoutDetail.inv);
      this.site.siteLayoutDetail.xfmr = this.site.siteLayoutDetail.xfmr ? Number(this.site.siteLayoutDetail.xfmr) : null;
      this.site.siteLayoutDetail.bessxfmr = this.site.siteLayoutDetail.bessxfmr ? Number(this.site.siteLayoutDetail.bessxfmr) : null;
      this.site.siteLayoutDetail.dryXFMR = this.site.siteLayoutDetail.dryXFMR ? Number(this.site.siteLayoutDetail.dryXFMR) : null;
      this.site.siteAutomationDetails.automationSitePerformanceDetails.systemLosses = Number(
        this.site.siteAutomationDetails.automationSitePerformanceDetails.systemLosses
      );
      this.site.siteAutomationDetails.automationSitePerformanceDetails.temperatureCoefficient = Number(
        this.site.siteAutomationDetails.automationSitePerformanceDetails.temperatureCoefficient
      );
      this.site.siteAutomationDetails.automationSitePerformanceDetails.inverterEfficiency = Number(
        this.site.siteAutomationDetails.automationSitePerformanceDetails.inverterEfficiency
      );
      this.site.siteLayoutDetail.utilityOwned = this.checkboxStates.oilFilled.utility;
      this.site.siteLayoutDetail.isUtilityDryXFMR = this.checkboxStates.dry.utility;
      this.site.siteLayoutDetail.isUtilityBessXFMR = this.checkboxStates.bess.utility;
      this.site.siteNumber = this.site.siteNumber ? String(this.site.siteNumber) : null;
      this.site.qeSiteId = this.site.qeSiteId ? String(this.site.qeSiteId) : null;
      this.site.outageSetting = this.siteOutage;
      this.site.archiveUpdatedData = this.updatedData;
      if (!this.site.siteAutomationDetails.automationSitePerformanceDetails.temperatureCoefficient) {
        this.site.siteAutomationDetails.automationSitePerformanceDetails.temperatureCoefficient = null;
      }
      if (!this.site.siteAutomationDetails.automationSitePerformanceDetails.inverterEfficiency) {
        this.site.siteAutomationDetails.automationSitePerformanceDetails.inverterEfficiency = null;
      }
      if (!this.site.siteAutomationDetails.automationSitePerformanceDetails.systemLosses) {
        this.site.siteAutomationDetails.automationSitePerformanceDetails.systemLosses = null;
      }
      const performance: ListOfAutomationSitePerformance[] = [];
      for (const item of this.site.siteAutomationDetails.listOfAutomationSitePerformanceModelTable) {
        if (item.expectedInsolationValue || item.expectedProductionValue) {
          item.expectedInsolationValue = item.expectedInsolationValue ? Number(item.expectedInsolationValue) : null;
          item.expectedProductionValue = item.expectedProductionValue ? Number(item.expectedProductionValue) : null;
          performance.push(item);
        }
      }
      this.site.siteAutomationDetails.listOfAutomationSitePerformanceModelTable = performance;
      const model = JSON.parse(JSON.stringify(this.site));
      model.qeServiceTypes = model?.qeServiceTypes?.filter(item => typeof item !== 'string') ?? [];
      if (this.site.contractStartDate) {
        model.contractStartDate = this.datePipe.transform(this.site.contractStartDate, AppConstants.fullDateFormat);
      }
      if (this.site.fiscalDateRange) {
        this.site.fiscalDateRange.start = this.site.fiscalDateRange.start
          ? this.datePipe.transform(this.site.fiscalDateRange.start, AppConstants.fullDateFormat)
          : null;
        this.site.fiscalDateRange.end = this.site.fiscalDateRange.end
          ? this.datePipe.transform(this.site.fiscalDateRange.end, AppConstants.fullDateFormat)
          : null;
      }
      if (this.isCreate) {
        model.isActive = true;
        model.isArchive = false;
        this.loading = true;
        this.site.id = 0;
        this.subscription.add(
          this.siteService.createSite(model).subscribe({
            next: res => {
              this.site.id = res.entryid;
              this.loading = false;
              if (
                this.files.length ||
                this.createFileUploadList.length ||
                this.siteLayoutImg.length ||
                this.masterImg.length ||
                this.zoneMapImg.length
              ) {
                if (this.files.length || this.createFileUploadList.length) {
                  this.uploadFiles(res.entryid, res.message, true);
                }
                if (this.masterImg.length || this.siteLayoutImg.length || this.zoneMapImg.length) {
                  this.createSiteLayoutAndPhotoGalleryFiles(res.entryid, res.message, true);
                }
              } else {
                this.alertService.showSuccessToast(res.message);
                // once the site is created update the url and navigate the user to that specific site.
                this.router.navigateByUrl(`${APP_ROUTES.SITES}/edit/${res.entryid}`);
              }
              this.handleServiceTypeStringId(this.site?.qeServiceTypes ?? []);
              this.isCreate = false;
              this.isEdit = true;
            },
            error: e => {
              this.handleServiceTypeStringId(this.site?.qeServiceTypes ?? []);
              this.loading = false;
            }
          })
        );
      } else if (this.site.customerId) {
        if (this.site.portfolioId) {
          if (this.isEdit) {
            this.site.workOrderCount = 0;
            if (this.site.isArchive) {
              const ngModalOptions: ModalOptions = {
                backdrop: 'static',
                keyboard: false,
                animated: true,
                initialState: {
                  message: `${this.site.siteName} will be archived along with all ${this.site.siteName}'s devices. Are you sure you want to save?`
                }
              };
              this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
              this.modalRef.content.onClose.subscribe(result => {
                if (result) {
                  this.updateSiteAfterArchiveConfirmation(model);
                }
              });
            } else {
              this.updateSiteAfterArchiveConfirmation(model);
            }
          }
        }
      }
    } else {
      this.zoneNameErrorCheckInit(siteForm);
      this.alertService.showErrorToast('Please fill the mandatory fields to submit the form.');
    }
  }

  formatTime(time: string, index: number, fieldToUpdate: string) {
    const timePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (timePattern.test(time)) {
      this.siteOutage.timeSetting[index][fieldToUpdate] = time;
    } else if (time.charAt(2) === ':') {
      this.siteOutage.timeSetting[index][fieldToUpdate] = time;
    } else if (time.length === 4) {
      const hours = time.substring(0, 2);
      const minutes = time.substring(2, 4);
      const formattedTime = `${hours}:${minutes}`;
      this.siteOutage.timeSetting[index][fieldToUpdate] = formattedTime;
    }
  }

  updateSiteAfterArchiveConfirmation(model) {
    this.loading = true;
    this.subscription.add(
      this.siteService.updateSite(model).subscribe({
        next: res => {
          this.updatedData = [];
          this.handleServiceTypeStringId(this.site?.qeServiceTypes ?? []);
          this.dataSharingService.updateData(this.updatedData);
          this.dataSharingService.clearCachedSites();
          this.loading = false;
          this.router.navigateByUrl(APP_ROUTES.SITES);
          this.alertService.showSuccessToast(res.message);
        },
        error: _e => {
          this.handleServiceTypeStringId(this.site?.qeServiceTypes ?? []);
          this.loading = false;
        }
      })
    );
  }

  addContact() {
    const newContact = new CustomerContact();
    this.site.customerContactEmails.push(newContact);
  }

  accordionChange(e, val: string) {
    this[val] = e ? false : true;
  }

  onCustomerSelect(updatedTheSelectedCustomer = false) {
    if (this.site.customerId) {
      this.subscription.add(
        this.portfolioService.getAllPortfoliosByCustomerId(this.isEdit, this.site.customerId, true).subscribe({
          next: res => {
            setTimeout(() => {
              if (!this.isEdit) {
                const portList = res.filter(p => p.customerId === this.site.customerId && p.isActive);
                this.portData = portList;
              } else {
                this.portData = res;
              }
            }, 0);
            // if the customer has been change this means the contract for the new selected customer would be different, hence we need to clear the current selected contract.
            if (updatedTheSelectedCustomer) {
              this.site.contractId = null;
              this.site.contractName = '';
              this.site.contractStartDate = '';
            }
            this.getCusomerContracts();
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    }
  }

  deleteContact(id: number, index: number) {
    if (this.site.customerContactEmails.length > 1) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          if (id) {
            this.subscription.add(
              this.siteService.deleteSiteContact(id).subscribe({
                next: res => {
                  this.site.customerContactEmails = res;
                  this.alertService.showSuccessToast('Contact deleted successfully.');
                  this.loading = false;
                },
                error: e => {
                  this.loading = false;
                }
              })
            );
          } else {
            this.site.customerContactEmails.splice(index, 1);
          }
        }
      });
    } else {
      this.alertService.showErrorToast('Minimum one site contact is required.');
    }
  }

  getPrimaryDevice(site) {
    this.subscription.add(
      this.siteService.getPrimaryDevices(site).subscribe({
        next: (res: PrimaryDevice) => {
          this.deviceMeter = res.listOfDeviceMeter;
          this.deviceWeather = res.listOfDeviceWeatherStation;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  isVGTScopeAvailableForSite(customerId: number = this.site.customerId, siteId: number = this.site.id ?? null): void {
    this.loading = true;
    this.subscription.add(
      this.siteService.isVGTScopeAvailableForSite(customerId, siteId).subscribe({
        next: (res: boolean) => {
          if (
            res &&
            this.clonedVgtCostData &&
            this.clonedVgtCostData.id &&
            (this.clonedVgtCostData.customerId === customerId || this.clonedVgtCostData.id === siteId)
          ) {
            this.site.cost = this.clonedVgtCostData.cost;
          } else {
            this.site.cost = null;
          }
          this.site.isSiteSelectedForVGTWO = res;
          this.siteDetail = JSON.parse(JSON.stringify(this.site));
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getDataSource() {
    if (!this.site.siteAutomationDetails.automationSitePerformanceDetails.temperatureCoefficient) {
      this.site.siteAutomationDetails.automationSitePerformanceDetails.temperatureCoefficient = null;
    }
    if (!this.site.siteAutomationDetails.automationSitePerformanceDetails.inverterEfficiency) {
      this.site.siteAutomationDetails.automationSitePerformanceDetails.inverterEfficiency = null;
    }
    if (!this.site.siteAutomationDetails.automationSitePerformanceDetails.systemLosses) {
      this.site.siteAutomationDetails.automationSitePerformanceDetails.systemLosses = null;
    }
    this.subscription.add(
      this.siteService.getAllDataSource().subscribe({
        next: (res: DataSource) => {
          this.dataSource = res;
          this.partner = res.listOfAutomationDataSourcePartner;
          this.sourceList = res.listOfAutomationDataSource;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
    this.years = this.commonService.getYear(false, true, false, 2008);
  }

  dataSourceSelect(event, eventName, _index: number, isChange = false) {
    this.partnerList[_index] = [];
    this.automationSiteName[_index] = [];
    if (isChange) {
      this.site.siteAutomationDetails.automationSiteDetail[_index].automationDataSourceName = eventName;
      if (event === 4) {
        this.site.siteAutomationDetails.automationSiteDetail[_index].partnerNumber = '1';
        this.site.siteAutomationDetails.automationSiteDetail[_index].automationSiteName = this.siteDetail.siteName;
        this.site.siteAutomationDetails.automationSiteDetail[_index].siteNumber = null;
        this.siteDisplay = false;
      } else if (event === 5) {
        this.site.siteAutomationDetails.automationSiteDetail[_index].partnerNumber = '1';
        this.site.siteAutomationDetails.automationSiteDetail[_index].automationSiteName = null;
        this.site.siteAutomationDetails.automationSiteDetail[_index].ipAddress = null;
        this.site.siteAutomationDetails.automationSiteDetail[_index].siteNumber = null;
        this.site.siteAutomationDetails.automationSiteDetail[_index].token = null;
        this.siteDisplay = false;
      } else {
        this.site.siteAutomationDetails.automationSiteDetail[_index].partnerNumber = null;
        this.site.siteAutomationDetails.automationSiteDetail[_index].automationSiteName = null;
        this.site.siteAutomationDetails.automationSiteDetail[_index].siteNumber = null;
        this.site.siteAutomationDetails.automationSiteDetail[_index].ipAddress = null;
        this.site.siteAutomationDetails.automationSiteDetail[_index].token = null;
      }
    }
    this.partner.forEach(x => {
      if (event === x.automationDataSourceId) {
        this.partnerList[_index].push(x);
      }
    });
  }

  openEGaugeCredentialModal(template: TemplateRef<any>, _index: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true
    };
    this.eGaugeCredentials = {
      automationUserName: this.site.siteAutomationDetails.automationSiteDetail[_index].automationUserName,
      automationPassword: this.site.siteAutomationDetails.automationSiteDetail[_index].automationPassword,
      eGaugeIndex: _index
    };
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  onModalClose() {
    this.modalRef.hide();
    this.eGaugeCredentials = {
      automationUserName: '',
      automationPassword: '',
      eGaugeIndex: null
    };
  }

  saveCredential(index) {
    (this.site.siteAutomationDetails.automationSiteDetail[index].automationUserName = this.eGaugeCredentials.automationUserName),
      (this.site.siteAutomationDetails.automationSiteDetail[index].automationPassword = this.eGaugeCredentials.automationPassword),
      this.onModalClose();
  }

  partnerSelect(event, dataSourceId, _index: number, siteNumber = null, isChange = false) {
    this.siteLoader[_index] = true;
    this.automationSiteName[_index] = [];
    if (dataSourceId === 4 || dataSourceId === 5) {
      this.siteDisplay = false;
    }
    if (isChange) {
      this.site.siteAutomationDetails.automationSiteDetail[_index].automationSiteName = null;
      this.site.siteAutomationDetails.automationSiteDetail[_index].siteNumber = null;
    }
    const autoNumber = this.site.siteAutomationDetails.automationSiteDetail.length
      ? this.site.siteAutomationDetails.automationSiteDetail[_index].siteNumber
      : null;
    const number = siteNumber ? siteNumber : autoNumber;
    this.subscription.add(
      this.siteService.getAllSiteList(dataSourceId, event, number).subscribe({
        next: (res: any) => {
          this.automationSiteName[_index] = res;
          setTimeout(() => {
            this.siteLoader[_index] = false;
          }, 0);
        },
        error: e => {
          this.siteLoader[_index] = false;
          this.loading = false;
        }
      })
    );
  }

  siteSelect(e, _index: number) {
    this.site.siteAutomationDetails.automationSiteDetail[_index].siteNumber = e.apiSiteId;
    this.siteDetail.siteAutomationDetails.automationSiteDetail[_index].siteNumber = e.apiSiteId;
  }

  // get All Portfolio Access
  getCustomerAccess() {
    this.subscription.add(
      this.customerService.getAllCustomer().subscribe({
        next: res => {
          setTimeout(() => {
            if (!this.isEdit) {
              const cusList = res.filter(p => p.isActive);
              this.customerData = cusList;
            } else {
              this.customerData = res;
            }
          }, 0);
          if (this.site.customerId) {
            this.onCustomerSelect();
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  // Image Pop Up
  imagePopup(index, type: SiteImageTypes) {
    const getImagesForModelView = type => {
      switch (type) {
        case this.siteImageTypes.SITE_LAYOUT_IMAGE:
          return this.site.siteImages;
        case this.siteImageTypes.ZONE_MAP_IMAGE:
          return this.site.siteZoneImages;
        default:
          return [];
      }
    };

    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-xl img-view-section',
      initialState: {
        imageList: getImagesForModelView(type),
        selectedImageIndex: index
      }
    };
    this.modalRef = this.modalService.show(ModelComponent, ngModalOptions);
  }

  goBack() {
    if (this.sitePhotoLibraryTab) {
      this.router.navigate(['entities/sites']);
    } else {
      // this._location.back();
      this.router.navigateByUrl(`${APP_ROUTES.SITES}`);
    }
  }

  selectSiteImageArrayByType(type: SiteImageTypes): File[] {
    switch (type) {
      case this.siteImageTypes.MASTER_IMAGE:
        return this.masterImg;
      case this.siteImageTypes.SITE_LAYOUT_IMAGE:
        return this.siteLayoutImg;
      case this.siteImageTypes.ZONE_MAP_IMAGE:
        return this.zoneMapImg;
    }
  }

  deleteFile(index: number, type: SiteImageTypes) {
    const targetArray = this.selectSiteImageArrayByType(type);
    const [removedFile] = targetArray.splice(index, 1);

    if (removedFile) {
      this.files = this.files.filter(file => file.name !== removedFile.name);
    }
  }

  addDataSource() {
    const newDataSource = new AutomationSiteDetail();
    if (!this.site.siteAutomationDetails.automationSiteDetail.length) {
      newDataSource.isPrimarySite = true;
    }
    this.site.siteAutomationDetails.automationSiteDetail.push(newDataSource);
  }

  onDelete(id: any, siteDeviceCount: number, index: number) {
    const obj = {
      automationSiteDetailId: id,
      isActive: false
    };
    if (siteDeviceCount === 0 || !siteDeviceCount) {
      if (id) {
        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          initialState: {
            message: 'Are you sure want to delete this data source?'
          }
        };
        this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
        this.modalRef.content.onClose.subscribe(res => {
          if (res) {
            this.subscription.add(
              this.siteService.deleteDataSource(obj).subscribe({
                next: res => {
                  this.alertService.showSuccessToast('data source deleted');
                  this.site.siteAutomationDetails.automationSiteDetail.splice(index, 1);
                  this.loading = false;
                },
                error: _e => {
                  this.loading = false;
                }
              })
            );
          }
        });
      } else {
        this.site.siteAutomationDetails.automationSiteDetail.splice(index, 1);
      }
    } else {
      this.alertService.showWarningToast('Unmapped all devices from data source then delete.');
    }
  }

  changeTab(event) {
    this.activeTabTitle = event.tabTitle;
    if (event.tabTitle === this.siteAddEditScreenTabsName[this.siteAddEditScreenTabsEnum.AUTOMATION_SITE_DEVICE_LIST]) {
      this.loadAutomationTable = true;
    } else {
      this.loadAutomationTable = false;
    }
  }

  getErrorNumber(form, tabId) {
    if (form.submitted && form.form.status === 'INVALID') {
      return document.querySelectorAll(`#${tabId} .input-error`).length;
    }
    return null;
  }

  getActiveTab(tab: string) {
    return this.activeTab === tab;
  }

  navigateToReportGallery(workorderId) {
    this.router.navigate(['/entities/reports/sitevisits/image-gallery/' + workorderId], {
      queryParams: { isSiteLayout: true, siteId: this.id }
    });
  }

  gotoAutomationDevice(automationSiteId) {
    this.router.navigate(['entities/site-device/list'], {
      queryParams: { automationSiteId }
    });
  }

  getValueExpectedValue(data: ProductionExpectation, month: number) {
    for (const i of data.siteProdBreak) {
      if (i.month === month) {
        return i.expProduction;
      }
    }
  }

  getColor(data: ProductionExpectation, month: number) {
    for (const i of data.siteProdBreak) {
      if (i.expProduction && i.month === month) {
        return i.yearDifferenceColor;
      }
    }
  }

  openImageGallery(isPmImages: boolean, images, ticketNumber) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: { images: images, isPmImages: isPmImages, ticketNumber }
    };
    this.modalRef = this.modalService.show(ImageGalleryModelComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result && result.imageId) {
        if (result.isPmImage) {
          this.copyPMSitePhotoToMaster(result.imageId);
        } else {
          this.copyCMSitePhotoToMaster(result.imageId);
        }
      }
    });
  }

  generatePerformanceRecord(isClick = true) {
    if (
      this.site.siteAutomationDetails.automationSiteDetail.length &&
      this.site.siteAutomationDetails.automationSitePerformanceDetails.startYear &&
      this.site.siteAutomationDetails.automationSitePerformanceDetails.startMonth &&
      this.site.siteAutomationDetails.automationSitePerformanceDetails.moduleDegradation &&
      (isClick ||
        this.site.siteAutomationDetails.automationSitePerformanceDetails.startYear !==
          this.siteDetail.siteAutomationDetails.automationSitePerformanceDetails.startYear ||
        this.site.siteAutomationDetails.automationSitePerformanceDetails.startMonth !==
          this.siteDetail.siteAutomationDetails.automationSitePerformanceDetails.startMonth ||
        this.site.siteAutomationDetails.automationSitePerformanceDetails.moduleDegradation !==
          this.siteDetail.siteAutomationDetails.automationSitePerformanceDetails.moduleDegradation ||
        this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryWeatherSensorDevice !==
          this.siteDetail.siteAutomationDetails.automationSitePerformanceDetails.primaryWeatherSensorDevice)
    ) {
      this.productionLoading = true;
      const data = {
        qeSiteId: this.site.id,
        startYear: this.site.siteAutomationDetails.automationSitePerformanceDetails.startYear,
        startMonth: this.site.siteAutomationDetails.automationSitePerformanceDetails.startMonth,
        moduleDegradation: this.site.siteAutomationDetails.automationSitePerformanceDetails.moduleDegradation,
        performanceData: this.site.siteAutomationDetails.listOfAutomationSitePerformanceModelTable,
        primaryWeatherSensorDevice: this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryWeatherSensorDevice
      };
      this.subscription.add(
        this.siteService.generatePerformanceRecord(data).subscribe({
          next: res => {
            if (isClick) {
              this.getSite(this.site.id, false);
              this.siteDetail.siteAutomationDetails.automationSitePerformanceDetails.startYear = JSON.parse(
                JSON.stringify(this.site.siteAutomationDetails.automationSitePerformanceDetails.startYear)
              );
              this.siteDetail.siteAutomationDetails.automationSitePerformanceDetails.startMonth = JSON.parse(
                JSON.stringify(this.site.siteAutomationDetails.automationSitePerformanceDetails.startMonth)
              );
              this.siteDetail.siteAutomationDetails.automationSitePerformanceDetails.moduleDegradation = JSON.parse(
                JSON.stringify(this.site.siteAutomationDetails.automationSitePerformanceDetails.moduleDegradation)
              );
              this.siteDetail.siteAutomationDetails.automationSitePerformanceDetails.primaryWeatherSensorDevice = JSON.parse(
                JSON.stringify(this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryWeatherSensorDevice)
              );
            }
            this.performanceRecordUpdated = true;
            this.productionLoading = false;
          },
          error: e => {
            this.productionLoading = false;
          }
        })
      );
    }
  }

  activeToggleChange(event) {
    if (event === true && !this.currentIsActiveStatusInBE) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-full-dialog',

        initialState: {
          siteId: this.site.id,
          archiveModalFrom: 'sites'
        }
      };
      this.modalRef = this.modalService.show(CustomerSiteInfoArchiveComponent, ngModalOptions);
    }
  }

  onDeleteSite(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to delete this site?' }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.loading = true;

          this.subscription.add(
            this.siteService.deleteSite(event).subscribe({
              next: res => {
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  this.profileService.setDefaultFilterSelection();
                  this.loading = false;
                  this.goBack();
                }
              },
              error: () => (this.loading = false)
            })
          );
        }
      });
    }
  }
  onChangeLiftRequired(event) {
    event.target.checked ? this.site.siteAccess.liftDetails : (this.site.siteAccess.liftDetails = '');
  }
  onChangeLadderRequired(event) {
    event.target.checked
      ? (this.site.siteAccess.ladderDetails = this.ladderDetailesArray[0].value)
      : (this.site.siteAccess.ladderDetails = '');
  }
  onChangeNoticeRequired(event) {
    event.target.checked
      ? (this.site.siteAccess.noticeDetails = this.noticeDetailsArray[0].value)
      : (this.site.siteAccess.noticeDetails = '');
  }

  getTimeZones() {
    this.subscription.add(
      this.userService.getAllTimeZone().subscribe({
        next: (res: TimeZone[]) => {
          this.timeZoneList = res;
        }
      })
    );
  }

  getPhotoGalleryUploadedFiles(files, type: SiteImageTypes, fileInput: HTMLInputElement) {
    if (files.length) {
      for (const i of files) {
        this.masterImg.push(i);
      }
      if (this.site.id) {
        this.loading = true;
        if (type === this.siteImageTypes.MASTER_IMAGE) {
          this.createSiteLayoutAndPhotoGalleryFiles(this.site.id);
        }
      }
      fileInput.value = '';
    }
  }

  createSiteLayoutAndPhotoGalleryFiles(id, msg?: string, isSiteCreate?: boolean): void {
    this.loading = true;
    const tempArray = [];

    if (this.masterImg.length) {
      for (const i of this.masterImg) {
        const formData: FormData = new FormData();
        formData.append('file', i as File);
        formData.append('siteId', id.toString());
        tempArray.push(this.siteService.getSitePhotoGalleryUplodedImage(formData));
      }
    }

    if (this.siteLayoutImg.length) {
      for (const i of this.siteLayoutImg) {
        const formData: FormData = new FormData();
        formData.append('Image', i as File);
        formData.append('SiteId', id.toString());
        formData.append('siteImageType', SiteImageTypeAPIEnum.SITE_LAYOUT_IMAGE.toString());
        tempArray.push(this.siteService.getSiteUplodedImage(formData));
      }
    }

    if (this.zoneMapImg.length) {
      for (const i of this.zoneMapImg) {
        const formData: FormData = new FormData();
        formData.append('Image', i as File);
        formData.append('SiteId', id.toString());
        formData.append('siteImageType', SiteImageTypeAPIEnum.ZONE_MAP_IMAGE.toString());
        tempArray.push(this.siteService.getSiteUplodedImage(formData));
      }
    }

    this.subscription.add(
      forkJoin(tempArray).subscribe({
        next: res => {
          if (this.site.id) {
            if (isSiteCreate) {
              // once the site is created update the url and navigate the user to that specific site.
              this.router.navigateByUrl(`${APP_ROUTES.SITES}/edit/${this.site.id}`);
              this.alertService.showSuccessToast(msg);
            } else {
              this.getSite(id, true);
              this.alertService.showSuccessToast('Files uploaded.');
            }
          } else {
            this.alertService.showSuccessToast(msg);
            // this.router.navigateByUrl(APP_ROUTES.SITES);
          }
          this.files = [];
          this.masterImg = [];
          this.siteLayoutImg = [];
          this.zoneMapImg = [];
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  photoGalleryUploadFiles(id, msg?: string) {
    this.loading = true;
    const tempArray = [];
    for (const i of this.masterImg) {
      const formData: FormData = new FormData();
      formData.append('file', i as File);
      formData.append('siteId', id.toString());
      tempArray.push(this.siteService.getSitePhotoGalleryUplodedImage(formData));
    }
    this.subscription.add(
      forkJoin(tempArray).subscribe({
        next: res => {
          if (this.site.id) {
            this.alertService.showSuccessToast('Files uploaded.');
            this.getSitePhotoImages(id);
          } else {
            this.alertService.showSuccessToast(msg);
            // this.router.navigateByUrl(APP_ROUTES.SITES);
          }
          this.files = [];
          this.masterImg = [];
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  downloadSitePhotoImage(imageUrl: string) {
    fetch(imageUrl)
      .then(response => response.blob())
      .then(blob => {
        const link = this.commonService.createObject(blob, '');
        link.download = 'sitePhoto.jpg';
        link.click();
      })
      .catch(error => console.error('Error downloading image:', error));
  }

  deletePhotoGalleryImages(id) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: ErrorMessages.deleteMessage
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.subscription.add(
      this.modalRef.content.onClose.subscribe(
        result => {
          if (result) {
            this.loading = true;
            this.subscription.add(
              this.siteService.deletePhotoGalleryImage(id).subscribe({
                next: res => {
                  this.loading = false;
                  if (res) {
                    this.alertService.showSuccessToast(res.message);
                    this.getSitePhotoImages(this.id);
                  }
                },
                error: e => {
                  this.loading = false;
                }
              })
            );
          }
        },
        error => {
          this.loading = false;
        }
      )
    );
  }

  // Function to determine if an image item is the selected key photo
  isKeyPhotoSelected(item) {
    return item.keyPhotosForSiteinfo;
  }

  onKeyPhotoChange(imageId: number): void {
    const data = {
      imageId: imageId
    };
    for (let key in this.photoGalleryImage) {
      if (this.photoGalleryImage.hasOwnProperty(key)) {
        const value = this.photoGalleryImage[key];
        if (value.id !== imageId) {
          value.keyPhotosForSiteinfo = false;
        } else {
          value.keyPhotosForSiteinfo = true;
        }
      }
    }
    this.subscription.add(
      this.siteService.selectKeyPhoto(data).subscribe({
        next: (res: any) => {
          this.loading = false;
          if (res) {
            this.site.siteInfoImageURL = this.getImageUrlById(imageId);
            this.alertService.showSuccessToast('Site image set Successfully.');
            this.getSitePhotoImages(this.id);
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }
  getSiteHistoryData() {
    this.exclusionsLoading = true;
    this.subscription.add(
      this.siteService.getSiteHistoryData(Number(this.id)).subscribe({
        next: (res: SiteAuditHistory[]) => {
          if (res) {
            this.siteAuditHistory = res;
            this.exclusionsLoading = false;
          }
        },
        error: e => {
          this.exclusionsLoading = false;
        }
      })
    );
  }

  getImageUrlById(imageId: number) {
    const item = this.photoGalleryImage.find(item => item.id === imageId);
    return item ? item.imageUrl : '';
  }

  selectAllPhotoGalleryImages(): void {
    this.photoGalleryImage.forEach(image => {
      image.isSelected = true;
    });
  }

  unselectAllPhotoGalleryImages(): void {
    this.photoGalleryImage.forEach(image => {
      image.isSelected = false;
    });
  }

  downloadAllAsZipFolder(): void {
    this.selectedImages = this.photoGalleryImage.filter(image => image.isSelected);
    if (!this.selectedImages.length) {
      this.alertService.showWarningToast('Please select images to download folder.');
      return;
    }
    const zip = new JSZip();
    const filePromises: Promise<void>[] = [];
    for (let i = 0; i < this.selectedImages.length; i++) {
      const image = this.selectedImages[i];
      filePromises.push(
        fetch(image.imageUrl)
          .then(response => response.blob())
          .then(blob => {
            const fileName = `siteImage${i + 1}.jpg`;
            zip.file(fileName, blob);
          })
      );
    }
    Promise.all(filePromises)
      .then(() => {
        return zip.generateAsync({ type: 'blob' });
      })
      .then(content => {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(content);
        link.download = 'sitePhotos.zip';
        link.click();
      })
      .catch(error => {
        console.error(error);
      });
  }

  ClearPMFilter() {
    this.pmFilterModel = new PMFilter();
    this.getFilteredPmReports();
  }

  getFilteredPmReports() {
    this.sitePhotoLoading = true;
    const pmModal = {
      ...this.pmFilterModel,
      siteId: Number(this.id)
    };
    this.subscription.add(
      this.siteService.getPMSitePhotoImages(pmModal).subscribe({
        next: (res: SitePhotoPMTableModal[]) => {
          if (res) {
            this.sitePhotoPMTableData = res;
            this.sitePhotoLoading = false;
          }
        },
        error: e => {
          this.sitePhotoLoading = false;
        }
      })
    );
  }

  ClearCMFilter() {
    this.CMfilterModel = new CMFilter();
    this.getFilteredCmReports();
  }

  getFilteredCmReports() {
    this.sitePhotoLoading = true;
    const cmModal = {
      ...this.CMfilterModel,
      siteId: Number(this.id)
    };
    this.subscription.add(
      this.siteService.getCMSitePhotoImages(cmModal).subscribe({
        next: (res: SitePhotoCMTableModal[]) => {
          if (res) {
            this.sitePhotoCMTableData = res;
            this.sitePhotoLoading = false;
          }
        },
        error: e => {
          this.sitePhotoLoading = false;
        }
      })
    );
  }

  copyPMSitePhotoToMaster(imageId: number) {
    const copyModal = {
      fileId: imageId,
      siteId: this.site.id
    };
    this.subscription.add(
      this.siteService.copyPMPhotoToMaster(copyModal).subscribe({
        next: (res: any) => {
          this.alertService.showSuccessToast('PM Photo is Copied Successfully in Site Photo');
          this.getSitePhotoImages(this.site.id);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  copyCMSitePhotoToMaster(imageId: number) {
    const copyModal = {
      id: imageId,
      siteId: this.site.id
    };
    this.subscription.add(
      this.siteService.copyCMPhotoToMaster(copyModal).subscribe({
        next: (res: any) => {
          this.alertService.showSuccessToast('CM Photo is Copied Successfully in Site Photo.');
          this.getSitePhotoImages(this.site.id);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  selectAndDeselectAll(isSelect, filteredListType: string) {
    if (filteredListType === 'filteredSiteTypeIds') {
      if (isSelect) {
        if (this[filteredListType].length) {
          this.site.siteLayoutDetail.siteType = [
            ...new Set([...this.site.siteLayoutDetail.siteType, ...JSON.parse(JSON.stringify(this[filteredListType]))])
          ];
        } else {
          this.site.siteLayoutDetail.siteType = this.arrayTypeList.map(element => element.id);
        }
      } else {
        if (this[filteredListType].length) {
          this.site.siteLayoutDetail.siteType = this.site.siteLayoutDetail.siteType.filter(x => !this[filteredListType].includes(x));
        } else {
          this.site.siteLayoutDetail.siteType = [];
        }
      }
    } else if (filteredListType === 'filteredInverterTypeIds') {
      if (isSelect) {
        if (this[filteredListType].length) {
          this.site.siteLayoutDetail.inverterTypes = [
            ...new Set([...this.site.siteLayoutDetail.inverterTypes, ...JSON.parse(JSON.stringify(this[filteredListType]))])
          ];
        } else {
          this.site.siteLayoutDetail.inverterTypes = this.inverterTypes.map(element => element.id);
        }
      } else {
        if (this[filteredListType].length) {
          this.site.siteLayoutDetail.inverterTypes = this.site.siteLayoutDetail.inverterTypes.filter(
            x => !this[filteredListType].includes(x)
          );
        } else {
          this.site.siteLayoutDetail.inverterTypes = [];
        }
      }
    } else if (filteredListType === 'filteredAssessmentTypeIds') {
      if (isSelect) {
        if (this[filteredListType].length) {
          this.pmFilterModel.assessmentType = [
            ...new Set([...this.pmFilterModel.assessmentType, ...JSON.parse(JSON.stringify(this[filteredListType]))])
          ];
        } else {
          this.pmFilterModel.assessmentType = this.reportTypeData.map(element => element.id);
        }
      } else {
        if (this[filteredListType].length) {
          this.pmFilterModel.assessmentType = this.pmFilterModel.assessmentType.filter(x => !this[filteredListType].includes(x));
        } else {
          this.pmFilterModel.assessmentType = [];
        }
      }
    } else if (filteredListType === 'filteredPriorityIds') {
      if (isSelect) {
        if (this[filteredListType].length) {
          this.CMfilterModel.priority = [
            ...new Set([...this.CMfilterModel.priority, ...JSON.parse(JSON.stringify(this[filteredListType]))])
          ];
        } else {
          this.CMfilterModel.priority = this.ticketPriority.map(element => element.id);
        }
      } else {
        if (this[filteredListType].length) {
          this.CMfilterModel.priority = this.CMfilterModel.priority.filter(x => !this[filteredListType].includes(x));
        } else {
          this.CMfilterModel.priority = [];
        }
      }
    } else if (filteredListType === 'filteredPrimaryMeterDeviceIds') {
      if (isSelect) {
        if (this[filteredListType].length) {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryMeterDevice = [
            ...new Set([
              ...this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryMeterDevice,
              ...JSON.parse(JSON.stringify(this[filteredListType]))
            ])
          ];
        } else {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryMeterDevice = this.deviceMeter.map(element => element.id);
        }
      } else {
        if (this[filteredListType].length) {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryMeterDevice =
            this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryMeterDevice.filter(
              x => !this[filteredListType].includes(x)
            );
        } else {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.primaryMeterDevice = [];
        }
      }
    } else if (filteredListType === 'filteredAggregateMeterDeviceIds') {
      if (isSelect) {
        if (this[filteredListType].length) {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.aggregateMeterDevice = [
            ...new Set([
              ...this.site.siteAutomationDetails.automationSitePerformanceDetails.aggregateMeterDevice,
              ...JSON.parse(JSON.stringify(this[filteredListType]))
            ])
          ];
        } else {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.aggregateMeterDevice = this.deviceMeter.map(
            element => element.id
          );
        }
      } else {
        if (this[filteredListType].length) {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.aggregateMeterDevice =
            this.site.siteAutomationDetails.automationSitePerformanceDetails.aggregateMeterDevice.filter(
              x => !this[filteredListType].includes(x)
            );
        } else {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.aggregateMeterDevice = [];
        }
      }
    } else if (filteredListType === 'filteredSubMeterDeviceIds') {
      if (isSelect) {
        if (this[filteredListType].length) {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.subMeterDevice = [
            ...new Set([
              ...this.site.siteAutomationDetails.automationSitePerformanceDetails.subMeterDevice,
              ...JSON.parse(JSON.stringify(this[filteredListType]))
            ])
          ];
        } else {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.subMeterDevice = this.deviceMeter.map(element => element.id);
        }
      } else {
        if (this[filteredListType].length) {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.subMeterDevice =
            this.site.siteAutomationDetails.automationSitePerformanceDetails.subMeterDevice.filter(
              x => !this[filteredListType].includes(x)
            );
        } else {
          this.site.siteAutomationDetails.automationSitePerformanceDetails.subMeterDevice = [];
        }
      }
    }
  }

  onDropdownSearchFilter(event, filteredListType: string) {
    if (event.term) {
      this[filteredListType] = event.items?.map(element => element.id);
    } else {
      this[filteredListType] = [];
    }
  }

  getCusomerContracts() {
    this.loading = true;
    this.subscription.add(
      this.contractService.getContractListByCustomer(this.site.customerId).subscribe({
        next: res => {
          this.customerContracts = res;
          if (this.customerContracts.length)
            this.customerContracts.push({
              abbreviation: null,
              customerId: null,
              id: 0,
              isActive: null,
              isArchive: null,
              isAutomationSite: null,
              name: 'No Contract',
              portfolioId: null,
              siteNumber: null,
              endDate: null
            });
          this.loading = false;
          this.selectedContract = this.customerContracts.filter(contract => contract.id === this.site.contractId)[0];
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  //Below code is for Site Files dropbox 2890 jira ticket

  getFilesAttachmentsList(requestParams = null) {
    this.attachmentsLoading = true;
    const getListingParams = {
      siteId: Number(this.id),
      customerId: this.site.customerId,
      portfolioId: this.site.portfolioId,
      entityId: Number(this.id),
      entityNumber: '',
      parentId: null,
      fileType: 'document',
      imagePreviewId: 0,
      isCustomerFacing: false,
      moduleType: 9,
      page: this.filterModel.page,
      sortBy: this.filterModel.sortBy,
      direction: this.filterModel.direction,
      itemsCount: this.filterModel.items,
      search: this.fileSearchText
    };
    this.subscription.add(
      this.dropBoxService.getGalleryImageFiles(getListingParams).subscribe({
        next: (res: AttachmentListResponse) => {
          let selectedForPreviewCount = 0;
          this.isSelectedAllFiles = false;
          const updatedFileGallery = res.fileGallery.map(file => {
            const isSelected = this.allSelectedFiles.some(selectedFile => selectedFile.id === file.id);
            if (isSelected) {
              selectedForPreviewCount++;
            }
            return {
              ...file,
              isSelectedForPreview: isSelected
            };
          });

          this.fileAttachments = {
            totalCount: res.totalCount,
            fileGallery: [...updatedFileGallery]
          };
          if (this.checkAuthorisationsFn([this.roleType.CUSTOMER])) {
            this.fileAttachments.fileGallery.forEach(file => {
              file.fileTagTxt = file.fileTagTxt.filter(tag => tag !== 'Customer Facing');
            });
          }
          this.isSelectedAllFiles = this.fileAttachments.fileGallery.length === selectedForPreviewCount;
          this.cdr.detectChanges();
          this.attachmentsLoading = false;
        },
        error: err => {
          this.attachmentsLoading = false;
        }
      })
    );
  }

  fileSearchChanged() {
    this.fileSearchModelChanged.next(null);
  }

  sortFiles(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.filterModel.items = this.filesPaginationParams.pageSize;
    this.filterModel.page = this.filesPaginationParams.currentPage - 1;
    const params = {
      sortBy: this.filterModel.sortBy,
      direction: this.filterModel.direction,
      itemsCount: this.filterModel.items,
      page: this.filterModel.page
    };
    this.getFilesAttachmentsList(params);
  }

  openFileUploadSidePanel(mode, fileItem) {
    const entityDetails = {
      customerId: this.siteDetail.customerId,
      portfolioId: this.siteDetail.portfolioId,
      siteId: Number(this.id),
      entityId: Number(this.id),
      entityNumber: '',
      moduleType: 9
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        isFileEditMode: mode,
        fileItemObj: fileItem,
        entityDetails: entityDetails,
        parentModuleName: 'Site',
        isParentCreateMode: this.isCreate ? true : false
      }
    };
    this.modalRef = this.modalService.show(CommonDropboxFileUploadComponent, ngModalOptions);
    if (this.isCreate) {
      this.modalRef.content.fileUploadList.subscribe(res => {
        if (res && res.length) {
          this.createFileUploadList.push(...res);
        }
      });
    } else {
      this.modalRef.content.isParentRefresh.subscribe(res => {
        if (res) {
          this.filesPaginationParams.currentPage = 1;
          this.getFilesAttachmentsList();
        }
      });
    }
  }

  downloadDropBoxFile(fileId, fileName) {
    this.loading = true;
    this.dropBoxService.downloadPreviewedImage(fileId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, data.type);
          link.download = fileName;
          link.click();
          this.loading = false;
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  deleteDropBoxFile(fileId, isCreateMode = false) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (isCreateMode) {
          this.createFileUploadList = this.createFileUploadList.filter(item => item.id !== fileId);
        } else {
          this.subscription.add(
            this.dropBoxService.deleteImageGalleryFiles(fileId).subscribe({
              next: data => {
                this.alertService.showSuccessToast(`file deleted Successfully.`);
                this.filesPaginationParams.currentPage = 1;
                this.getFilesAttachmentsList();
                this.loading = false;
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      }
    });
  }

  onPageChange(obj) {
    this.filesPaginationParams.currentPage = obj;
    this.filterModel.items = this.filesPaginationParams.itemsCount;
    this.filterModel.page = obj - 1;
    this.getFilesAttachmentsList();
  }

  onChangeSize() {
    this.filesPaginationParams.itemsCount = Number(this.filesPaginationParams.pageSize);
    this.filterModel.page = this.filesPaginationParams.currentPage = 0;
    this.filterModel.items = this.filesPaginationParams.itemsCount;
    this.getFilesAttachmentsList();
  }

  // Upload General Images
  getUploadedFiles(files, type: SiteImageTypes, fileInput: HTMLInputElement) {
    if (files.length) {
      for (const i of files) {
        switch (type) {
          case this.siteImageTypes.SITE_LAYOUT_IMAGE:
            this.siteLayoutImg.push(i);
            break;
          case this.siteImageTypes.ZONE_MAP_IMAGE:
            this.zoneMapImg.push(i);
            break;
        }
      }
      fileInput.value = '';
      if (this.site.id) {
        this.loading = true;
        switch (type) {
          case this.siteImageTypes.SITE_LAYOUT_IMAGE:
            this.createSiteLayoutAndPhotoGalleryFiles(this.site.id);
            break;
          case this.siteImageTypes.ZONE_MAP_IMAGE:
            this.createSiteLayoutAndPhotoGalleryFiles(this.site.id);
            break;
        }
      }
    }
  }

  // Image delete in General Images section
  deleteSiteImages(siteImageId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: ErrorMessages.deleteMessage
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);

    this.subscription.add(
      this.modalRef.content.onClose.subscribe(
        result => {
          if (result) {
            this.loading = true;
            this.subscription.add(
              this.siteService.deleteSiteImage(siteImageId).subscribe({
                next: res => {
                  this.loading = false;
                  if (res) {
                    this.alertService.showSuccessToast(res.message);
                    this.getSite(this.id, true);
                  }
                },
                error: e => {
                  this.loading = false;
                }
              })
            );
          }
        },
        error => {
          this.loading = false;
        }
      )
    );
  }

  onCheckboxClick(val: boolean, key: string, subKey: string) {
    if (val) {
      this.checkboxStates[key][subKey] = false;
    } else {
      switch (key) {
        case 'oilFilled':
          const value = this.site?.siteLayoutDetail?.xfmr ? this.site.siteLayoutDetail.xfmr.toString() : '';
          if (value.length) {
            this.checkboxStates[key][subKey] = true;
          }
          break;
        case 'bess':
          const bessValue = this.site?.siteLayoutDetail?.bessxfmr ? this.site.siteLayoutDetail.bessxfmr.toString() : '';
          if (bessValue.length) {
            this.checkboxStates[key][subKey] = true;
          }
          break;
        case 'dry':
          const dryValue = this.site?.siteLayoutDetail?.dryXFMR ? this.site.siteLayoutDetail.dryXFMR.toString() : '';
          if (dryValue.length) {
            this.checkboxStates[key][subKey] = true;
          }
          break;
        default:
          break;
      }
    }
  }

  onInputChange(event: InputEvent, key: string) {
    if (!(event.target as HTMLInputElement).value || Number((event.target as HTMLInputElement).value) <= 0) {
      this.checkboxStates[key].utility = false;
      this.checkboxStates[key].customer = false;
    } else if ((event.target as HTMLInputElement).value && !this.checkboxStates[key].utility && !this.checkboxStates[key].customer) {
      this.checkboxStates[key].utility = true;
    }
  }

  isCheckboxDisable(val: string | number): boolean {
    return !val || Number(val) <= 0;
  }

  openEditTagsModal(template: TemplateRef<any>) {
    if (this.allSelectedFiles) {
      this.getFilesTagList();
      this.selectedFilesNamesString = this.allSelectedFiles.map(item => item.fileName).join(', ');
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-md'
      };
      setTimeout(() => {
        this.addRemoveFilesTagsModalRef = this.modalService.show(template, ngModalOptions);
      }, 0);
    } else {
      this.alertService.showWarningToast('Please select at least one file.');
    }
  }

  onFilter(event: any) {
    if (event.term) {
      this.filteredAppliedTags = event.items?.map(element => element.id);
    } else {
      this.filteredAppliedTags = [];
    }
  }

  toggleSelectUnselectAllTags(isSelect = false) {
    if (isSelect) {
      if (!this.filteredAppliedTags.length) {
        this.fileTagIds = this.filesTagList.map(site => site.id);
      } else {
        if (!Array.isArray(this.fileTagIds)) {
          this.fileTagIds = [];
        }
        this.fileTagIds = [...new Set([...this.fileTagIds, ...JSON.parse(JSON.stringify(this.filteredAppliedTags))])];
      }
    } else {
      if (this.filteredAppliedTags.length) {
        this.fileTagIds = this.fileTagIds.filter(x => !this.filteredAppliedTags.includes(x));
      } else {
        this.fileTagIds = [];
      }
    }
  }

  getFilesTagList() {
    this.loading = true;
    this.dropBoxService.getFileTagList().subscribe({
      next: data => {
        if (data) {
          this.filesTagList = data;
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  reorderTags() {
    const selectedTags = this.filesTagList.filter(tag => this.fileTagIds.includes(tag.id));
    const unselectedTags = this.filesTagList.filter(tag => !this.fileTagIds.includes(tag.id));
    this.filesTagList = [...selectedTags, ...unselectedTags];
  }

  selectAllFiles() {
    if (this.isSelectedAllFiles) {
      // If selecting all files
      for (const element of this.fileAttachments.fileGallery) {
        element.isSelectedForPreview = true; // Mark as selected
        if (!this.allSelectedFiles.some(file => file.id === element.id)) {
          this.allSelectedFiles.push(element);
        }
      }
    } else {
      // If deselecting all files
      for (const element of this.fileAttachments.fileGallery) {
        element.isSelectedForPreview = false; // Mark as deselected
        // Remove from allSelectedFiles if it exists
        const index = this.allSelectedFiles.findIndex(file => file.id === element.id);
        if (index !== -1) {
          this.allSelectedFiles.splice(index, 1);
        }
      }
    }
  }

  singleFilesCheckChanged(files) {
    if (files.isSelectedForPreview) {
      // Add the file only if it's not already in allSelectedFiles
      if (!this.allSelectedFiles.includes(files)) {
        this.allSelectedFiles.push(files);
      }
    } else {
      // If the file is not selected for preview, remove it from allSelectedFiles
      const index = this.allSelectedFiles.findIndex(file => file.id === files.id);
      if (index !== -1) {
        this.allSelectedFiles.splice(index, 1);
      }
    }

    this.isSelectedAllFiles = this.fileAttachments.fileGallery.every(file => this.allSelectedFiles.includes(file));
  }

  addRemoveMultipleFilesTags(isApplyTags: boolean) {
    const filesIds: number[] = this.allSelectedFiles.filter(item => item.isSelectedForPreview === true).map(item => item.id);
    const multipleImagesTags = {
      filesIds: filesIds,
      fileTagIds: this.fileTagIds,
      isApplyTags: isApplyTags
    };
    if (filesIds && filesIds.length && this.fileTagIds && this.fileTagIds.length) {
      this.addRemoveTags(multipleImagesTags);
    } else {
      this.alertService.showWarningToast(`Please select at least one file tag.`);
    }
  }

  addRemoveTags(filesTagsParams) {
    this.loading = true;
    this.subscription.add(
      this.dropBoxService.applyTagsToFiles(filesTagsParams).subscribe({
        next: res => {
          this.addRemoveFilesTagsModalRef.hide();
          this.loading = false;
          this.allSelectedFiles = [];
          this.fileTagIds = [];
          this.isSelectedAllFiles = false;
          this.filesPaginationParams.currentPage = 1;
          this.alertService.showSuccessToast(`Tags updated successfully.`);
          this.getFilesAttachmentsList();
        },
        error: err => {
          this.loading = false;
          this.isSelectedAllFiles = false;
          this.allSelectedFiles = [];
        }
      })
    );
  }

  onContractChange(selectedContract) {
    this.selectedContract = this.customerContracts.filter(contract => contract.id === selectedContract.id)[0];
  }

  ngOnDestroy(): void {
    this.sharedCPSDataService.resetSharedCPSData();
  }
}
