import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { ShowerrorComponent } from '../../../@shared/components/showerror/showerror.component';
import { AppConstants } from '../../../@shared/constants';
import { ASSESSMENTFILTERLIST, AssessmentDTO, AssessmentFilterData } from '../../../@shared/models/assessment.model';
import { AlertService, SharedCPSDataService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppliedFilter } from '../../site-device/site-device.model';
import { AssessmentService } from '../assessment.service';

@Component({
  selector: 'sfl-assessment-listing',
  templateUrl: './assessment-listing.component.html',
  styleUrls: ['./assessment-listing.component.scss']
})
export class AssessmentListingComponent implements OnInit, OnDestroy {
  assessments: AssessmentDTO[] = [];
  subscription: Subscription = new Subscription();
  loading = false;
  modalRef: BsModalRef;
  currentPage = 1;
  pageSize = AppConstants.rowsPerPage;
  total: number;
  disabled = false;
  viewPage = FILTER_PAGE_NAME.PM_SCOPE_LISTING;
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  filterList = ASSESSMENTFILTERLIST;
  viewFilterSection = 'assessmentFilterSection';
  sortOptionList = {
    SiteName: 'asc',
    State: 'asc',
    CustomerPortfolio: 'asc'
  };
  filterDetails: FilterDetails = new FilterDetails();
  @ViewChild('file') fileInput;
  file: File;

  constructor(
    private readonly assessmentService: AssessmentService,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly sharedCPSDataService: SharedCPSDataService
  ) {}

  ngOnInit() {
    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection),
      sharedFilter = this.storageService.get(AppConstants.SHARED_FILTER_KEY),
      jumpToMenuFilterData = this.sharedCPSDataService.getSharedCPSDataForCommonFilterWithResetData();

    if (Object.keys(jumpToMenuFilterData).length) {
      this.filterModel.customerIds = jumpToMenuFilterData?.customerIds ?? [];
      this.filterModel.portfolioIds = jumpToMenuFilterData?.portfolioIds ?? [];
      this.storageService.set(this.viewPage, this.filterModel);
    } else if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.filterModel.sortBy = 'SiteName';
    this.filterModel.itemsCount = this.pageSize;
    this.isFilterDisplay = filterSection ? filterSection : false;

    if (this.filterModel.direction && this.filterModel.sortBy) {
      this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
    }
    if (this.filterModel.page) {
      this.currentPage = this.filterModel.page + 1;
    }
    if (this.filterModel.itemsCount) {
      this.pageSize = this.filterModel.itemsCount;
    }

    if (!this.filterModel.year) {
      this.filterModel.year = this.commonService.getCurrentYear();
    }
    this.initFilterDetails();
    this.filterModel.customerIds = (sharedFilter?.customerIds?.length ? sharedFilter.customerIds : this.filterModel.customerIds) || [];
    this.filterModel.portfolioIds = (sharedFilter?.portfolioIds?.length ? sharedFilter.portfolioIds : this.filterModel.portfolioIds) || [];
    this.filterModel.siteIds = [];
    const scopeAssessmentsPageFilterKeys = ['customerIds', 'portfolioIds'];
    if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, sharedFilter, scopeAssessmentsPageFilterKeys)) {
      this.getAllAssessmentList();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.START_YEAR.show = true;
    this.filterDetails.default_sort = 'SiteName';
    this.filterDetails.filter_item = filterItem;
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllAssessmentList();
  }

  // sorting
  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllAssessmentList();
  }

  getAllAssessmentList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.assessmentService.getAllAssessmentsByfilter(this.filterModel).subscribe({
        next: (data: AssessmentFilterData) => {
          this.assementList(data);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  assementList(data: AssessmentFilterData) {
    this.assessments = data.assesments;
    this.total = data.totalAssesment;
    this.loading = false;
  }

  // Delete Assessment
  onDelete(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete this assessment?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.subscription.add(
            this.assessmentService.deleteAssessment(event).subscribe({
              next: res => {
                if (res) {
                  if (this.currentPage !== 0 && this.assessments.length === 1) {
                    this.onChangeSize();
                    this.alertService.showSuccessToast(res.message);
                  } else {
                    this.getAllAssessmentList();
                    this.alertService.showSuccessToast(res.message);
                  }
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      });
    }
  }

  onChangeSize() {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllAssessmentList();
  }

  text(valAbb, val) {
    if (valAbb !== 'NA' && valAbb !== 'Alltime') {
      return valAbb;
    } else {
      return val;
    }
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    this.getAllAssessmentList(true, filterParams);
  }

  exportData() {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    model.itemsCount = this.total;
    model.page = 0;
    model.isExport = true;
    this.subscription.add(
      this.assessmentService.getAllAssessmentsByfilter(model).subscribe({
        next: (data: AssessmentFilterData) => {
          this.exportToCSV(data.assesments);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  exportToCSV(data: AssessmentDTO[]) {
    const title = 'Scope';
    const rows: any = [
      [
        'Customer',
        'Portfolio',
        'Site Name',
        'StartYear',
        'Site Address',
        'State',
        'City',
        'ZipCode',
        'Latitude',
        'Longitude',
        'Array Type',
        'Inv Type',
        'AC Size',
        'DC Size',
        '# of INV',
        '# of CB',
        '# of Panelboards',
        'Transformers',
        'SV',
        'IPM',
        'MVPM',
        'TPM',
        'THERMAL',
        'AERIAL',
        'VOC',
        'IV',
        'VGT',
        'PR',
        'TRQ',
        'MVTH',
        'MNT'
      ]
    ];
    for (const i of data) {
      const tempData = [
        i.customerName,
        i.portfolioName,
        i.siteName,
        i.startYear,
        i.address,
        i.state,
        i.city,
        `="${i.zipCode}"`,
        i.latitude,
        i.logitude,
        i.siteTypeStr,
        i.invType ? i.invType : '',
        i.acSize,
        i.dcSize,
        i.invNumber,
        i.cbPanelboards ? i.cbPanelboards : '',
        i.panelboards ? i.panelboards : '',
        i.xfmr,
        i.siteVisit,
        i.inverterPM,
        i.mvpm,
        i.tpm,
        i.thermal,
        i.aerialScan,
        i.electricalVOC,
        i.electricalIV,
        i.vegetation,
        i.performanceReporting,
        i.trq,
        i.mvth,
        `="${i.monitoing}"`
      ];
      rows.push(tempData);
    }
    this.commonService.exportExcel(rows, title);
    this.loading = false;
  }

  selectFile() {
    this.fileInput.nativeElement.click();
  }

  importData(files) {
    if (files.length > 0) {
      this.loading = true;
      const formData: FormData = new FormData();
      formData.append('uploadCSV', files[0] as File);
      this.subscription.add(
        this.assessmentService.uploadFile(formData).subscribe({
          next: (res: any) => {
            if (res.status === 200) {
              this.alertService.showSuccessToast(res.message);
            }
            if (res.status === 201) {
              this.showErrorOnImport(res);
            }
            this.fileInput.nativeElement.value = '';
            this.getAllAssessmentList();
          },
          error: (e: any) => {
            this.loading = false;
            this.alertService.showWarningToast(e.message);
          }
        })
      );
    }
  }

  showErrorOnImport(message) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: message
      }
    };
    this.modalRef = this.modalService.show(ShowerrorComponent, ngModalOptions);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
      this.subscription.unsubscribe();
    }
  }
}
