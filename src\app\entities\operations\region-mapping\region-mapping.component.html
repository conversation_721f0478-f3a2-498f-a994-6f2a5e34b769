<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-body class="dropdownOverlap">
    <nb-tabset (changeTab)="selectedTab = $event.tabTitle; getUsersAndRegionDropdownList()" fullWidth>
      <nb-tab [tabTitle]="tableItem.accordionHeader" *ngFor="let tableItem of tableDetails; index as i" class="row">
        <div class="col-12 mb-2 d-flex justify-content-end">
          <button
            nbButton
            status="primary"
            size="small"
            type="button"
            class="me-4"
            (click)="addUpdateRecord('Add', tableItem.accordionHeader); $event.stopPropagation()"
            [nbTooltip]="'Add ' + tableItem.accordionHeader"
            nbTooltipPlacement="top"
            nbTooltipStatus="primary"
            *ngIf="(i > 0 ? tableDetails[i - 1].data.length : true) && selectedTab !== 'County'"
          >
            Add
          </button>
        </div>
        <div class="col-12 mb-2">
          <sfl-filter
            [filterDetails]="getFilterDetails(tableItem.accordionHeader)"
            (refreshList)="refreshList($event)"
            (refreshTableHeight)="
              tableItem.accordionHeader === 'Region'
                ? (isRegionFilterDisplay = $event)
                : tableItem.accordionHeader === 'Subregion'
                ? (isSubRegionFilterDisplay = $event)
                : (isCountyFilterDisplay = $event)
            "
          ></sfl-filter>
        </div>
        <div
          id="fixed-table"
          setTableHeight
          [isFilterDisplay]="
            tableItem.accordionHeader === 'Region'
              ? isRegionFilterDisplay
              : tableItem.accordionHeader === 'Subregion'
              ? isSubRegionFilterDisplay
              : isCountyFilterDisplay
          "
          class="col-12 table-responsive table-card-view"
        >
          <table class="table table-hover table-bordered" aria-describedby="Customer List">
            <thead>
              <tr>
                <ng-container *ngFor="let col of tableItem.columns">
                  <th (click)="sort(col.field, tableItem.sortOptionList[col.field])" [id]="col.field" *ngIf="col.field !== 'action'">
                    <div class="d-flex align-items-center">
                      {{ col.header }}
                      <span
                        class="fa text-center cursor-pointer ms-auto"
                        [ngClass]="{
                          'fa-arrow-up': tableItem.sortOptionList[col.field] === 'desc',
                          'fa-arrow-down': tableItem.sortOptionList[col.field] === 'asc',
                          'icon-selected':
                            tableItem.accordionHeader === 'Region'
                              ? regionFilterModel.sortBy === col.field
                              : tableItem.accordionHeader === 'Subregion'
                              ? subRegionFilterModel.sortBy === col.field
                              : countyFilterModel.sortBy === col.field
                        }"
                      ></span>
                    </div>
                  </th>
                  <th *ngIf="col.field === 'action'" id="action" class="text-center">Action</th>
                </ng-container>
              </tr>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let tableData of tableItem.data
                    | paginate
                      : {
                          id: tableItem.accordionHeader,
                          itemsPerPage: (tableItem.accordionHeader === 'Region'
                            ? regionFilterModel
                            : tableItem.accordionHeader === 'Subregion'
                            ? subRegionFilterModel
                            : countyFilterModel
                          ).itemsCount,
                          currentPage: tableItem.currentPage,
                          totalItems: tableItem.totalCount
                        };
                  trackBy: trackByFunction
                "
              >
                <td
                  *ngFor="let col of tableItem.columns"
                  [data-title]="tableData[col.field]"
                  [ngClass]="{ 'text-center': col.field === 'action', 'text-end': col.field === 'siteCount' }"
                >
                  <span *ngIf="col.field !== 'action'">
                    {{ tableData[col.field] }}
                  </span>
                  <span *ngIf="col.field === 'action'">
                    <em
                      class="fa fa-pencil text-primary cursor-pointer me-3"
                      nbTooltip="Edit"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      (click)="addUpdateRecord('Update', tableItem.accordionHeader, tableData.id)"
                    ></em>
                    <em
                      class="fa fa-trash cursor-pointer text-danger"
                      nbTooltip="Delete"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="danger"
                      aria-hidden="true"
                      (click)="onDeleteRecord(tableData.id)"
                      *ngIf="tableItem.accordionHeader !== 'County'"
                    ></em>
                  </span>
                </td>
              </tr>
              <tr *ngIf="!tableItem.data?.length">
                <td colspan="6" class="text-center">No {{ tableItem.accordionHeader }} found.</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="mt-2 d-md-flex align-items-center" *ngIf="tableItem.data?.length">
          <div class="d-flex align-items-center">
            <label class="mb-0">Items per page: </label>
            <ng-select class="ms-2" [(ngModel)]="tableItem.pageSize" [clearable]="false" (change)="onChangeSize()" appendTo="body">
              <ng-option value="5">5</ng-option>
              <ng-option value="10">10</ng-option>
              <ng-option value="50">50</ng-option>
              <ng-option value="100">100</ng-option>
            </ng-select>
          </div>
          <strong class="ms-md-3"> Total {{ tableItem.accordionHeader }}: {{ tableItem.totalCount }} </strong>
          <div class="ms-md-auto ms-sm-0">
            <pagination-controls
              [id]="tableItem.accordionHeader"
              (pageChange)="onPageChange($event)"
              class="paginate"
            ></pagination-controls>
          </div>
        </div>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
</nb-card>
