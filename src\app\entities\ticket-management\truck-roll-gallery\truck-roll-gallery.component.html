<nb-card class="reports" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header class="d-flex align-items-center">
    <h6 class="w-100">{{ truckRollData?.truckRollNumber }}</h6>
    <div class="d-flex align-items-center w-100">
      <div class="ms-auto d-flex button_list">
        <button
          nbButton
          type="button"
          status="primary"
          size="small"
          id="DropBoxImageGallery"
          [disabled]="!truckRollData?.galleryTotalCount || loading"
          (click)="openDropBoxImageGallery()"
        >
          <span class="d-flex"><em class="pi pi-images me-2"></em> ({{ truckRollData?.galleryTotalCount || 0 }})</span>
        </button>
        <button nbButton status="primary" size="medium" type="button" [disabled]="loading" class="ms-1 ms-sm-2" (click)="exportPdf()">
          <span class="d-none d-lg-inline-block"> <em class="fa fa-file-download download_icon me-2"></em>Export to PDF</span>
          <i class="d-inline-block d-lg-none fa fa-file-download download_icon"></i>
        </button>
        <button nbButton type="button" status="basic" [disabled]="loading" size="medium" class="ms-1 ms-sm-2" (click)="goBack()">
          <span class="d-none d-lg-inline-block">Back</span>
          <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="truck-roll-gallery-container">
      <div class="row mb-4">
        <div class="col-md-4 col-12 d-flex flex-column">
          <ul class="truck-roll-ul">
            <li class="basic-info-li">
              <div class="row">
                <div class="col-6">Customer</div>
                <div class="col-6 text-center">
                  <a
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT]"
                    [routerLink]="['/entities/customers/detail/' + truckRollData?.customerId]"
                  >
                    {{ truckRollData?.customerName }}</a
                  >
                  <span *ngIf="!checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT])">{{
                    truckRollData?.customerName
                  }}</span>
                </div>
              </div>
            </li>
            <li class="basic-info-li">
              <div class="row">
                <div class="col-6">Portfolio</div>
                <div class="col-6 text-center">{{ truckRollData?.portfolioName }}</div>
              </div>
            </li>
            <li class="basic-info-li">
              <div class="row">
                <div class="col-6">Site</div>
                <div class="col-6 text-center">{{ truckRollData?.siteName }}</div>
              </div>
            </li>
            <li class="basic-info-li">
              <div class="row">
                <div class="col-6">Dispatch Date</div>
                <div class="col-6 text-center">{{ truckRollData?.dispatchDate | date : fullDateFormat }}</div>
              </div>
            </li>
            <li class="basic-info-li">
              <div class="row">
                <div class="col-6">Truck Roll Count</div>
                <div class="col-6 text-center">{{ truckRollData?.truckRollCount }}</div>
              </div>
            </li>
            <li class="basic-info-li" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
              <div class="row">
                <div class="col-6">Hours</div>
                <div *ngIf="!loading" class="col-6 text-center">{{ truckRollData?.totalHours || 0 }} hours</div>
              </div>
            </li>
            <li class="basic-info-li" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
              <div class="row">
                <div class="col-6">Materials</div>
                <div class="col-6 text-center">{{ truckRollData?.totalMaterialCost | currency }}</div>
              </div>
            </li>
          </ul>
        </div>
        <div class="col-md-8 col-12">
          <div
            class="truck-roll-gallery overflow-auto border border-secondary rounded p-3 mx-auto w-100 d-flex gap-3 text-center flex-wrap"
          >
            <ng-container select="[imageFound]" *ngIf="truckRollData?.imageGalleryList?.length; else noImageFound">
              <ng-container *ngFor="let image of truckRollData?.imageGalleryList; let i = index; trackBy: trackByFn">
                <div class="img-container">
                  <div class="cursor-pointer rounded position-relative w-100 h-100" (click)="imagePopup(i)">
                    <img
                      [src]="image.thumbnailUrl ? image.thumbnailUrl : image.fileUrl"
                      onError="this.src='assets/images/no-image-found.jpg'"
                      class="w-100 rounded"
                      [alt]="'img-' + (i + 1)"
                      loading="lazy"
                    />
                    <div class="img-footer px-2 mx-auto text-center">
                      <div class="image-ticket" *ngIf="image?.entityNumber">{{ image?.entityNumber }}</div>
                      <div class="image-date text-nowrap" *ngIf="image?.dateTaken">
                        {{ image?.dateTaken | date : dateFormat }}
                      </div>
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
            <ng-template #noImageFound><span class="text-center">No Images Found</span> </ng-template>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="truckRollData?.ticketList?.length">
        <div class="table-responsive table-card-view">
          <table class="table truck-roll-ticket-table">
            <thead>
              <tr>
                <th scope="col">Ticket #</th>
                <th scope="col">Status</th>
                <th scope="col">Resolved</th>
                <th *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])" scope="col">Tech(s)</th>
                <th *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])" scope="col">Hours</th>
                <th *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])" scope="col">Materials</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let ticket of truckRollData?.ticketList">
                <tr>
                  <td data-title="Ticket #">
                    <a class="custom-link" (click)="redirectToTicket(ticket?.ticketNumber)"> {{ ticket?.ticketNumber }}</a>
                  </td>
                  <td data-title="Status">{{ ticket?.statusStr }}</td>
                  <td data-title="Resolved">{{ ticket?.isResolve ? 'Yes' : 'No' }}</td>
                  <td *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])" data-title="Tech(s)">
                    {{ ticket?.fieldTechs || '-' }}
                  </td>
                  <td *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])" data-title="Hours">{{ ticket?.hours }}</td>
                  <td *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])" data-title="Materials">{{ ticket?.materialCost | currency }}</td>
                </tr>
              </ng-container>
              <tr *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])" class="empty-tr d-none d-md-table-row">
                <td data-title="Ticket #"></td>
                <td data-title="Status"></td>
                <td data-title="Resolved"></td>
                <td data-title="Tech(s)"></td>
                <td data-title="Hours"></td>
                <td data-title="Materials"></td>
              </tr>
              <tr *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                <td>Totals</td>
                <td></td>
                <td></td>
                <td></td>
                <td>{{ truckRollData?.totalHours }}</td>
                <td>{{ truckRollData?.totalMaterialCost | currency }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
