import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AlertService } from '../../../../@shared/services';
import { DashboardData } from '../operation-reports.model';
import { OperationsReportsService } from '../operations-reports.service';
import { CustomDashboardAddEditModalComponent } from './custom-dashboard-add-edit-modal/custom-dashboard-add-edit-modal.component';

@Component({
  selector: 'sfl-custom-dashboard',
  templateUrl: './custom-dashboard.component.html',
  styleUrls: ['./custom-dashboard.component.scss']
})
export class CustomDashboardComponent implements OnInit, OnDestroy {
  loading = false;
  dashboardId: number;
  modalRef: BsModalRef;
  selectedDashboard: DashboardData;

  constructor(
    private readonly router: Router,
    public readonly sanitizer: DomSanitizer,
    private readonly activatedRoute: ActivatedRoute,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService,
    private readonly operationsReportsService: OperationsReportsService
  ) {}

  ngOnInit() {
    this.activatedRoute.params.subscribe(params => {
      this.dashboardId = params['id'];
      this.getCustomDashboardById();
    });
  }

  getAllCustomDashboards() {
    this.operationsReportsService.getCustomDashboards().subscribe({
      next: response => {
        this.operationsReportsService.dashboardList = response;
        this.router.navigate(['/entities/operations/operations-reports/pm-completion-report']);
      }
    });
  }

  getCustomDashboardById() {
    this.loading = true;
    this.operationsReportsService.getCustomDashboardById(this.dashboardId).subscribe({
      next: response => {
        this.operationsReportsService.selectedDashboard = response;

        this.selectedDashboard = {
          id: response.id,
          menuTitle: response.menuTitle,
          content: this.sanitizer.bypassSecurityTrustHtml(response.content as any)
        };

        this.loading = false;
      },
      error: () => (this.loading = false)
    });
  }

  addUpdateDashboard(operationType: 'Add' | 'Update') {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        operationType,
        selectedDashboardData: this.operationsReportsService.selectedDashboard
      }
    };

    this.modalRef = this.modalService.show(CustomDashboardAddEditModalComponent, ngModalOptions);

    this.modalRef.content.onClose.subscribe({
      next: result => {
        if (result) {
          this.getCustomDashboardById();
          this.operationsReportsService.$refreshDashboardListingOnUpdate.next(true);
        }
      }
    });
  }

  onDelete(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete this dashboard?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe({
        next: result => {
          if (result) {
            this.operationsReportsService.deleteCustomDashboard(event).subscribe({
              next: response => {
                this.alertService.showSuccessToast(response.message);
                this.getAllCustomDashboards();
                this.closeModal();
              },
              error: () => this.closeModal()
            });
          }
        },
        error: () => (this.loading = false)
      });
    }
  }

  closeModal() {
    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.operationsReportsService.selectedDashboard = null;
  }
}
