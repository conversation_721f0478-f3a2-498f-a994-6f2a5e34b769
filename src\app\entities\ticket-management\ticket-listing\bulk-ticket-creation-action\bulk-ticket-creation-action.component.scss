.error-message-section {
  height: 20px;
}

.modal-full-view-dialog {
  margin: 0px !important;
  min-width: 100vw !important;
  min-height: 100vh !important;
  max-width: 100% !important;
  .modal-content {
    padding: 10px !important;
    min-height: 100vh !important;
    min-width: 100vw !important;
  }
}

nb-card-body {
  overflow: unset !important;
}

.close {
  border: none;
  background-color: transparent;
  color: #fff;
}

nb-card-header.border-bottom-0 {
  border-bottom: 0px !important;
  padding-bottom: 0px !important;
}

::ng-deep .siteDeviceSpinner nb-spinner {
  align-items: flex-start !important;
  padding: 8% !important;
}

::ng-deep nb-card-header {
  padding: 0.75rem 1.25rem !important;
}

.multi-column-list {
  columns: 10;
  column-gap: 20px;

  li {
    line-height: 1.5rem;
  }
}
