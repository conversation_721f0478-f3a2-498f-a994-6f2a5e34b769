<button hidden type="button" #securityQuesAnsButton (click)="openModal(securityQuesAnsTemplate)"></button>
<button hidden type="button" #changePasswordButton (click)="openModal(changePasswordTemplate)"></button>

<ng-template #securityQuesAnsTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Security Questions</h4>
  </div>
  <div [nbSpinner]="loading" nbSpinnerStatus="primary">
    <form
      name="securityQuesAnsForm"
      #securityQuesAnsForm="ngForm"
      aria-labelledby="title"
      (ngSubmit)="securityQuesAnsForm.valid && saveSecurityQuesAnsList(securityQuesAnsForm)"
      autocomplete="off"
    >
      <div class="modal-body">
        <ng-container *ngIf="userSecurityQuestionAnswer && userSecurityQuestionAnswer.length">
          <ng-container *ngFor="let quesAnsItem of userSecurityQuestionAnswer; let index = index">
            <div class="form-group row">
              <label for="inputEmail" class="label col-sm-3 col-form-label text-right">Question {{ index + 1 }}</label>
              <div class="col-sm-9">
                <div class="mb-2">
                  <div class="d-flex align-items-center">
                    <ng-select
                      name="question{{ index + 1 }}"
                      id="question{{ index + 1 }}"
                      [items]="securityQuestionList"
                      bindLabel="question"
                      bindValue="questionID"
                      #question="ngModel"
                      [(ngModel)]="quesAnsItem.questionID"
                      notFoundText="No Question Found"
                      placeholder="Select Question"
                      appendTo=""
                      [style]="
                        !isSecurityQuesAnsRequired(quesAnsItem) && quesAnsItem.questionID ? 'width: calc(100% - 48px);' : 'width: 100%;'
                      "
                      required
                      [clearable]="false"
                      (ngModelChange)="onSecurityQuestionChange(quesAnsItem, index)"
                      (open)="setSecurityQuestionList(index, userSecurityQuestionAnswer)"
                      (close)="setSecurityQuestionList(index, userSecurityQuestionAnswer)"
                    >
                    </ng-select>
                    <ng-container *ngIf="!isSecurityQuesAnsRequired(quesAnsItem) && quesAnsItem.questionID">
                      <div class="ms-2">
                        <button
                          nbButton
                          fullWidth
                          status="basic"
                          outline
                          [nbTooltip]="quesAnsItem.needToResetAnswer ? 'No Change Needed' : 'Change Answer'"
                          nbTooltipPlacement="right"
                          nbTooltipStatus="primary"
                          status="primary"
                          size="medium"
                          type="button"
                          [disabled]="isSecurityQuesAnsRequired(quesAnsItem)"
                          (click)="quesAnsItem.needToResetAnswer = !quesAnsItem.needToResetAnswer; quesAnsItem.questionAnswerStr = ''"
                        >
                          <nb-icon [icon]="quesAnsItem.needToResetAnswer ? 'slash-outline' : 'edit-outline'"></nb-icon>
                        </button>
                      </div>
                    </ng-container>
                  </div>
                  <ng-container *ngIf="question.invalid && (question.dirty || question.touched)">
                    <p class="caption status-danger mb-0" *ngIf="question.errors?.required">Question is required</p>
                  </ng-container>
                </div>
                <ng-container *ngIf="isSecurityQuesAnsRequired(quesAnsItem) || quesAnsItem.needToResetAnswer">
                  <div class="mb-2">
                    <div class="d-flex align-items-center">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="quesAnsItem.questionAnswerStr"
                        #questionAnswer="ngModel"
                        name="questionAnswer{{ index + 1 }}"
                        id="questionAnswer{{ index + 1 }}"
                        class="form-control w-100"
                        [type]="quesAnsItem.hideInputValues ? 'password' : 'text'"
                        class="form-control"
                        placeholder="Enter Answer"
                        pattern="^(?!\s*$).+"
                        [status]="questionAnswer.dirty ? (questionAnswer.invalid ? 'danger' : 'success') : 'basic'"
                        [disabled]="!quesAnsItem.questionID"
                        [required]="isSecurityQuesAnsRequired(quesAnsItem) || quesAnsItem.needToResetAnswer"
                        autocomplete="new-password"
                        [attr.aria-invalid]="questionAnswer.invalid && questionAnswer.touched ? true : null"
                      />
                      <div class="ms-2">
                        <button
                          nbButton
                          fullWidth
                          status="basic"
                          [nbTooltip]="quesAnsItem.hideInputValues ? 'View Answer' : 'Hide Answer'"
                          nbTooltipPlacement="right"
                          nbTooltipStatus="primary"
                          outline
                          status="primary"
                          size="medium"
                          type="button"
                          [disabled]="!quesAnsItem.questionID"
                          (click)="quesAnsItem.hideInputValues = !quesAnsItem.hideInputValues"
                        >
                          <nb-icon [icon]="quesAnsItem.hideInputValues ? 'eye-outline' : 'eye-off-2-outline'"></nb-icon>
                        </button>
                      </div>
                    </div>
                    <ng-container *ngIf="questionAnswer.invalid && (questionAnswer.dirty || questionAnswer.touched)">
                      <p class="caption status-danger mb-0" *ngIf="questionAnswer.errors?.required">Answer is required</p>
                      <p class="caption status-danger mb-0" *ngIf="questionAnswer.errors?.pattern">White spaces are not allowed</p>
                    </ng-container>
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary" #securityQuesAnsSaveButton [disabled]="securityQuesAnsForm.invalid">Save</button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #changePasswordTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Change Password</h4>
  </div>
  <div [nbSpinner]="loading" nbSpinnerStatus="primary">
    <form
      name="changePassForm"
      #changePassForm="ngForm"
      aria-labelledby="title"
      (ngSubmit)="changePassForm.valid && changePWD(changePassForm)"
      autocomplete="off"
    >
      <div class="modal-body">
        <div class="form-group row">
          <label for="input-currentPassword" class="label mb-0 col-form-label"
            >Current Password<span class="ms-1 text-danger">*</span></label
          >
          <div class="mb-2">
            <input
              nbInput
              fullWidth
              [(ngModel)]="changePWDObj.currentPassword"
              #currentPassword="ngModel"
              name="currentPassword"
              type="password"
              id="input-currentPassword"
              class="form-control"
              [status]="currentPassword.dirty ? (currentPassword.invalid ? 'danger' : 'success') : 'basic'"
              required
              [attr.aria-invalid]="currentPassword.invalid && currentPassword.touched ? true : null"
            />
            <ng-container *ngIf="currentPassword.invalid && currentPassword.touched">
              <p class="caption status-danger mb-0" *ngIf="currentPassword.errors?.required">Current Password is required</p>
            </ng-container>
          </div>
        </div>
        <div class="form-group row">
          <label for="input-newPassword" class="label mb-0 col-form-label">New Password<span class="ms-1 text-danger">*</span></label>
          <div class="mb-2">
            <input
              nbInput
              fullWidth
              [(ngModel)]="changePWDObj.newPassword"
              #newPassword="ngModel"
              name="newPassword"
              type="password"
              id="input-newPassword"
              class="form-control"
              [status]="newPassword.dirty ? (newPassword.invalid ? 'danger' : 'success') : 'basic'"
              required
              [attr.aria-invalid]="newPassword.invalid && newPassword.touched ? true : null"
            />
            <ng-container *ngIf="newPassword.invalid && newPassword.touched">
              <p class="caption status-danger mb-0" *ngIf="newPassword.errors?.required">Password is required</p>
            </ng-container>
          </div>
        </div>
        <div class="form-group row">
          <label for="input-confirmpassword" class="label mb-0 col-form-label"
            >Confirm Password<span class="ms-1 text-danger">*</span></label
          >
          <div class="mb-2">
            <input
              nbInput
              fullWidth
              name="confirmpassword"
              type="password"
              id="input-confirmpassword"
              [ngModel]="changePWDObj.confirmPassword"
              #confirmPassword="ngModel"
              class="form-control"
              [status]="confirmPassword.dirty ? (confirmPassword.invalid ? 'danger' : 'success') : 'basic'"
              required
              [attr.aria-invalid]="confirmPassword.invalid && confirmPassword.touched ? true : null"
            />
            <ng-container *ngIf="confirmPassword.touched">
              <p class="caption status-danger mb-0" *ngIf="confirmPassword.invalid && confirmPassword.errors?.required">
                Confirm Password is required!
              </p>
              <p
                class="caption status-danger mb-0"
                *ngIf="newPassword.value !== confirmPassword.value && !confirmPassword.errors?.required"
              >
                Password does not match the confirm password.
              </p>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary" #changePasswordSaveButton [disabled]="changePassForm.invalid">Save</button>
      </div>
    </form>
  </div>
</ng-template>
