import { QE_MENU_MODULE_ENUM } from '../../../@shared/enums/qe-menu.enum';
import { QE_MENU_INTERACTION_EVENT_TYPE_ENUM, QEAnalyticsPasswordOperationEnum } from './qe-analytics.enum';

export interface QEAnalyticsRes {
  qeAnalyticsTableRes: QEAnalyticsTableRes[];
}

export interface HeatMapColor {
  hexBGColor: string;
  colorName: string;
  hexTextColor: string;
}

export interface ProcessedTableResCategories extends QEAnalyticsTableRes {
  processedTableResItems: QEAnalyticsTableRes[];
}
export interface QEAnalyticsTableRes {
  menuId: number;
  menuName: string;
  menuClickedPercentage: number;
  childrens: QEAnalyticsTableRes[];
  processedTableResItems?: QEAnalyticsTableRes[];
  heatMapColor?: HeatMapColor;
  isFilterApplied?: boolean;
}

export class QEAnalyticsPasswordObj {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  pwdOperation: QEAnalyticsPasswordOperationEnum;

  constructor(pwdOperation: QEAnalyticsPasswordOperationEnum = QEAnalyticsPasswordOperationEnum.PASSWORD) {
    this.pwdOperation = pwdOperation;
  }
}

export class QEMenuModuleType {
  public menuId: number;
  public menuName: string;
  public parentName: string;
  public parentId: number;
  public menuLevelOrder: number;
  public menuUniqueId: QE_MENU_MODULE_ENUM;
  public menuParentId: QE_MENU_MODULE_ENUM;
  public frontEndMenuDisplayOrder: number;
}

export class QEMenuInteractionEventObj extends QEMenuModuleType {
  public interactionEventName: string;
  public interactionEventType: QE_MENU_INTERACTION_EVENT_TYPE_ENUM;
  public interactionEventTime: string;
  public interactionStartTime?: string;
  public interactionEndTime?: string;
  public randomUniqueKey: string;
}

// to be reference for QE Analytics Module List
export const qeMenuModuleList = [
  {
    menuId: 1,
    menuName: 'Dashboard',
    parentName: 'Site Info',
    parentId: 1,
    menuLevelOrder: 1
  },
  {
    menuId: 2,
    menuName: 'Customers',
    parentName: 'Site Info',
    parentId: 1,
    menuLevelOrder: 1
  },
  {
    menuId: 3,
    menuName: 'Portfolios',
    parentName: 'Site Info',
    parentId: 1,
    menuLevelOrder: 1
  },
  {
    menuId: 4,
    menuName: 'Sites',
    parentName: 'Site Info',
    parentId: 1,
    menuLevelOrder: 1
  },
  {
    menuId: 5,
    menuName: 'Devices',
    parentName: 'Site Info',
    parentId: 1,
    menuLevelOrder: 1
  },
  {
    menuId: 6,
    menuName: 'Equipment',
    parentName: 'Site Info',
    parentId: 1,
    menuLevelOrder: 1
  },
  {
    menuId: 7,
    menuName: 'Dashboard',
    parentName: 'PM',
    parentId: 2,
    menuLevelOrder: 1
  },
  {
    menuId: 8,
    menuName: 'Scope',
    parentName: 'PM',
    parentId: 2,
    menuLevelOrder: 1
  },
  {
    menuId: 9,
    menuName: 'Work Orders',
    parentName: 'PM',
    parentId: 2,
    menuLevelOrder: 1
  },
  {
    menuId: 10,
    menuName: 'Reports',
    parentName: 'PM',
    parentId: 2,
    menuLevelOrder: 1
  },
  {
    menuId: 11,
    menuName: 'Site Audit',
    parentName: 'PM',
    parentId: 2,
    menuLevelOrder: 1
  },
  {
    menuId: 12,
    menuName: 'Non-Conformance',
    parentName: 'PM',
    parentId: 2,
    menuLevelOrder: 1
  },
  {
    menuId: 13,
    menuName: 'Dashboard',
    parentName: 'CM',
    parentId: 3,
    menuLevelOrder: 1
  },
  {
    menuId: 14,
    menuName: 'All Tickets',
    parentName: 'CM',
    parentId: 3,
    menuLevelOrder: 1
  },
  {
    menuId: 15,
    menuName: 'Ticket Audit Report',
    parentName: 'CM',
    parentId: 3,
    menuLevelOrder: 1
  },
  {
    menuId: 16,
    menuName: 'Exclusion Report',
    parentName: 'CM',
    parentId: 3,
    menuLevelOrder: 1
  },
  {
    menuId: 17,
    menuName: 'Billing Report',
    parentName: 'CM',
    parentId: 3,
    menuLevelOrder: 1
  },
  {
    menuId: 18,
    menuName: 'Truck Roll Report',
    parentName: 'CM',
    parentId: 3,
    menuLevelOrder: 1
  },
  {
    menuId: 19,
    menuName: 'Map Report',
    parentName: 'CM',
    parentId: 3,
    menuLevelOrder: 1
  },
  {
    menuId: 20,
    menuName: 'RMA Report',
    parentName: 'CM',
    parentId: 3,
    menuLevelOrder: 1
  },
  {
    menuId: 21,
    menuName: 'Dashboard',
    parentName: 'Availability',
    parentId: 4,
    menuLevelOrder: 1
  },
  {
    menuId: 22,
    menuName: 'Reports',
    parentName: 'Availability',
    parentId: 4,
    menuLevelOrder: 1
  },
  {
    menuId: 23,
    menuName: 'Data Table',
    parentName: 'Availability',
    parentId: 4,
    menuLevelOrder: 1
  },
  {
    menuId: 24,
    menuName: 'Exclusions',
    parentName: 'Availability',
    parentId: 4,
    menuLevelOrder: 1
  },
  {
    menuId: 25,
    menuName: 'Dashboard',
    parentName: 'Performance',
    parentId: 5,
    menuLevelOrder: 1
  },
  {
    menuId: 26,
    menuName: 'Power Cards',
    parentName: 'Performance',
    parentId: 5,
    menuLevelOrder: 1
  },
  {
    menuId: 27,
    menuName: 'Power Charts',
    parentName: 'Performance',
    parentId: 5,
    menuLevelOrder: 1
  },
  {
    menuId: 28,
    menuName: 'Reports',
    parentName: 'Performance',
    parentId: 5,
    menuLevelOrder: 1
  },
  {
    menuId: 29,
    menuName: 'Data Table',
    parentName: 'Performance',
    parentId: 5,
    menuLevelOrder: 1
  },
  {
    menuId: 30,
    menuName: 'Alerts',
    parentName: 'Performance',
    parentId: 5,
    menuLevelOrder: 1
  },
  {
    menuId: 31,
    menuName: 'JHA',
    parentName: 'Safety',
    parentId: 6,
    menuLevelOrder: 1
  },
  {
    menuId: 32,
    menuName: 'Site Check-In',
    parentName: 'Safety',
    parentId: 6,
    menuLevelOrder: 1
  },
  {
    menuId: 33,
    menuName: 'Site Audit-JHA',
    parentName: 'Safety',
    parentId: 6,
    menuLevelOrder: 1
  },
  {
    menuId: 34,
    menuName: 'General Info',
    parentName: 'Settings',
    parentId: 7,
    menuLevelOrder: 2
  },
  {
    menuId: 35,
    menuName: 'Work Type',
    parentName: 'Settings',
    parentId: 7,
    menuLevelOrder: 2
  },
  {
    menuId: 36,
    menuName: 'Work Step',
    parentName: 'Settings',
    parentId: 7,
    menuLevelOrder: 2
  },
  {
    menuId: 37,
    menuName: 'Hazard',
    parentName: 'Settings',
    parentId: 7,
    menuLevelOrder: 2
  },
  {
    menuId: 38,
    menuName: 'Barrier',
    parentName: 'Settings',
    parentId: 7,
    menuLevelOrder: 2
  },
  {
    menuId: 39,
    menuName: 'LOTO',
    parentName: 'Settings',
    parentId: 7,
    menuLevelOrder: 2
  },
  {
    menuId: 40,
    menuName: 'Reports',
    parentName: 'Operations',
    parentId: 8,
    menuLevelOrder: 1
  },
  {
    menuId: 41,
    menuName: 'Region Mapping',
    parentName: 'Operations',
    parentId: 8,
    menuLevelOrder: 1
  },
  {
    menuId: 42,
    menuName: 'Services',
    parentName: 'Operations',
    parentId: 8,
    menuLevelOrder: 1
  },
  {
    menuId: 43,
    menuName: 'Contracts',
    parentName: 'Operations',
    parentId: 8,
    menuLevelOrder: 1
  },
  {
    menuId: 44,
    menuName: 'Custom Forms',
    parentName: 'Operations',
    parentId: 8,
    menuLevelOrder: 1
  },
  {
    menuId: 45,
    menuName: 'Users',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 46,
    menuName: 'Data Source Mapping',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 47,
    menuName: 'API Error Log',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 48,
    menuName: 'Report Scheduler',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 49,
    menuName: 'Customer API Gateway',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 50,
    menuName: 'API Gateway Dashboard',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 51,
    menuName: 'Re-Fetch Scheduler',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 52,
    menuName: 'Email Log',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 53,
    menuName: 'Analytics',
    parentName: 'Admin',
    parentId: 9,
    menuLevelOrder: 1
  },
  {
    menuId: 54,
    menuName: 'Site Check-In',
    parentName: 'Others',
    parentId: 10,
    menuLevelOrder: 1
  },
  {
    menuId: 55,
    menuName: 'User Profile',
    parentName: 'Others',
    parentId: 10,
    menuLevelOrder: 1
  },
  {
    menuId: 56,
    menuName: 'Change Password',
    parentName: 'Others',
    parentId: 10,
    menuLevelOrder: 1
  }
] as QEMenuModuleType[];
