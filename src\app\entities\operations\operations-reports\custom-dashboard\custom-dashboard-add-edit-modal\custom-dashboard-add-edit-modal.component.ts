import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { AlertService } from '../../../../../@shared/services';
import { DashboardData } from '../../operation-reports.model';
import { OperationsReportsService } from '../../operations-reports.service';

@Component({
  selector: 'sfl-custom-dashboard-add-edit-modal',
  templateUrl: './custom-dashboard-add-edit-modal.component.html',
  styleUrls: ['./custom-dashboard-add-edit-modal.component.scss']
})
export class CustomDashboardAddEditModalComponent implements OnInit, OnDestroy {
  @Input() selectedDashboardData: DashboardData;
  @Input() operationType: 'Add' | 'Update' = 'Add';

  loading = false;

  onClose = new Subject();

  dashboardForm: FormGroup = new FormGroup({
    id: new FormControl(''),
    menuTitle: new FormControl('', Validators.required),
    content: new FormControl('', Validators.required)
  });

  constructor(
    private readonly bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly operationsReportsService: OperationsReportsService
  ) {}

  ngOnInit() {
    if (this.operationType === 'Update') {
      this.dashboardForm.patchValue(this.selectedDashboardData);
    }
  }

  onSave() {
    if (this.dashboardForm.valid) {
      if (this.operationType === 'Add') {
        this.dashboardForm.get('id').setValue(0);
        this.operationsReportsService.createCustomDashboard(this.dashboardForm.value).subscribe({
          next: response => {
            this.alertService.showSuccessToast(response.message);
            this.closeModal(true);
          },
          error: () => this.closeModal()
        });

        this.closeModal();
      } else {
        this.operationsReportsService.updateCustomDashboard(this.dashboardForm.value).subscribe({
          next: response => {
            this.alertService.showSuccessToast(response.message);
            this.closeModal(true);
          },
          error: () => this.closeModal()
        });
      }
    }
  }

  closeModal(isChanged = false) {
    this.onClose.next(isChanged);
    this.bsModalRef.hide();
  }

  ngOnDestroy() {
    this.dashboardForm.reset();
  }
}
