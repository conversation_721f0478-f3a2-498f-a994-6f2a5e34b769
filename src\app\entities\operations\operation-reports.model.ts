export class OperationsReportsMenu {
  public label: string;
  public icon: string;
  public routerLink: string;
}
export class FilterModel {
  public customerIds: number[] = [];
  public portfolioIds: number[] = [];
  public siteIds: number[] = [];
  public reportTypeIds: number[] = [];
  public duration: string = 'Month';
}

export class AllReportDropdown {
  public ids: number[] = [];
  public customerIds?: number[] = [];
  public isActive = true;
}

export class ReportType {
  public abbreviation: string;
  public id: number;
  public isActive: boolean;
  public name: string;
  public item_id: number;
  public item_text: string;
}
