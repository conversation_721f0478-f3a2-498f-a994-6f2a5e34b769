import { DatePipe } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiUrl, AppConstants } from '../../../@shared/constants';
import {
  DeviceRecords,
  FilterModel,
  INVERTER_COLORS,
  PowerChartResponse,
  PowerChartSiteSunriseSunsetData
} from '../power-chart/power-chart.model';
import { PowerCardsResponse } from './power-cards-model';

@Injectable({
  providedIn: 'root'
})
export class PowerCardsService {
  chartViewList = [
    { displayName: 'Plot Irradiance', isSelected: true, name: 'Irradiance', disabled: false },
    { displayName: 'Plot Meters', isSelected: false, name: 'Meters', disabled: false },
    { displayName: 'Sum Meters', isSelected: true, name: 'Meter Sum', disabled: false },
    { displayName: 'Plot Inverters', isSelected: false, name: 'Inverters', disabled: false },
    { displayName: 'Sum Inverters', isSelected: true, name: 'Inverter Sum', disabled: false },
    { displayName: 'Sunrise/Sunset', isSelected: true, name: 'Sunrise/Sunset', disabled: false }
  ];

  constructor(private readonly http: HttpClient) {}

  viewPowerCardsData(data: any): Observable<PowerCardsResponse[]> {
    return this.http.post<PowerCardsResponse[]>(ApiUrl.GET_POWER_CARDS, data, {
      headers: new HttpHeaders({ timeout: `${1000 * 60 * 5}` })
    });
  }

  getSiteSunriseSunsetChartsInfo(data: FilterModel): Observable<PowerChartSiteSunriseSunsetData[]> {
    return this.http.post<PowerChartSiteSunriseSunsetData[]>(ApiUrl.GET_SUNRISE_SUNSET_BY_SITE_IDS, data, {
      headers: new HttpHeaders({ timeout: `${1000 * 60 * 5}` })
    });
  }

  getChartsData(data: FilterModel): Observable<PowerChartResponse> {
    return this.http.post<PowerChartResponse>(ApiUrl.GET_POWER_CHARTS, data, {
      headers: new HttpHeaders({ timeout: `${1000 * 60 * 5}` })
    });
  }

  generateSingleSiteChart(
    deviceData: DeviceRecords[],
    powerChartSiteSunriseSunsetData: PowerChartSiteSunriseSunsetData[],
    siteName: string,
    isSingleTooltip: boolean = false
  ): any {
    if (!deviceData?.length) {
      return null;
    }
    const siteData = deviceData[0];
    const sunriseSunsetData = powerChartSiteSunriseSunsetData.find(x => x.qesiteid === siteData.qesiteid);
    const isSelect = this.chartViewList.find(x => x.displayName === 'Plot Inverters').isSelected;
    const sunriseSunsetSelected = this.chartViewList.find(x => x.displayName === 'Sunrise/Sunset').isSelected;

    const sunriseSunsetMarkAreaData = sunriseSunsetSelected && sunriseSunsetData ? sunriseSunsetData.qesiteSunriseSunsetMarkArea ?? [] : [];

    const sunriseSunsetMarkLineData = sunriseSunsetSelected && sunriseSunsetData ? sunriseSunsetData.qesiteSunriseSunsetMarkLine ?? [] : [];

    const markArea = {
      itemStyle: {
        color: 'rgba(143,155,179,.24)'
      },
      silent: true,
      data: sunriseSunsetMarkAreaData
    };

    const markLine = {
      symbol: 'none',
      silent: false,
      label: {
        show: false
      },
      lineStyle: {
        type: 'solid',
        color: 'rgba(143,155,179,.24)',
        width: 2
      },
      tooltip: {
        confine: true,
        trigger: 'item',
        className: sunriseSunsetSelected ? 'tooltipText' : '',
        formatter: function (params) {
          const formattedDate = new DatePipe('en-US').transform(new Date(params.data.xAxis), AppConstants.powerChartSateTimeFormat);
          return `${params.name}: ${formattedDate}`;
        }
      },
      data: sunriseSunsetMarkLineData
    };

    const data = {
      title: {
        text: siteName,
        left: 'center',
        top: 45
      },
      grid: {
        y: 100,
        containLabel: true
      },
      responsive: true,
      tooltip: {
        confine: !isSelect,
        trigger: isSingleTooltip ? 'item' : 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        extraCssText: (isSelect ? 'width:auto; max-width:550px; white-space:pre-wrap;' : '') + 'z-index: 99999',
        className: isSelect ? 'tooltipText' : '',
        position: function (point, params, dom, rect, size) {
          const [x, y] = point;
          const tooltipWidth = size.contentSize[0];
          const tooltipHeight = size.contentSize[1];
          const chartWidth = size.viewSize[0];
          const chartHeight = size.viewSize[1];

          let xPos = x + tooltipWidth > chartWidth ? x - tooltipWidth : x;
          let yPos = y + tooltipHeight > chartHeight ? y - tooltipHeight : y;

          if (xPos < 0) xPos = 0;
          if (yPos < 0) yPos = 0;

          return [xPos, yPos];
        }
      },
      toolbox: {
        feature: {
          dataZoom: { show: true },
          dataView: {
            show: true,
            readOnly: true,
            optionToContent: function (opt) {
              let series = opt.series;
              let table = '<table class="table table-bordered"><thead><tr>' + '<th>Time</th>';
              for (let i = 0, l = series.length; i < l; i++) {
                table += `<th>${series[i].name}</th>`;
              }
              table += '</tr></thead><tbody style="color:#000000">';
              for (let i = 0, l = series[0].data.length; i < l; i++) {
                table += '<tr>';
                table += `<td style="color:#000000">${new Date(series[0].data[i][0])?.toLocaleString()}</td>`;
                for (let j = 0, m = series.length; j < m; j++) {
                  table += `<td style="text-align:right; color:#000000;">${series[j].data[i][1]?.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  })}</td>`;
                }
                table += '</tr>';
              }
              table += '</thead></table>';
              return table;
            }
          },
          magicType: { show: true, type: ['line', 'bar'] },
          saveAsImage: { show: true }
        },
        top: 7,
        right: 40
      },
      legend: {
        data: [],
        type: 'scroll',
        bottom: 10
      },
      xAxis: [
        {
          type: 'time',
          data: [],
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            hideOverlap: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: 'kW',
          nameTextStyle: {
            fontWeight: 'bolder',
            align: 'right',
            padding: [0, 6, 0, 0]
          },
          min: 0,
          splitLine: {
            show: true
          },
          formatter: function (value) {
            return value?.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          },
          axisLabel: {
            formatter: function (value) {
              return value?.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            }
          }
        }
      ],
      series: []
    };

    // Group devices by type
    const deviceGroupBy = deviceData.reduce((group, product) => {
      const { devicetype } = product;
      group[devicetype] = group[devicetype] ?? [];
      group[devicetype].push(product);
      return group;
    }, {});

    const sumOfMeter = [];
    const sumOfInverter = [];

    // Process chart view options
    this.chartViewList.forEach(element => {
      if (element.displayName === 'Plot Irradiance' && element.isSelected) {
        this.processIrradianceData(deviceGroupBy, data, markArea, markLine, isSingleTooltip);
      } else if (element.displayName === 'Plot Meters' && element.isSelected) {
        this.processMeterData(deviceGroupBy, data, markArea, markLine, sumOfMeter, isSingleTooltip);
      } else if (element.displayName === 'Sum Meters' && element.isSelected) {
        this.processSumMeterData(deviceGroupBy, data, markArea, markLine, sumOfMeter, isSingleTooltip);
      } else if (element.displayName === 'Plot Inverters' && element.isSelected) {
        this.processInverterData(deviceGroupBy, data, markArea, markLine, sumOfInverter, isSingleTooltip);
      } else if (element.displayName === 'Sum Inverters' && element.isSelected) {
        this.processSumInverterData(deviceGroupBy, data, markArea, markLine, sumOfInverter, isSingleTooltip);
      }
    });

    return data;
  }

  private processIrradianceData(deviceGroupBy: any, data: any, markArea: any, markLine: any, isSingleTooltip: boolean) {
    const tempData = deviceGroupBy['12'];
    let count = 0;
    if (tempData?.length) {
      const devices = tempData.reduce((group, device) => {
        const { qedeviceid } = device;
        group[qedeviceid] = group[qedeviceid] ?? [];
        group[qedeviceid].push(device);
        return group;
      }, {});

      for (let key in devices) {
        const value = devices[key];
        const obj = [];
        for (const i of value) {
          if (i.bindata) {
            count++;
          }
          obj.push([new Date(i.bindatetime), i.bindata]);
        }
        data.legend.data.push(value[0].qedevicename);
        data.series.push({
          color: '#FE8713',
          yAxisIndex: count > 0 ? 1 : 0,
          name: value[0].qedevicename,
          type: 'line',
          tooltip: {
            confine: true,
            valueFormatter: function (value: any) {
              return (
                (value as number)?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' W/m²'
              );
            }
          },
          responsive: true,
          data: obj,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
      }

      if (count > 0) {
        data.yAxis.push({
          type: 'value',
          name: 'W/m²',
          nameTextStyle: {
            fontWeight: 'bolder',
            align: 'left',
            padding: [0, 0, 0, 6]
          },
          min: 0,
          splitLine: {
            show: true
          },
          formatter: function (value) {
            return value?.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          },
          axisLabel: {
            formatter: function (value) {
              return value?.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            }
          }
        });
      }
    }
  }

  private processMeterData(deviceGroupBy: any, data: any, markArea: any, markLine: any, sumOfMeter: any[], isSingleTooltip: boolean) {
    const tempData = deviceGroupBy['2'];
    if (tempData?.length) {
      const devices = tempData.reduce((group, device) => {
        const { qedeviceid } = device;
        group[qedeviceid] = group[qedeviceid] ?? [];
        group[qedeviceid].push(device);
        return group;
      }, {});

      let meterCount = 0;
      for (let key in devices) {
        const value = devices[key];
        const obj = [];
        for (const i of value) {
          obj.push([new Date(i.bindatetime), i.bindata]);
          if (!meterCount) {
            sumOfMeter.push([new Date(i.bindatetime), i.bindata]);
          } else {
            sumOfMeter[obj.length - 1][1] = Math.round((sumOfMeter[obj.length - 1][1] + i.bindata) * 100) / 100;
          }
        }
        data.legend.data.push(value[0].qedevicename);
        data.series.push({
          name: value[0].qedevicename,
          type: 'line',
          color: '#4589FF',
          tooltip: {
            confine: true,
            valueFormatter: function (value: any) {
              return (
                (value as number)?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' kW'
              );
            }
          },
          data: obj,
          responsive: true,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
        meterCount++;
      }
    }
  }

  private processSumMeterData(deviceGroupBy: any, data: any, markArea: any, markLine: any, sumOfMeter: any[], isSingleTooltip: boolean) {
    if (sumOfMeter.length) {
      data.legend.data.push('Meter Sum');
      data.series.push({
        name: 'Meter Sum',
        type: 'line',
        color: '#4589FF',
        tooltip: {
          confine: true,
          valueFormatter: function (value: any) {
            return (
              (value as number)?.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              }) + ' kW'
            );
          }
        },
        data: sumOfMeter,
        responsive: true,
        symbolSize: isSingleTooltip ? 4 : 1,
        markArea,
        markLine
      });
    } else {
      // Calculate sum if not already done
      const tempData = deviceGroupBy['2'];
      if (tempData?.length) {
        const devices = tempData.reduce((group, device) => {
          const { qedeviceid } = device;
          group[qedeviceid] = group[qedeviceid] ?? [];
          group[qedeviceid].push(device);
          return group;
        }, {});

        let meterCount = 0;
        for (let key in devices) {
          const value = devices[key];
          for (const i of value) {
            if (!meterCount) {
              sumOfMeter.push([new Date(i.bindatetime), i.bindata]);
            } else {
              sumOfMeter[sumOfMeter.length - 1][1] = Math.round((sumOfMeter[sumOfMeter.length - 1][1] + i.bindata) * 100) / 100;
            }
          }
          meterCount++;
        }

        data.legend.data.push('Meter Sum');
        data.series.push({
          name: 'Meter Sum',
          color: '#4589FF',
          type: 'line',
          tooltip: {
            confine: true,
            valueFormatter: function (value: any) {
              return (
                (value as number)?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' kW'
              );
            }
          },
          data: sumOfMeter,
          responsive: true,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
      }
    }
  }

  private processInverterData(deviceGroupBy: any, data: any, markArea: any, markLine: any, sumOfInverter: any[], isSingleTooltip: boolean) {
    const tempData = deviceGroupBy['1'];
    if (tempData?.length) {
      const devices = tempData.reduce((group, device) => {
        const { qedeviceid } = device;
        group[qedeviceid] = group[qedeviceid] ?? [];
        group[qedeviceid].push(device);
        return group;
      }, {});

      let inverterCount = 0;
      const colorList = INVERTER_COLORS;
      let colorIndex = 0;

      for (let key in devices) {
        const value = devices[key];
        const obj = [];
        for (const i of value) {
          obj.push([new Date(i.bindatetime), i.bindata]);
          if (!inverterCount) {
            sumOfInverter.push([new Date(i.bindatetime), i.bindata]);
          } else {
            sumOfInverter[obj.length - 1][1] = Math.round((sumOfInverter[obj.length - 1][1] + i.bindata) * 100) / 100;
          }
        }
        inverterCount++;
        data.legend.data.push(value[0].qedevicename);

        let color = colorList[colorIndex] || colorList[0];
        colorIndex = (colorIndex + 1) % colorList.length;

        data.series.push({
          name: value[0].qedevicename,
          type: 'line',
          color: color,
          tooltip: {
            confine: true,
            valueFormatter: function (value: any) {
              return (
                (value as number)?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' kW'
              );
            }
          },
          data: obj,
          responsive: true,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
      }
    }
  }

  private processSumInverterData(
    deviceGroupBy: any,
    data: any,
    markArea: any,
    markLine: any,
    sumOfInverter: any[],
    isSingleTooltip: boolean
  ) {
    if (sumOfInverter.length) {
      data.legend.data.push('Inverter Sum');
      data.series.push({
        name: 'Inverter Sum',
        color: '#00B050',
        type: 'line',
        tooltip: {
          confine: true,
          valueFormatter: function (value: any) {
            return (
              (value as number)?.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              }) + ' kW'
            );
          }
        },
        data: sumOfInverter,
        responsive: true,
        symbolSize: isSingleTooltip ? 4 : 1,
        markArea,
        markLine
      });
    } else {
      // Calculate sum if not already done
      const tempData = deviceGroupBy['1'];
      if (tempData?.length) {
        const devices = tempData.reduce((group, device) => {
          const { qedeviceid } = device;
          group[qedeviceid] = group[qedeviceid] ?? [];
          group[qedeviceid].push(device);
          return group;
        }, {});

        let inverterCount = 0;
        for (let key in devices) {
          const value = devices[key];
          for (const i of value) {
            if (!inverterCount) {
              sumOfInverter.push([new Date(i.bindatetime), i.bindata]);
            } else {
              sumOfInverter[sumOfInverter.length - 1][1] = Math.round((sumOfInverter[sumOfInverter.length - 1][1] + i.bindata) * 100) / 100;
            }
          }
          inverterCount++;
        }

        data.legend.data.push('Inverter Sum');
        data.series.push({
          name: 'Inverter Sum',
          color: '#00B050',
          type: 'line',
          tooltip: {
            confine: true,
            valueFormatter: function (value: any) {
              return (
                (value as number)?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' kW'
              );
            }
          },
          data: sumOfInverter,
          responsive: true,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
      }
    }
  }
}
