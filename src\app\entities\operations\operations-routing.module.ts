import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { QE_MENU_MODULE_ENUM } from '../../@shared/enums/qe-menu.enum';
import { QEAnalyticsGatheringGuard } from '../qe-analytics/services/qe-analytics-gathering.guard';

const routes: Routes = [
  {
    path: 'operations-reports',
    loadChildren: () => import('./operations-reports/operations-reports.module').then(m => m.OperationsReportsModule),
    canActivate: [QEAnalyticsGatheringGuard],
    canDeactivate: [QEAnalyticsGatheringGuard],
    data: { qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.OP_REPORTS, qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.OPERATIONS }
  },
  {
    path: 'region-mapping',
    loadChildren: () => import('./region-mapping/region-mapping.module').then(m => m.RegionMappingModule),
    canActivate: [QEAnalyticsGatheringGuard],
    canDeactivate: [QEAnalyticsGatheringGuard],
    data: { qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.OP_REGION_MAPPING, qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.OPERATIONS }
  },
  {
    path: 'services',
    loadChildren: () => import('./operation-services/operation-services.module').then(m => m.OperationServicesModule),
    canActivate: [QEAnalyticsGatheringGuard],
    canDeactivate: [QEAnalyticsGatheringGuard],
    data: { qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.OP_SERVICES, qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.OPERATIONS }
  },
  {
    path: 'contracts',
    loadChildren: () => import('./contracts/contracts.module').then(m => m.ContractsModule),
    canActivate: [QEAnalyticsGatheringGuard],
    canDeactivate: [QEAnalyticsGatheringGuard],
    data: { qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.OP_CONTRACTS, qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.OPERATIONS }
  },
  {
    path: 'custom-forms',
    loadChildren: () => import('./custom-forms/custom-forms.module').then(m => m.CustomFormsModule),
    canActivate: [QEAnalyticsGatheringGuard],
    canDeactivate: [QEAnalyticsGatheringGuard],
    data: { qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS, qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.OPERATIONS }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class OperationsRoutingModule {}
