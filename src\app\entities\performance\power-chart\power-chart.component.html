<nb-card class="performanceDashboardSpinner" [nbSpinner]="loading || getMoreLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="d-flex align-items-center">
      <h6>Power Charts</h6>
      <div class="ms-auto d-none d-lg-inline-block">
        <button
          class="ms-2"
          size="small"
          status="primary"
          *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
          (click)="slideShow()"
          [disabled]="getMoreLoading"
          nbButton
        >
          Slide Show
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 performanceFilter mb-3">
        <div class="row align-items-center mb-2">
          <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
            <label class="label" for="customer">Customer</label>
            <ng-select
              id="power-chart-customer-drop-down"
              name="customer"
              [items]="customerList"
              (change)="onCustomerSelect()"
              (clear)="onCustomerDeSelect()"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.customerId"
              notFoundText="No Customer Found"
              placeholder="Select Customer"
              appendTo="body"
              [loading]="isCustomerLoading"
              [disabled]="isCustomerLoading || getMoreLoading"
            >
            </ng-select>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
            <label class="label" for="portfolio">Portfolio</label>
            <ng-select
              id="power-chart-portfolio-customer-drop-down"
              name="portfolio"
              [multiple]="true"
              [items]="portfolioList"
              (change)="onPortfolioChange()"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.portfolioIds"
              [closeOnSelect]="false"
              notFoundText="No Portfolio Found"
              placeholder="Select Portfolio"
              [loading]="isPortfolioLoading"
              [disabled]="getMoreLoading"
              appendTo="body"
              (search)="onFilter($event, 'filteredPortfolioIds')"
              (close)="filteredPortfolioIds = []"
            >
              <ng-template ng-header-tmp *ngIf="portfolioList && portfolioList.length">
                <button type="button" (click)="selectAllPortfolio()" class="btn btn-sm btn-primary me-2">Select all</button>
                <button type="button" (click)="unSelectAllPortfolio()" class="btn btn-sm btn-primary ml-2">Unselect all</button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
            <label class="label" for="site">Site</label>
            <ng-select
              id="power-chart-site-drop-down"
              name="Site"
              [multiple]="true"
              [items]="siteList"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.siteIds"
              [closeOnSelect]="false"
              notFoundText="No Site Found"
              (change)="onSitesChange()"
              placeholder="Select Site"
              [loading]="siteListLoading"
              [disabled]="getMoreLoading"
              appendTo="body"
              (search)="onFilter($event, 'filteredSiteIds')"
              (close)="filteredSiteIds = []"
              [virtualScroll]="true"
            >
              <ng-template ng-header-tmp *ngIf="siteList && siteList.length">
                <button type="button" (click)="selectAllSite()" class="btn btn-sm btn-primary me-2">Select all</button>
                <button type="button" (click)="unSelectAllSite()" class="btn btn-sm btn-primary ml-2">Unselect all</button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
            <label class="label" for="month">Duration</label>
            <ng-select
              id="duration"
              name="duration"
              [items]="durationList"
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.duration"
              notFoundText="No Month Found"
              placeholder="Select Month"
              [clearable]="false"
              (change)="onDurationSelect()"
              [disabled]="getMoreLoading"
              appendTo="body"
            >
            </ng-select>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 pe-lg-0">
            <div class="row">
              <div
                class="col-6"
                [ngClass]="{
                  'col-6 pe-1':
                    filterModel.duration !== 'Specific Date' &&
                    filterModel.duration !== 'Current Day' &&
                    filterModel.duration !== 'Previous Day',
                  'col-12':
                    filterModel.duration === 'Specific Date' ||
                    filterModel.duration === 'Current Day' ||
                    filterModel.duration === 'Previous Day'
                }"
              >
                <label
                  class="label"
                  for="year"
                  *ngIf="
                    filterModel.duration !== 'Specific Date' &&
                    filterModel.duration !== 'Current Day' &&
                    filterModel.duration !== 'Previous Day'
                  "
                >
                  Start
                </label>
                <label
                  class="label"
                  for="year"
                  *ngIf="
                    filterModel.duration === 'Specific Date' ||
                    filterModel.duration === 'Current Day' ||
                    filterModel.duration === 'Previous Day'
                  "
                >
                  Date
                </label>
                <input
                  class="form-control search-textbox"
                  [nbDatepicker]="startDate"
                  name="availabilityDate"
                  placeholder="Select Date"
                  id="input-availabilityDate start-date"
                  autocomplete="off"
                  [(ngModel)]="filterModel.startDate"
                  (ngModelChange)="dateChanged($event)"
                  [disabled]="
                    filterModel.duration === 'Current Day' ||
                    filterModel.duration === 'Previous Day' ||
                    filterModel.duration === 'Last 3 Days' ||
                    getMoreLoading
                  "
                />
                <nb-datepicker #startDate [max]="maxDate"></nb-datepicker>
              </div>
              <div
                class="col-6 ps-1"
                *ngIf="
                  filterModel.duration !== 'Specific Date' &&
                  filterModel.duration !== 'Current Day' &&
                  filterModel.duration !== 'Previous Day'
                "
              >
                <label class="label" for="year">End</label>
                <input
                  class="form-control search-textbox"
                  [nbDatepicker]="endDate"
                  name="availabilityDate"
                  placeholder="Select Date"
                  id="input-availabilityDate end-date"
                  autocomplete="off"
                  [(ngModel)]="filterModel.endDate"
                  [disabled]="
                    filterModel.duration === 'Current Day' ||
                    filterModel.duration === 'Previous Day' ||
                    filterModel.duration === 'Last 3 Days' ||
                    getMoreLoading
                  "
                />
                <nb-datepicker #endDate [min]="minDate" [max]="maxEndDate"></nb-datepicker>
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-2 mb-2 mt-1 mt-md-4 d-flex">
            <button
              id="powerchart-view-data"
              class="linear-mode-button"
              nbButton
              [disabled]="!filterModel.siteIds.length || getMoreLoading"
              status="primary"
              size="small"
              type="button"
              (click)="viewData()"
            >
              View Data
            </button>
            <button
              id="power-chart-clear-filter"
              class="linear-mode-button ms-2"
              nbButton
              status="primary"
              size="small"
              type="button"
              [disabled]="loading || getMoreLoading"
              (click)="ClearFilter()"
            >
              clear
            </button>
          </div>
        </div>
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center flex-wrap">
            <div class="pe-4" *ngFor="let item of chartViewList">
              <nb-checkbox
                id="powerchart-{{ item.name }}"
                status="primary"
                (change)="loading = true; changeSunriseSunsetFlag(); changeReport(true)"
                [checked]="item.isSelected"
                [(ngModel)]="item.isSelected"
                [disabled]="getMoreLoading || item.disabled"
              >
                {{ item.name }}
              </nb-checkbox>
            </div>
          </div>
          <nb-toggle
            id="power-chart-tooltip-toggle"
            status="primary"
            class="ms-auto"
            [(checked)]="isSingleTooltip"
            [(ngModel)]="isSingleTooltip"
            (checkedChange)="loading = true; changeReport(true)"
          >
            Single Tooltip
          </nb-toggle>
        </div>
      </div>
      <div class="col-12">
        <div class="row">
          <div class="col-12 mb-3" *ngIf="getMoreLoading">
            <nb-progress-bar [value]="progress" status="info" [displayValue]="true"></nb-progress-bar>
          </div>
          <ng-container *ngIf="portfolioData.length && !getMoreLoading">
            <div class="col-12 mb-3" *ngFor="let portfolioObj of portfolioData; let i = index; trackBy: trackByFunction">
              <nb-accordion>
                <nb-accordion-item [expanded]="true" class="border-bottom">
                  <nb-accordion-item-header class="accordion_head">
                    {{ portfolioObj?.portfolioName }}
                  </nb-accordion-item-header>
                  <nb-accordion-item-body>
                    <ng-container *ngIf="portfolioObj?.sitePowerData?.length">
                      <div class="row">
                        <div
                          class="col-12 mb-3"
                          [ngClass]="{ 'col-lg-6 col-xl-4': portfolioObj?.sitePowerData?.length > 1 }"
                          *ngFor="let item of portfolioObj?.sitePowerData; let index = index; trackBy: trackByFunction"
                        >
                          <div #containerRef class="chart-box">
                            <div class="z-index-1 position-relative">
                              <em
                                *ngIf="isFullView"
                                aria-hidden="true"
                                class="fa fa-compress-alt text-primary cursor-pointer float-end mt-3 me-3"
                                (click)="compressView()"
                              ></em>
                              <em
                                *ngIf="!isFullView"
                                aria-hidden="true"
                                class="fa fa-expand-alt text-primary cursor-pointer float-end mt-3 me-3"
                                (click)="chartsData = item; expandView(template)"
                              ></em>
                            </div>
                            <div class="bar-chart-box">
                              <div echarts [options]="item" [theme]="currentTheme" class="demo-chart"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                    <div class="text-center p-3" *ngIf="!portfolioObj?.sitePowerData?.length">No data found</div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
              </nb-accordion>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
<ng-template #template>
  <div class="row">
    <div class="col-12 mt-1 mb-2 pe-0" *ngIf="isFullView && chartsData">
      <em
        *ngIf="isFullView"
        aria-hidden="true"
        class="fa fa-compress-alt text-primary cursor-pointer float-end"
        (click)="compressView()"
      ></em>
      <em
        *ngIf="!isFullView"
        aria-hidden="true"
        class="fa fa-expand-alt text-primary cursor-pointer float-end"
        (click)="chartsData = item; expandView(template)"
      ></em>
    </div>
    <div echarts [options]="chartsData" [theme]="currentTheme" class="demo-chart vh-85"></div>
  </div>
</ng-template>

<ng-template #alertDetailsTemplate>
  <div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
    <div class="modal-header justify-content-between align-items-center">
      <h5 class="mb-0">Alert Details</h5>
      <button type="button" class="close" aria-label="Close" (click)="closeAlertModal()">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>

    <div class="modal-body alert-details-body">
      <div *ngIf="selectedAlertDetails && selectedAlertDetails.length > 0">
        <div class="row">
          <div class="col-md-4" *ngFor="let alert of selectedAlertDetails">
            <div class="alert-item">
              <p>
                {{ alert.deviceName }} - <strong>{{ alert.binData }} kW</strong> ({{ alert.binDateTime }})
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>
