import { DatePipe } from '@angular/common';
import { Component, HostListener, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NbMenuService } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { MenuItem } from 'primeng/api';
import { forkJoin, Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { AllReportDropdown } from '../../../@shared/models/report.model';
import { DateToUsersTimezonePipe } from '../../../@shared/pipes/date-to-users-timezone.pipe';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppliedFilter } from '../../site-device/site-device.model';
import { cmTicketPageFilterKeys, NavigationBack, statusDropDownList, TicketColumnFilter, Tickets, TicketsList } from '../ticket.model';
import { TicketService } from '../ticket.service';
import { BulkTicketCloseReOpenActionComponent } from './bulk-ticket-close-re-open-action/bulk-ticket-close-re-open-action.component';
import { BulkTicketCreationActionComponent } from './bulk-ticket-creation-action/bulk-ticket-creation-action.component';
import { BulkTicketEditActionComponent } from './bulk-ticket-edit-action/bulk-ticket-edit-action.component';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';
import { SharedCPSDataService } from '../../../@shared/services';
@Component({
  selector: 'sfl-ticket-listing',
  templateUrl: './ticket-listing.component.html',
  styleUrls: ['./ticket-listing.component.scss']
})
export class TicketListingComponent implements OnInit {
  @ViewChild('truckRollModal') truckRollModal: TemplateRef<void>;
  loading = false;
  tickets: Tickets[] = [];
  selectedTicket: Tickets;
  subscription: Subscription = new Subscription();
  filterModel: CommonFilter = new CommonFilter();
  appliedFilter: AppliedFilter[] = [];
  isFilterDisplay = false;
  isReadMore = true;
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  total = 10;
  viewPage = FILTER_PAGE_NAME.CM_ALL_TICKETS_LISTING;
  viewFilterSection = 'ticketsFilterSection';
  sortOptionList = {
    Number: 'asc',
    Priority: 'asc',
    CustomerPortfolio: 'asc',
    Site: 'asc',
    Device: 'asc',
    Close: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc',
    Open: 'desc',
    TicketType: 'asc'
  };
  fullDateFormat = AppConstants.fullDateFormat;
  dateFormat = AppConstants.momentDateFormat;
  ticketColumnFilter = TicketColumnFilter;
  allReportDropdown = new AllReportDropdown();
  filterDetails: FilterDetails = new FilterDetails();
  showCommonFilter = false;
  rightClickItems: MenuItem[];
  modalRef: BsModalRef;
  showFilter: boolean;
  modalId: number;
  openPendingTicketsCount: number = 0;
  items = [
    { title: 'Bulk Create', id: 1 },
    { title: 'Bulk Edit', id: 2 },
    { title: 'Bulk Close/Re-Open', id: 3 }
  ];
  selectedBulkAction = null;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly datePipe: DatePipe,
    private readonly commonService: CommonService,
    private readonly ticketService: TicketService,
    private readonly storageService: StorageService,
    private readonly modalService: BsModalService,
    private readonly dateToUsersTimezonePipe: DateToUsersTimezonePipe,
    private nbMenuService: NbMenuService,
    private readonly sharedCPSDataService: SharedCPSDataService
  ) {}

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth > 630) {
      this.showFilter = true;
    } else {
      this.showFilter = false;
    }
  }

  ngOnInit(): void {
    this.subscription.add(
      this.nbMenuService.onItemClick().subscribe(event => {
        this.onBulkSelection(event.item);
      })
    );
    if (window.innerWidth > 630) {
      this.showFilter = true;
    } else {
      this.showFilter = false;
    }

    this.route.queryParams.subscribe(params => {
      if (params?.ticketIdModal) {
        this.modalId = +params.ticketIdModal;
        this.router.navigate([], { queryParams: { ticketIdModal: null }, queryParamsHandling: 'merge' });
      }
    });

    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection),
      jumpToMenuFilterData = this.sharedCPSDataService.getSharedCPSDataForCommonFilterWithResetData();

    if (Object.keys(jumpToMenuFilterData)?.length) {
      this.filterModel.customerIds = jumpToMenuFilterData?.customerIds ?? [];
      this.filterModel.portfolioIds = jumpToMenuFilterData?.portfolioIds ?? [];
      this.filterModel.siteIds = jumpToMenuFilterData?.siteIds ?? [];

      this.storageService.set(this.viewPage, this.filterModel);
    } else if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.filterModel.siteIds = (localFilterData || defaultFilterData).siteIds;
      this.filterModel.states = (localFilterData || defaultFilterData).states;
      this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds;
      this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds;

      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.initFilterDetails();
    this.filterModel.direction = 'desc';
    this.filterModel.itemsCount = this.pageSize;
    this.isFilterDisplay = filterSection;
    this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel);
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));

    model.getAllStatusListBetweenDates = false;

    if (model.openDate && model.openDate.start && model.openDate.end) {
      model.openDate.start = this.datePipe.transform(model?.openDate?.start, AppConstants.fullDateFormat);
      model.openDate.end = this.datePipe.transform(model?.openDate?.end, AppConstants.fullDateFormat);
    }
    if (model.closeDate && model.closeDate.start && model.closeDate.end) {
      model.closeDate.start = this.datePipe.transform(model?.closeDate?.start, AppConstants.fullDateFormat);
      model.closeDate.end = this.datePipe.transform(model?.closeDate?.end, AppConstants.fullDateFormat);
    }
    if (model.activityRange && model.activityRange.start && model.activityRange.end) {
      model.activityRange.start = this.datePipe.transform(model?.activityRange?.start, AppConstants.fullDateFormat);
      model.activityRange.end = this.datePipe.transform(model?.activityRange?.end, AppConstants.fullDateFormat);
    }

    if (this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, model, cmTicketPageFilterKeys)) {
      const tempArray = [this.ticketService.getAllTicketList(model)],
        tempArrayObj = ['bindTicketList'];
      this.getAllLists(tempArray, tempArrayObj);
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = 'ticketsFilterSection';
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.SITE.show = true;
    filterItem.PRIORITY.show = true;
    filterItem.STATE.show = true;
    filterItem.STATUS.show = true;
    filterItem.OPEN_DATE.show = true;
    filterItem.CLOSE_DATE.show = true;
    filterItem.ACTIVITY_RANGE.show = true;
    filterItem.QE_TECH.show = true;
    filterItem.STATUS.multi = true;
    filterItem.PRIORITY.multi = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.RESOLVED.show = true;
    filterItem.TRUCK_ROLL.show = true;
    filterItem.TICKET_TYPE.show = true;
    filterItem.TICKET_TYPE.multi = true;
    filterItem.COSTS_TYPE.show = true;
    filterItem.COSTS_TYPE.multi = true;
    if (!this.checkAuthorisationsFn([this.roleType.CUSTOMER])) {
      filterItem.REGION.show = true;
      filterItem.SUB_REGION.show = true;
      filterItem.BILLING_STATUS.show = true;
    }
    filterItem.TICKET_ESTIMATION_STATUS.show = true;
    filterItem.TICKET_ESTIMATION_STATUS.multi = true;

    this.filterDetails.default_direction = 'desc';
    this.filterDetails.filter_item = filterItem;
    setTimeout(() => {
      this.showCommonFilter = true;
    }, 0);
  }

  getAllLists(apiArray: any, mapResultList: string[], filterArrayObj = []) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          if (value === 'bindTicketList' || value === 'bindSearchTicketList') {
            this.bindTickets(res[index]);
          } else {
            this[value] = res[index];
          }
        }
        setTimeout(() => {
          if (this.modalId) {
            this.openTruckRollModal(
              this.tickets.find(ticket => ticket.id === this.modalId),
              this.truckRollModal
            );
          }
          this.loading = false;
        }, 1000);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getAllTicketList(saveFilter = true, filterParams?: CommonFilter) {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }

    const model = { ...this.filterModel };

    this.processDateRanges(model);

    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }

    model.getAllStatusListBetweenDates = false;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    this.subscription.add(
      this.ticketService.getAllTicketList(model).subscribe({
        next: (res: TicketsList) => {
          clearTimeout(timeoutId);
          this.bindTickets(res);
        },
        error: e => {
          clearTimeout(timeoutId);
          this.loading = false;
        }
      })
    );
  }

  private processDateRanges(model: CommonFilter): void {
    if (this.filterModel.openDate?.start && this.filterModel.openDate?.end) {
      model.openDate = {
        start: this.datePipe.transform(this.filterModel.openDate.start, AppConstants.fullDateFormat),
        end: this.datePipe.transform(this.filterModel.openDate.end, AppConstants.fullDateFormat)
      };
    } else {
      this.filterModel.openDate = null;
      model.openDate = null;
    }

    if (this.filterModel.closeDate?.start && this.filterModel.closeDate?.end) {
      model.closeDate = {
        start: this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat),
        end: this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat)
      };
    } else {
      this.filterModel.closeDate = null;
      model.closeDate = null;
    }

    if (this.filterModel.activityRange?.start && this.filterModel.activityRange?.end) {
      model.activityRange = {
        start: this.datePipe.transform(this.filterModel.activityRange.start, AppConstants.fullDateFormat),
        end: this.datePipe.transform(this.filterModel.activityRange.end, AppConstants.fullDateFormat)
      };
    } else {
      this.filterModel.activityRange = null;
      model.activityRange = null;
    }
  }

  bindTickets(res: TicketsList) {
    setTimeout(() => {
      this.loading = false;
      this.tickets = res?.ticketLists ? res.ticketLists : [];
      this.tickets?.forEach((ticket: Tickets) => {
        ticket.show = false;
      });
      this.total = res?.totalTickets ? res.totalTickets : 0;
      this.openPendingTicketsCount = res?.pendingTicketCount + res?.openTicketCount;
    }, 0);
  }

  openAndCloseDateChanged(event) {
    if ((event && event.start && event.end) || event === null) {
      this.resetPage();
      this.getAllTicketList();
    }
  }

  resetPage() {
    this.filterModel.page = 0;
    this.currentPage = 0;
  }

  // Pagesize Change
  onChangeSize() {
    this.currentPage = 0;
    this.filterModel.page = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllTicketList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllTicketList();
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllTicketList();
  }

  exportData() {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.openDate && this.filterModel.openDate.start && this.filterModel.openDate.end) {
      model.openDate.start = this.datePipe.transform(this.filterModel.openDate.start, AppConstants.fullDateFormat);
      model.openDate.end = this.datePipe.transform(this.filterModel.openDate.end, AppConstants.fullDateFormat);
    } else {
      model.openDate = null;
    }
    if (this.filterModel.closeDate && this.filterModel.closeDate.start && this.filterModel.closeDate.end) {
      model.closeDate.start = this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat);
      model.closeDate.end = this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat);
    } else {
      model.closeDate = null;
    }
    if (this.filterModel.activityRange && this.filterModel.activityRange.start && this.filterModel.activityRange.end) {
      model.activityRange.start = this.datePipe.transform(this.filterModel.activityRange.start, AppConstants.fullDateFormat);
      model.activityRange.end = this.datePipe.transform(this.filterModel.activityRange.end, AppConstants.fullDateFormat);
    } else {
      model.activityRange = null;
    }
    model.itemsCount = this.total;
    model.page = 0;
    model.getAllStatusListBetweenDates = false;
    model.isExport = true;
    this.subscription.add(
      this.ticketService.getAllTicketList(model).subscribe({
        next: (data: TicketsList) => {
          this.exportToCSV(data.ticketLists);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  exportToCSV(data: Tickets[]) {
    const title = 'Ticket';
    const rows: any = [
      [
        'Number',
        'Opened',
        'Closed',
        'Customer',
        'Portfolio',
        'Site',
        'Device',
        'Issue',
        'Activity Log',
        'Truck Roll',
        'Resolved',
        'Status',
        'Priority',
        'Ticket Type',
        'Affected kWac',
        'Production Loss',
        'Estimate Number',
        'Additional Hours',
        'Estimate Total',
        'Estimate Status',
        'Customer PO',
        'Approved By',
        'Approved On'
      ]
    ];
    if (this.checkAuthorisationsFn([this.roleType.MANAGER, this.roleType.SUPPORT, this.roleType.ADMIN])) {
      rows[0].push('Notes');
    }
    for (const i of data) {
      const tempData = [
        i.ticketNumber,
        `${this.datePipe.transform(i.open, AppConstants.fullDateFormat)}`,
        i.close ? `${this.datePipe.transform(i.close, AppConstants.fullDateFormat)}` : '',
        i.customerName,
        i.portfolioName,
        i.siteName,
        i.deviceLabel ? i.deviceLabel : '',
        i.issue,
        i.fullActivityLog ? i.fullActivityLog : '',
        i.truckRoll,
        i.isResolve,
        i.statusStr,
        i.priorityStr,
        i.ticketTypeName,
        i.affectedkWac,
        i.productionLossStr,
        i.estNumber ? i.estNumber : '',
        i.additionalHours ? this.commonService.formatNumber(i.additionalHours) : '',
        i.estimateTotal ? `$${this.commonService.formatNumber(i.estimateTotal)}` : '',
        i.estimateStatus ? i.estimateStatus : '',
        i.customerPO ? i.customerPO : '',
        i.approvedByName ? i.approvedByName : '',
        i.approvedOn ? `${this.datePipe.transform(i.approvedOn, AppConstants.fullDateFormat)}` : ''
      ];
      if (this.checkAuthorisationsFn([this.roleType.MANAGER, this.roleType.SUPPORT, this.roleType.ADMIN])) {
        tempData.push(i.fullNotes ? i.fullNotes : '');
      }
      rows.push(tempData);
    }
    this.commonService.exportExcel(rows, title);
    this.loading = false;
  }

  toggleFilter() {
    this.isFilterDisplay = !this.isFilterDisplay;
    this.storageService.set(this.viewFilterSection, this.isFilterDisplay);
  }
  clearSearch() {
    this.filterModel.searchBy = 'All';
    this.filterModel.searchValue = '';
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    filterParams.direction = this.filterModel.direction;
    this.getAllTicketList(true, filterParams);
  }

  openLink(ticketNumber: string, inNewWindow: boolean) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['../entities/ticket/detail/view', `${ticketNumber}`], { queryParams: { back: NavigationBack.TICKETS } })
    );
    if (inNewWindow) {
      window.open(url, '_blank', 'width=' + screen.availWidth + ',height=' + screen.availHeight);
    } else {
      window.open(url, '_blank');
    }
  }

  openTruckRollModal(item: Tickets, template: TemplateRef<void>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    this.selectedTicket = item;
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  redirectToTruckRollScreen(truckNumber: string) {
    this.modalRef.hide();
    const queryParams = { ticketIdModal: this.selectedTicket.id };
    this.router.navigate([`../entities/ticket/truckroll-gallery/${truckNumber}`], { queryParams });
  }

  // get filteredItems() {
  //   return this.total <= 1000 ? this.items : this.items.filter(item => item.id !== 2 && item.id !== 3);
  // }

  onBulkSelection(bulkAction) {
    this.selectedBulkAction = bulkAction.id;
    if (this.selectedBulkAction === 1) {
      this.openBulkTicketCreationModal();
    } else if (this.selectedBulkAction === 2 || this.selectedBulkAction === 3) {
      this.modalRef ? this.modalRef.hide() : null;
      this.openBulkTicketModalAfterConfirmation();
    }
  }

  openBulkTicketModalAfterConfirmation() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `${
          this.selectedBulkAction === 2
            ? `Bulk edit ${this.total > 1000 ? 1000 : this.total} Ticket(s)?`
            : `Close/Re-Open ${this.openPendingTicketsCount > 1000 ? 1000 : this.openPendingTicketsCount} ticket(s)?`
        }`,
        isWarning: false,
        confirmBtnText: 'Next',
        cancelBtnText: 'Cancel'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          class: 'modal-full-view-dialog',
          initialState: {
            selectedBulkAction: this.selectedBulkAction,
            filterModel: this.filterModel,
            total: this.total
          }
        };
        if (this.selectedBulkAction === 2) {
          this.modalRef = this.modalService.show(BulkTicketEditActionComponent, ngModalOptions);
        } else {
          this.modalRef = this.modalService.show(BulkTicketCloseReOpenActionComponent, ngModalOptions);
        }
        this.modalRef.content.onClose.subscribe(result => {
          if (result) {
            this.selectedBulkAction = null;
            this.modalRef ? this.modalRef.hide() : null;
            this.getAllTicketList();
          }
        });
      }
    });
  }

  openBulkTicketCreationModal(): void {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog',
      initialState: {
        selectedBulkAction: this.selectedBulkAction,
        total: this.total
      }
    };

    this.modalRef = this.modalService.show(BulkTicketCreationActionComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.selectedBulkAction = null;
        this.modalRef ? this.modalRef.hide() : null;
        this.getAllTicketList();
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
