import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AppConstants } from '../../../../@shared/constants';
import { ROLE_TYPE } from '../../../../@shared/enums';
import { AlertService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { checkAuthorisations } from '../../../../@shared/utils';
import {
  DataTableDeviceDataList,
  DataTableFilter,
  DataTableInformationList,
  EditHistoryData,
  FetchDeviceRecord,
  FetchStatus,
  PutDataTable,
  UpdateAllRecord
} from '../data-table-model';
import { AvailabilityDataTableService } from '../data-table.service';
import { EditInformationComponent } from '../edit-information/edit-information.component';

@Component({
  selector: 'sfl-data-table-information',
  templateUrl: './data-table-information.component.html',
  styleUrls: ['./data-table-information.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DataTableInformationComponent implements OnInit {
  @Input() informationList: DataTableInformationList = null;
  @Input() isFullView = false;
  @Input() siteId: number;
  @Input() dataTableFilter: DataTableFilter;
  @Output() deviceFetchingStart: EventEmitter<string[]> = new EventEmitter();
  @Output() fetchingStart: EventEmitter<string> = new EventEmitter();
  @Output() loadingStart: EventEmitter<boolean> = new EventEmitter();
  public onClose: Subject<boolean> = new Subject();
  modalRef: BsModalRef;
  subscription: Subscription = new Subscription();
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;
  @Output() refreshList: EventEmitter<boolean> = new EventEmitter();

  constructor(
    private readonly modalService: BsModalService,
    public _bsModalRef: BsModalRef,
    private readonly availabilityDataTableService: AvailabilityDataTableService,
    private readonly alertService: AlertService,
    public datePipe: DatePipe,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {}

  expandView() {
    this.isFullView = true;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-dialog',
      initialState: {
        informationList: this.informationList,
        isFullView: true,
        siteId: this.siteId
      }
    };
    this.modalRef = this.modalService.show(DataTableInformationComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      this.isFullView = res;
    });
  }

  compressView() {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  updateRecord(label: string, data: DataTableDeviceDataList, index: number, subIndex: number) {
    if (data.binId) {
      this.availabilityDataTableService.checkFetchIsInProcess(data.binId).subscribe({
        next: (res: FetchStatus) => {
          if (!res.isFetching) {
            this.updateDeviceRecord(label, data, index, subIndex);
          } else {
            this.deviceFetchingStart.emit([String(data.hardwareId)]);
            this.alertService.showInfoToast(res.message);
          }
        },
        error: (e: FetchStatus) => {
          this.alertService.showErrorToast(e.message);
        }
      });
    } else {
      this.updateDeviceRecord(label, data, index, subIndex);
    }
  }

  updateDeviceRecord(label: string, data: DataTableDeviceDataList, index: number, subIndex: number) {
    const selectedRecord: PutDataTable = new PutDataTable();
    selectedRecord.siteId = this.siteId;
    selectedRecord.binId = data.binId;
    selectedRecord.binValue = data.binValue;
    selectedRecord.day = data.day;
    selectedRecord.frequency = data.frequency;
    selectedRecord.hardwareId = data.hardwareId;
    selectedRecord.isLock = data.isLock;
    selectedRecord.month = data.month;
    selectedRecord.year = data.year;
    selectedRecord.hour = data.hour;
    selectedRecord.min = data.min;
    selectedRecord.automationSiteDetailId = data.automationSiteDetailId;
    selectedRecord.qEDeviceId = data.qEDeviceId;
    if (label === 'Monthly Total' || label === 'Yearly Total') {
      const month = new Date(this.dataTableFilter.datePerformed);
      selectedRecord.binDateTime = new Date(Date.UTC(month.getFullYear(), month.getMonth() - 1, 1, 0, 0, 0));
    } else {
      data.binDateTime = new Date(data.binDateTime);
      selectedRecord.binDateTime = new Date(Date.UTC(data.year, data.month - 1, data.day, data.hour, data.min, 0));
    }
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-sm modal-dialog-right',
      initialState: {
        deviceRecord: selectedRecord
      }
    };
    this.modalRef = this.modalService.show(EditInformationComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      if (res) {
        setTimeout(() => {
          // this.informationList.devicesData[index].data[subIndex] = res;
          this.refreshList.emit(true);
        }, 0);
      }
    });
  }

  fetchDeviceRecord(id: string, index: number) {
    this.startStopFetching(id, index, true);
    const model: FetchDeviceRecord = new FetchDeviceRecord();
    model.datePerformed = this.datePipe.transform(this.dataTableFilter.datePerformed, AppConstants.fullDateFormat);
    model.customerId = this.dataTableFilter.customerId;
    model.siteId = this.siteId;
    model.fetchType = 1;
    if (id === 'all') {
      for (const i of this.informationList.devices) {
        if (!i.isOnlyQEDevice) {
          model.hardwareIds.push(String(i.hardwareId));
        }
      }
    } else {
      model.hardwareIds.push(String(id));
    }
    model.frequency = 0;
    this.subscription.add(
      this.availabilityDataTableService.refetchDataTables(model).subscribe({
        next: (res: FetchStatus) => {
          setTimeout(() => {
            if (res.status === 500) {
              this.alertService.showErrorToast(res.message);
            } else {
              this.deviceFetchingStart.emit(model.hardwareIds);
              this.alertService.showSuccessToast(res.message);
            }
          }, 0);
        },
        error: _e => {
          this.startStopFetching(id, index, false);
          this.alertService.showErrorToast('Fail to Re-Fetching Data.');
        }
      })
    );
  }

  startStopFetching(id: string, index: number, status: boolean) {
    if (id !== 'all') {
      setTimeout(() => {
        this.informationList.devices[index].isFetching = true;
      }, 0);
    } else {
      setTimeout(() => {
        this.informationList.devices.forEach(x => (x.isFetching = true));
      }, 0);
    }
  }

  fetchAllDeviceOfDate(date: string, index: number) {
    this.informationList.devicesData[index].isFetching = true;
    const model: FetchDeviceRecord = new FetchDeviceRecord();
    model.datePerformed = date;
    model.customerId = this.dataTableFilter.customerId;
    model.siteId = this.siteId;
    model.fetchType = 2;
    for (const i of this.informationList.devices) {
      if (!i.isOnlyQEDevice) {
        model.hardwareIds.push(String(i.hardwareId));
      }
    }
    model.frequency = 0;
    this.subscription.add(
      this.availabilityDataTableService.refetchDataTables(model).subscribe({
        next: (res: FetchStatus) => {
          setTimeout(() => {
            if (res.status === 500) {
              this.alertService.showErrorToast(res.message);
            } else {
              this.fetchingStart.emit(date);
              this.alertService.showSuccessToast(res.message);
            }
          }, 0);
        },
        error: e => {
          this.informationList.devicesData[index].isFetching = false;
          this.alertService.showErrorToast('Fail to Re-Fetching Data.');
        }
      })
    );
  }

  lockUnlockAll(isLock: boolean, hardwareId = null, label = null) {
    const text = isLock ? 'lock' : 'unlock';
    let hardwareText = hardwareId ? `selected hardware's` : label ? `selected date's all` : 'all';
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `Are you sure you want to ${text} ${hardwareText} records?`
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        const model: UpdateAllRecord = new UpdateAllRecord();
        model.datePerformed = this.datePipe.transform(this.dataTableFilter.datePerformed, AppConstants.fullDateFormat);
        model.customerId = this.dataTableFilter.customerId;
        model.siteId = this.siteId;
        model.isLock = isLock;
        model.label = label;
        if (hardwareId) {
          model.hardwareIds.push(String(hardwareId));
        } else {
          for (const i of this.informationList.devices) {
            model.hardwareIds.push(String(i.hardwareId));
          }
        }
        this.subscription.add(
          this.availabilityDataTableService.updateAllLockRecord(model).subscribe({
            next: (res: FetchStatus) => {
              setTimeout(() => {
                if (res.status === 500) {
                  this.alertService.showErrorToast(res.message);
                } else {
                  if (hardwareId) {
                    this.informationList.devices[this.informationList.devices.findIndex(x => x.hardwareId === hardwareId)].isLock = isLock;
                    this.informationList.devicesData.forEach(x =>
                      x.data.forEach(y => {
                        if (y.binId && y.hardwareId === hardwareId) {
                          y.isLock = isLock;
                        }
                      })
                    );
                    this.checkAllLockOrUnlock();
                    this.checkLockUnlockByLabel();
                  } else if (label) {
                    this.informationList.devicesData.forEach(x => {
                      if (x.label === label) {
                        x.isLock = isLock;
                        x.data.forEach(y => {
                          if (y.binId) {
                            y.isLock = isLock;
                          }
                        });
                      }
                    });
                    this.checkAllLockOrUnlock();
                    this.checkLockUnlockByHardware();
                  } else {
                    this.informationList.isLock = isLock;
                    this.informationList.devicesData.forEach(x =>
                      x.data.forEach(z => {
                        if (z.binId) {
                          z.isLock = isLock;
                        }
                      })
                    );
                    this.checkLockUnlockByHardware();
                    this.checkLockUnlockByLabel();
                  }
                  this.alertService.showSuccessToast(res.message);
                }
              }, 0);
            },
            error: _e => {
              this.alertService.showErrorToast('Fail to Lock-Unlock Data.');
            }
          })
        );
      }
    });
  }

  checkAllLockOrUnlock() {
    let count = 0;
    this.informationList.devicesData.forEach(x =>
      x.data.forEach(y => {
        if (y.binId && !y.isLock) {
          count++;
        }
      })
    );
    this.informationList.isLock = count === 0 ? true : false;
  }

  checkLockUnlockByHardware() {
    this.informationList.devices.forEach(a => {
      let count = 0;
      this.informationList.devicesData.forEach(x =>
        x.data.forEach(y => {
          if (y.binId && !y.isLock && a.hardwareId === y.hardwareId) {
            count++;
          }
        })
      );
      a.isLock = count === 0 ? true : false;
    });
  }

  checkLockUnlockByLabel() {
    this.informationList.devicesData.forEach(x => {
      let count = 0;
      x.data.forEach(z => {
        if (z.binId && !z.isLock) {
          count++;
        }
      });
      x.isLock = count === 0 ? true : false;
    });
  }

  isMisMatch(originalValue, newValue) {
    return originalValue !== newValue;
  }

  showLockOption(item: DataTableDeviceDataList) {
    return !item.isLock;
  }

  showUnlockOption(item: DataTableDeviceDataList) {
    return item.isLock;
  }

  updateAction(data: DataTableDeviceDataList) {
    this.loadingStart.emit(true);
    const selectedRecord: PutDataTable = new PutDataTable();
    selectedRecord.siteId = this.siteId;
    selectedRecord.binId = data.binId;
    selectedRecord.binValue = Number(data.binValue);
    selectedRecord.day = data.day;
    selectedRecord.frequency = data.frequency;
    selectedRecord.hardwareId = data.hardwareId;
    selectedRecord.isLock = !data.isLock;
    selectedRecord.month = data.month;
    selectedRecord.year = data.year;
    selectedRecord.hour = data.hour;
    selectedRecord.min = data.min;
    selectedRecord.automationSiteDetailId = data.automationSiteDetailId;
    selectedRecord.qEDeviceId = data.qEDeviceId;
    this.subscription.add(
      this.availabilityDataTableService.putAllDataTableInformation(selectedRecord).subscribe({
        next: (res: EditHistoryData) => {
          this.informationList.devices.forEach(a => {
            this.informationList.devicesData.forEach(x =>
              x.data.forEach(y => {
                if (y.binId === res.dtData.binId) {
                  y.isLock = selectedRecord.isLock;
                }
              })
            );
          });
          this.alertService.showSuccessToast('Lock/Unlock operations performed successfully');
          this.loadingStart.emit(false);
        },
        error: _e => {
          this.loadingStart.emit(false);
        }
      })
    );
  }
}
