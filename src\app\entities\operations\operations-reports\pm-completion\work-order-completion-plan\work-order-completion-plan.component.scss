.workOrderPlanInfo {
  .primary-color {
    color: #3366ff;
  }

  .pm-completion-numbers {
    flex-wrap: nowrap;
    .selectable-div {
      flex: 1;
      box-sizing: border-box;
      justify-content: space-around;
      min-height: 70px;
      min-width: 150px;
      &.active {
        border-radius: 2px;
        outline: 2px solid #3366ff;
      }
    }
  }

  .table-responsive {
    border-radius: 8px;
  }
  .table {
    width: 100%;
    max-width: 100%;
  }
  .pm-completion-chart {
    ::ng-deep {
      table thead tr th {
        border: none;
        text-align: center;
      }
      .table tbody tr td {
        border: none;
        text-align: center;
      }
      .table {
        border: none;
      }
    }
  }

  .chart-box {
    box-shadow: 0 0 7px 2px #151a30;
    border-radius: 9px;

    .chart-height-150 {
      height: 150px;
    }
  }

  .wo-table-header {
    min-height: 40px;
  }

  .rescheduleWorkOrder-table {
    td,
    th {
      height: 40px;
    }
    .clear-icon {
      color: #999;
      right: 10px;
      top: 7px;
      font-size: 20px;
      &:hover {
        color: red;
      }
    }
    .search-textbox {
      padding-right: 1.75rem;
    }
  }
}

::ng-deep .nb-theme-dark .rescheduleWorkOrder-table {
  thead {
    tr {
      color: #fff;
      &:nth-child(2) {
        color: #fff;
        background-color: #2c3753;
        th {
          background-color: #192038;
        }
      }
      th {
        color: #fff;
        border: 1px solid #2c3753;
      }
    }
  }

  .custom-checkbox {
    cursor: pointer;
  }
}
