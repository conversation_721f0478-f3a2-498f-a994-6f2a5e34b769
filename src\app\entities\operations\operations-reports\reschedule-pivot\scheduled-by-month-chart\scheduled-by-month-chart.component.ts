import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';

@Component({
  selector: 'sfl-scheduled-by-month-chart',
  templateUrl: './scheduled-by-month-chart.component.html',
  styleUrls: ['./scheduled-by-month-chart.component.scss']
})
export class ScheduledByMonthChartComponent implements OnInit, OnChanges {
  @Input() byMonthChartConfig: any = {};
  @Input() RescheduleBy: string;
  chartsData: any;
  currentTheme = 'dark';
  constructor() {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.byMonthChartConfig && changes.byMonthChartConfig.currentValue) {
      const data = {
        title: {
          text: `Reschedules By ${this.RescheduleBy}`,
          left: 'center',
          top: 45,
          textStyle: {
            fontSize: '0.9375rem'
          }
        },
        color: ['#4472C4'],
        grid: {
          y: 100,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            showContent: true
          }
        },
        legend: {
          data: [],
          type: 'scroll',
          bottom: 10
        },
        toolbox: {
          show: true,
          orient: 'horizontal',
          feature: {
            dataZoom: { show: true },
            dataView: {
              show: true,
              readOnly: true,
              optionToContent: opt => {
                let series = opt.series;
                let axisData = opt.xAxis[0]['data'];

                let table = '<table id="site-performance-table" class="table table-bordered"><thead><tr>' + '<th>Month</th>';
                for (let i = 0, l = series.length; i < l; i++) {
                  table += `<th>${series[i].name}</th>`;
                }
                table += '</tr></thead><tbody style="color:#000000">';
                for (let i = 0, l = series[0].data.length; i < l; i++) {
                  table += '<tr>';
                  table += `<td style="color:#000000">${axisData[i]}</td>`;
                  for (let j = 0, m = series.length; j < m; j++) {
                    table += `<td style="text-align:right; color:#000000;">${series[j].data[i].toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}</td>`;
                  }
                  table += '</tr>';
                }
                table += '</thead></table>';
                return table;
              }
            },
            restore: {},
            saveAsImage: {}
          },
          top: 7,
          right: 40
        },
        xAxis: [
          {
            type: 'category'
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: `Reschedules ${this.RescheduleBy}`,
            nameTextStyle: {
              fontWeight: 'bolder',
              align: 'right',
              padding: [0, 6, 0, 0]
            }
          }
        ],
        series: []
      };
      data.legend.data = this.byMonthChartConfig['dataOf'];
      data.xAxis[0]['data'] = this.byMonthChartConfig['labels'];

      this.byMonthChartConfig['eChartObjects']?.forEach((item, index) => {
        data.series.push({
          ...item,
          type: 'bar',
          emphasis: {
            focus: 'series'
          },
          barGap: 0,
          tooltip: {
            valueFormatter: function (value: any) {
              return (value as number).toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            }
          }
        });

        if (this.byMonthChartConfig['eChartObjects']?.length - 1 === index) this.chartsData = data;
      });
    }
  }
  ngOnInit(): void {}
}
