import { Component, OnInit, Renderer2, TemplateRef, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { SecurityQuestionAnswer, SecurityQuestionItem, User } from '../../@shared/models/user.model';
import { forkJoin, Subscription } from 'rxjs';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { StorageService } from '../../@shared/services/storage.service';
import { Router } from '@angular/router';
import { AuthService } from '../../@auth';
import { ProfileService } from '../profile/profile.service';
import { ConfirmPassword } from '../../@shared/models/resetPassword.model';
import { AlertService } from '../../@shared/services';
import { APP_ROUTES, AppConstants } from '../../@shared/constants';
import { UserService } from '../user-management/user.service';

@Component({
  selector: 'sfl-user-authentication',
  templateUrl: './user-authentication.component.html',
  styleUrls: ['./user-authentication.component.scss']
})
export class UserAuthenticationComponent implements OnInit {
  @ViewChild('securityQuesAnsButton', { static: true }) securityQuesAnsButton;
  @ViewChild('changePasswordButton', { static: true }) changePasswordButton;
  @ViewChild('changePassForm') changePassForm!: NgForm;
  @ViewChild('securityQuesAnsForm') securityQuesAnsForm!: NgForm;

  subscription: Subscription = new Subscription();
  loading = false;
  userSecurityQuestionAnswer: SecurityQuestionAnswer[] = Array.from({ length: 3 }, () => new SecurityQuestionAnswer({}));
  clonedUserSecurityQuestionAnswer: Partial<SecurityQuestionAnswer>[] = Array.from({ length: 3 }, () => new SecurityQuestionAnswer({}))
    .filter(item => item.questionID)
    .map(item => ({ questionID: item.questionID, questionAnswerId: item.questionAnswerId }));
  changePWDObj: ConfirmPassword = new ConfirmPassword();
  modalRef: BsModalRef;
  cloneSecurityQuestionList: SecurityQuestionItem[] = [];
  securityQuestionList: SecurityQuestionItem[] = [];

  constructor(
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    private readonly router: Router,
    private readonly profileService: ProfileService,
    private renderer: Renderer2,
    private readonly authService: AuthService,
    private readonly alertService: AlertService,
    private readonly userService: UserService
  ) {}

  ngOnInit(): void {
    const userId = Number(this.storageService.get('userID'));
    const isForcedToChangePassword = Boolean(this.storageService.get(AppConstants.isForcedToChangePasswordKey));
    const isRequiredSecurityQuestion = Boolean(this.storageService.get(AppConstants.userKey).isRequiredSecurityQuestion);

    if ((isForcedToChangePassword && !isRequiredSecurityQuestion) || (isForcedToChangePassword && isRequiredSecurityQuestion)) {
      setTimeout(() => {
        this.changePasswordButton.nativeElement.click();
        return;
      }, 1);
    }

    if (isRequiredSecurityQuestion && !isForcedToChangePassword) {
      setTimeout(() => {
        if (userId !== 0) {
          this.getUserSecurityQuestionsById(userId);
        }
        this.getSecurityQuestionList();
        this.securityQuesAnsButton.nativeElement.click();
        return;
      }, 1);
    }
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {
      keyboard: false,
      ignoreBackdropClick: true,
      backdrop: true,
      class: 'user-authentication-modal-dialog'
    });
    this.addCustomClassToModal();
  }

  private addCustomClassToModal() {
    const modalElement = document.querySelector('.modal');
    if (modalElement?.querySelector('.user-authentication-modal-dialog')) {
      this.renderer.setStyle(modalElement, 'z-index', '99999');
    }
  }

  getSecurityQuestionList(): void {
    this.loading = true;
    this.authService.getSecurityQuestionList().subscribe({
      next: (res: any) => {
        this.securityQuestionList = res;
        this.cloneSecurityQuestionList = res;
        setTimeout(() => {
          this.loading = false;
        }, 1000);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getUserSecurityQuestionsById(id): void {
    this.loading = true;
    this.subscription.add(
      this.userService.getUserSecurityQuestionsById(id).subscribe({
        next: (res: SecurityQuestionAnswer[]) => {
          this.userSecurityQuestionAnswer = this.userService.setUserSecurityQuestionAnswerByLength(id, res);
          this.clonedUserSecurityQuestionAnswer = this.userService
            .setUserSecurityQuestionAnswerByLength(id, res)
            .filter(item => item.questionID)
            .map(item => ({ questionID: item.questionID, questionAnswerId: item.questionAnswerId }));
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  isQuesAnsItemPresent(quesAnsItem: SecurityQuestionAnswer, index: number): Partial<SecurityQuestionAnswer> {
    return this.clonedUserSecurityQuestionAnswer.find(
      item =>
        item.questionID !== null &&
        (item.questionAnswerId !== null || item.questionAnswerId !== 0) &&
        item.questionID === quesAnsItem.questionID
    );
  }

  onSecurityQuestionChange(quesAnsItem: SecurityQuestionAnswer, index: number): void {
    const presentQuesAnsItem = this.isQuesAnsItemPresent(quesAnsItem, index);
    quesAnsItem.questionAnswerStr = '';
    quesAnsItem.questionAnswerId = presentQuesAnsItem?.questionAnswerId ?? 0;
  }

  isSecurityQuesAnsRequired(quesAnsItem: SecurityQuestionAnswer, index: number): boolean {
    const presentQuesAnsItem = this.isQuesAnsItemPresent(quesAnsItem, index);
    return !(presentQuesAnsItem && presentQuesAnsItem?.questionAnswerId && quesAnsItem.questionID) && !!quesAnsItem.questionID;
  }

  setSecurityQuestionList(index: number, securityQuestionAnswerList: SecurityQuestionAnswer[]): void {
    const usedQuestionIdList = securityQuestionAnswerList.map(item => item.questionID);
    this.securityQuestionList = this.cloneSecurityQuestionList.filter(item => !usedQuestionIdList.includes(item.questionID));
  }

  addUpdateUserSecurityQuestionsById(securityQuesAnsForm: NgForm): void {
    this.loading = true;
    this.subscription.add(
      this.userService.addUpdateUserSecurityQuestionsById(this.userSecurityQuestionAnswer).subscribe({
        next: (res: any) => {
          this.alertService.showSuccessToast(res.message);
          setTimeout(() => {
            securityQuesAnsForm.reset();
            this.loading = false;
            if (this.modalRef) {
              this.modalRef.hide();
            }
            const user = this.storageService.get(AppConstants.userKey);
            user.isRequiredSecurityQuestion = false;
            this.storageService.set('user', user);
          }, 1000);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  saveSecurityQuesAnsList(securityQuesAnsForm: NgForm): void {
    if (securityQuesAnsForm.form.valid) {
      this.addUpdateUserSecurityQuestionsById(securityQuesAnsForm);
    } else {
      securityQuesAnsForm.form.markAllAsTouched();
    }
  }

  changePWDAPICall(changePassForm: NgForm): void {
    this.loading = true;
    this.subscription.add(
      this.profileService.changePassword(this.changePWDObj).subscribe({
        next: res => {
          if (res) {
            if (res.message === 'Password Change Sucessfully') {
              this.alertService.showSuccessToast(res.message);
              setTimeout(() => {
                changePassForm.reset();
                this.loading = false;
                if (this.modalRef) {
                  this.modalRef.hide();
                }
                this.router.navigateByUrl(APP_ROUTES.LOGIN);
              }, 1000);
            } else {
              this.alertService.showErrorToast(res.message);
              this.loading = false;
            }
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  changePWD(changePassForm: NgForm): void {
    if (changePassForm.form.valid) {
      this.changePWDAPICall(changePassForm);
    } else {
      changePassForm.form.markAllAsTouched();
    }
  }
}
