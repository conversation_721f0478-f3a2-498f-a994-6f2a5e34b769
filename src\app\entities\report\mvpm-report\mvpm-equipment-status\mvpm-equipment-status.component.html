<nb-accordion>
  <nb-accordion-item *ngFor="let jhaData of masterReportDataList" [expanded]="jhaData?.isFinal" class="border-bottom">
    <nb-accordion-item-header class="accordion_head flex-wrap">
      <h5>
        <label class="cursor-pointer px-2 mb-1">{{ jhaData?.isFinal ? '' : (jhaData?.order | number : '2.') }}</label>
      </h5>
      <h5>
        <label class="cursor-pointer px-2 mb-1">{{ jhaData?.isFinal ? 'Final' : jhaData?.reporterName }}</label>
      </h5>
      <label class="float-end ms-2 bg-light p-2 mb-1">
        Created on
        {{ jhaData?.reportCreatedOnStr | date : fullDateFormat }}
      </label>
      <label class="float-end ms-2 bg-light p-2 mb-1" *ngIf="!jhaData?.isFinal">
        Uploaded on
        {{ jhaData?.reportUploadOnStr | date : fullDateFormat }}
      </label>
    </nb-accordion-item-header>
    <nb-accordion-item-body>
      <div class="d-flex align-items-center">
        <h6 class="w-100">Equipment Status Summary</h6>
        <div class="row d-flex align-items-center">
          <div class="ms-auto d-flex button_list">
            <button
              nbButton
              size="medium"
              *ngIf="jhaData?.isFinal && !viewdeletetedbutton"
              status="primary"
              (click)="onClick(null, jhaData?.isFinal, true, jhaData?.reportGuid)"
              class="nc-addbutton mb-2"
              nbTooltip="Add Equipment Status"
              nbTooltipStatus="primary"
              nbTooltipPlacement="top"
            >
              <em class="fa-solid fa-plus" aria-hidden="true"></em>
            </button>
          </div>
        </div>
      </div>
      <div class="col-sm-12 col-md-12 col-lg-12 m-t-sm" *ngFor="let ncitem of jhaData?.uploadEquipmentTransDevices; let ncIndex = index">
        <div class="d-flex heading_tittle">
          <div class="d-flex heading_section">
            <label class="fw-bold m-2">Equipment Transformer Status</label>
          </div>
        </div>
        <div class="col-md-12 text-end m-t-sm">
          <a
            class="listgrid-icon px-1 text-primary"
            (click)="onClick(ncitem, jhaData?.isFinal, false, jhaData?.reportGuid, jhaData?.componentList)"
          >
            <em
              *ngIf="jhaData?.isFinal && !viewdeletetedbutton"
              class="fa fa-edit"
              nbTooltip="Edit"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
            ></em>
          </a>

          <a class="text-danger px-1 listgrid-icon" (click)="onDelete(ncitem?.esGuid, ncIndex)" *ngIf="!viewdeletetedbutton">
            <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
          </a>
        </div>
        <div class="row">
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Device Type</label>
              <div>{{ ncitem?.deviceTypeName }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-address">{{ ncitem?.deviceTypeName }} Name</label>
              <div>{{ ncitem?.siteDeviceName }}</div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="label col-md-12" for="input-address">Photos</div>
          <div *ngFor="let item of ncitem?.equipmentStatusImages | orderBy : 'order'; let i = index" class="col-md-3 m-t-sm">
            <img
              [src]="item?.thumbnailUrl || item?.imageUrl"
              alt="Equipment Status Image"
              class="text-center img-thumbnail img-responsive siteImage cursor-pointer"
              onError="this.src='assets/images/no-image-found.jpg'"
              (click)="imagePopup(i, ncitem)"
            />
          </div>
        </div>

        <div class="row">
          <div class="col-12">
            <label class="fw-bold m-t-sm">Transformer Status</label>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Sample Valve</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.sampleValve === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">External</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.sampleValveExternal === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Nitrogen Fill Valve</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.nitrogenFillValve === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">External</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.nitrogenFillValveExternal === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Pressure Gauge</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.pressureGauge === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">External</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.pressureGaugeExternal === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Temperature Gauge</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.temperatureGauge === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">External</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.temperatureGaugeExternal === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Max Temp Reset</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.maxTempReset === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Liquid Level Gauge</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.liquidLevelGauge === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Liquid Level</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.liquidLevel }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Top Oil Temperature</label>
              <div>
                {{ ncitem?.transformerEquipmentStatus?.topOilTemperature }}
                <span *ngIf="ncitem?.transformerEquipmentStatus?.topOilTemperature" class="ms-1">°C</span>
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Max Oil Temperature</label>
              <div>
                {{ ncitem?.transformerEquipmentStatus?.maxOilTemperature }}
                <span *ngIf="ncitem?.transformerEquipmentStatus?.maxOilTemperature" class="ms-2">°C</span>
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Pressure As found</label>
              <div>
                {{ ncitem?.transformerEquipmentStatus?.pressureAsFound
                }}<span *ngIf="ncitem?.transformerEquipmentStatus?.pressureAsFound" class="ms-1">psi</span>
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Pressure As Left</label>
              <div>
                {{ ncitem?.transformerEquipmentStatus?.pressureAsLeft
                }}<span *ngIf="ncitem?.transformerEquipmentStatus?.pressureAsLeft" class="ms-1">psi</span>
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Detc Position</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.detcPosition }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Live Sample</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.liveSample === true ? 'Yes' : 'No' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Grounding Test Results</label>
              <div>{{ ncitem?.transformerEquipmentStatus?.groundingTestResults }}</div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="label col-md-12" for="input-address">Nameplate Photo</div>
          <div *ngIf="ncitem?.transformerEquipmentStatus?.nameplateImageURL" class="col-md-3 m-t-sm">
            <img
              [src]="ncitem?.transformerEquipmentStatus?.nameplateImageURL"
              alt="Equipment Status Image"
              class="text-center img-thumbnail img-responsive siteImage cursor-pointer"
              onError="this.src='assets/images/no-image-found.jpg'"
            />
          </div>
        </div>
      </div>

      <div
        class="col-sm-12 col-md-12 col-lg-12 m-t-sm"
        *ngFor="let equipmentMVDevicesList of jhaData?.uploadEquipmentMVDevices; let i = index"
      >
        <div class="d-flex heading_tittle">
          <div class="d-flex heading_section">
            <label class="fw-bold m-2">Equipment MV Disconnect Status</label>
          </div>
        </div>
        <div class="col-md-12 text-end m-t-sm">
          <a
            class="listgrid-icon px-1 text-primary"
            (click)="onClick(equipmentMVDevicesList, jhaData?.isFinal, false, jhaData?.reportGuid, jhaData?.componentList)"
          >
            <em
              *ngIf="jhaData?.isFinal && !viewdeletetedbutton"
              class="fa fa-edit"
              nbTooltip="Edit"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
            ></em>
          </a>

          <a class="text-danger px-1 listgrid-icon" (click)="onDelete(equipmentMVDevicesList?.esGuid, i)" *ngIf="!viewdeletetedbutton">
            <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
          </a>
        </div>
        <div class="row">
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Device Type</label>
              <div>{{ equipmentMVDevicesList?.deviceTypeName }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-address">{{ equipmentMVDevicesList?.deviceTypeName }} Name</label>
              <div>{{ equipmentMVDevicesList?.siteDeviceName }}</div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="label col-md-12" for="input-address">Photos</div>
          <div
            *ngFor="let item of equipmentMVDevicesList?.equipmentStatusImages | orderBy : 'order'; let i = index"
            class="col-md-3 m-t-sm"
          >
            <img
              [src]="item?.thumbnailUrl || item?.imageUrl"
              alt="Equipment Status Image"
              class="text-center img-thumbnail img-responsive siteImage cursor-pointer"
              onError="this.src='assets/images/no-image-found.jpg'"
              (click)="imagePopup(i, equipmentMVDevicesList)"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <label class="fw-bold m-t-sm">MV Disconnected Status</label>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">SF6 Level</label>
              <div>{{ equipmentMVDevicesList?.mvDisconnectedStatus?.sF6Level || 'N/A' }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Voltage Indicator Functional Level</label>
              <div>
                {{
                  equipmentMVDevicesList?.mvDisconnectedStatus?.voltageIndicatorFunctional === 1
                    ? 'Yes'
                    : equipmentMVDevicesList?.mvDisconnectedStatus?.voltageIndicatorFunctional === 0
                    ? 'No'
                    : 'N/A'
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Liquid Level</label>
              <div>{{ equipmentMVDevicesList?.mvDisconnectedStatus?.liquidLevel || 'N/A' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="col-sm-12 col-md-12 col-lg-12 m-t-sm"
        *ngFor="let equipmentPRDevicesList of jhaData?.uploadEquipmentPRDevices; let i = index"
      >
        <div class="d-flex heading_tittle">
          <div class="d-flex heading_section">
            <label class="fw-bold m-2">Equipment Protective Relay Status</label>
          </div>
        </div>
        <div class="col-md-12 text-end m-t-sm">
          <a
            class="listgrid-icon px-1 text-primary"
            (click)="onClick(equipmentPRDevicesList, jhaData?.isFinal, false, jhaData?.reportGuid, jhaData?.componentList)"
          >
            <em
              *ngIf="jhaData?.isFinal && !viewdeletetedbutton"
              class="fa fa-edit"
              nbTooltip="Edit"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
            ></em>
          </a>

          <a class="text-danger px-1 listgrid-icon" (click)="onDelete(equipmentPRDevicesList?.esGuid, i)" *ngIf="!viewdeletetedbutton">
            <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
          </a>
        </div>
        <div class="row">
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Device Type</label>
              <div>{{ equipmentPRDevicesList?.deviceTypeName }}</div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-address">{{ equipmentPRDevicesList?.deviceTypeName }} Name</label>
              <div>{{ equipmentPRDevicesList?.siteDeviceName }}</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="label col-md-12" for="input-address">Photos</div>
          <div
            *ngFor="let item of equipmentPRDevicesList?.equipmentStatusImages | orderBy : 'order'; let i = index"
            class="col-md-3 m-t-sm"
          >
            <img
              [src]="item?.thumbnailUrl || item?.imageUrl"
              alt="Equipment Status Image"
              class="text-center img-thumbnail img-responsive siteImage cursor-pointer"
              onError="this.src='assets/images/no-image-found.jpg'"
              (click)="imagePopup(i, equipmentMVDevicesList)"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <label class="fw-bold m-t-sm">Protective Relay Status</label>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Relay 3 Phase Open Test</label>
              <div>
                {{
                  equipmentPRDevicesList?.protectiveRelayStatus?.relay3PhaseOpenTest === true
                    ? 'Pass'
                    : equipmentPRDevicesList?.protectiveRelayStatus?.relay3PhaseOpenTest === null
                    ? 'N/A'
                    : 'Fail'
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Relay Loss Of Power Test</label>
              <div>
                {{
                  equipmentPRDevicesList?.protectiveRelayStatus?.relayLossOfPowerTest === true
                    ? 'Pass'
                    : equipmentPRDevicesList?.protectiveRelayStatus?.relayLossOfPowerTest === null
                    ? 'N/A'
                    : 'Fail'
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Relay Internal Self Test</label>
              <div>
                {{
                  equipmentPRDevicesList?.protectiveRelayStatus?.relayInternalSelfTest === true
                    ? 'Pass'
                    : equipmentPRDevicesList?.protectiveRelayStatus?.relayInternalSelfTest === null
                    ? 'N/A'
                    : 'Fail'
                }}
              </div>
            </div>
          </div>

          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Replace Date</label>
              <div>
                {{
                  equipmentPRDevicesList?.protectiveRelayStatus?.replaceDate
                    ? (equipmentPRDevicesList?.protectiveRelayStatus?.replaceDate | dateToUsersTimezone : userDateFormat)
                    : 'N/A'
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-4 mb-3">
            <div class="form-control-group">
              <label class="label" for="input-city">Relay Calibration Date</label>
              <div>
                {{
                  equipmentPRDevicesList?.protectiveRelayStatus?.relayCalibrationDate
                    ? (equipmentPRDevicesList?.protectiveRelayStatus?.relayCalibrationDate | dateToUsersTimezone : userDateFormat)
                    : 'N/A'
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </nb-accordion-item-body>
  </nb-accordion-item>
</nb-accordion>
<div
  *ngIf="
    !masterReportDataList[0]?.uploadEquipmentTransDevices ||
    !masterReportDataList[0]?.uploadEquipmentPRDevices ||
    !masterReportDataList[0]?.uploadEquipmentMVDevices
  "
  class="text-center"
>
  <label class="fw-bold">Equipment status data not found!</label>
</div>
