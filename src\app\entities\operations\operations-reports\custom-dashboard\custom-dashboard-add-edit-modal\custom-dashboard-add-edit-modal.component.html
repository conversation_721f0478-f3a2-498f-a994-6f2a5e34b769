<div class="modal-content" id="exclusion" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header ModalBody">
    <div class="container">
      <div class="row">
        <div class="col-8 ps-0">
          <h6>{{ operationType }} Custom Dashboard</h6>
        </div>
        <div class="col-4 text-end pe-0">
          <button type="button" class="close" aria-label="Close" (click)="closeModal()">
            <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
          </button>
        </div>
      </div>
    </div>
  </div>
  <form [formGroup]="dashboardForm" (ngSubmit)="onSave()">
    <div class="modal-body ModalBody add-device-modal-body">
      <div class="row">
        <div class="col-12 mb-2">
          <label class="label" for="input-address">
            Dashboard Name
            <span class="ms-1 text-danger">*</span>
          </label>
          <input
            nbInput
            name="menuTitle"
            class="form-control"
            fullWidth
            placeholder="Dashboard Name"
            formControlName="menuTitle"
            noLeadingSpace
          />
          <sfl-error-msg [control]="dashboardForm.controls.menuTitle" fieldName="Dashboard Name"></sfl-error-msg>
        </div>
        <div class="col-12 mb-2">
          <label class="label" for="input-address">
            Content
            <span class="ms-1 text-danger">*</span>
          </label>
          <textarea
            nbInput
            name="content"
            class="form-control"
            fullWidth
            placeholder="Content"
            formControlName="content"
            rows="5"
            cols="40"
            noLeadingSpace
          >
          </textarea>
          <sfl-error-msg [control]="dashboardForm.controls.content" fieldName="Content"></sfl-error-msg>
        </div>
      </div>
    </div>
    <div class="modal-footer ModalFooter">
      <button nbButton status="primary" size="medium" id="deviceSubmit" type="submit" [disabled]="dashboardForm.invalid">Save</button>
      <button nbButton status="basic" size="medium" type="button" (click)="closeModal()">Cancel</button>
    </div>
  </form>
</div>
