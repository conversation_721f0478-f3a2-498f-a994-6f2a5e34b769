import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { QEAnalyticsComponent } from './qe-analytics/qe-analytics.component';
import { QEAnalyticsPasswordComponent } from './qe-analytics-password/qe-analytics-password.component';
import { QEAnalyticsPasswordGuard } from './services/qe-analytics-password.guard';
import { QE_MENU_MODULE_ENUM } from '../../@shared/enums/qe-menu.enum';
import { QEAnalyticsGatheringGuard } from './services/qe-analytics-gathering.guard';

const routes: Routes = [
  {
    path: '',
    component: QEAnalyticsComponent,
    canActivate: [QEAnalyticsPasswordGuard, QEAnalyticsGatheringGuard],
    canDeactivate: [QEAnalyticsGatheringGuard],
    data: {
      pageTitle: 'Analytics',
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_ANALYTICS,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
    }
  },
  {
    path: 'password',
    component: QEAnalyticsPasswordComponent,
    data: { pageTitle: 'Analytics Password' }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class QEAnalyticsRoutingModule {}
