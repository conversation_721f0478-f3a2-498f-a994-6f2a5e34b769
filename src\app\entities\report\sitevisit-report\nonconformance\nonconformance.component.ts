import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NbDialogService } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AppConstants } from '../../../../@shared/constants';
import {
  AllReportModel,
  MasterNonConformanceDto,
  MasterReportModel,
  NonConformanceImagesById
} from '../../../../@shared/models/report.model';
import { AlertService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { DialogComponent } from '../../../modal-overlays/dialog/dialog.component';
import { ModelComponent } from '../../model/model.component';
import { ReportService } from '../../report.service';
import { AddEditNonconformanceComponent } from './add-edit-nonconformance/add-edit-nonconformance.component';
import { NcSiteReportImagesComponent } from './nc-site-report-images/nc-site-report-images.component';
import { NcUserSiteReportImagesComponent } from './nc-user-site-report-images/nc-user-site-report-images.component';

@Component({
  selector: 'sfl-nonconformance',
  templateUrl: './nonconformance.component.html',
  styleUrls: ['./nonconformance.component.scss']
})
export class NonconformanceComponent implements OnInit, OnDestroy {
  @Input() masterReportDataList: MasterReportModel[];
  @Input() reportDetails: AllReportModel;
  @Output() siteReportImagesCloseEvent: EventEmitter<Boolean> = new EventEmitter(false);
  loading = false;
  subscription: Subscription = new Subscription();
  modalRef: BsModalRef;
  mergeModalRef: BsModalRef;
  workorderId: number;
  reportMasterList: AllReportModel;
  report: MasterReportModel;
  workOrderId: number;
  reportId: string;
  @Output() nCOrderChange = new EventEmitter();
  @Output() ncItemAddEditChange = new EventEmitter();
  viewdeletetedbutton = false;
  @ViewChild('openModalButton', { static: true }) openModalButton;
  selectedData: number;
  items = [];
  mergeComponentObj = {
    newNCGuid: '',
    removeGuid: []
  };
  selectedComponentStr: string;
  isMerge = false;
  fullDateFormat = AppConstants.fullDateFormat;

  constructor(
    private readonly reportService: ReportService,
    private readonly modalService: BsModalService,
    private readonly route: ActivatedRoute,
    private readonly dialogService: NbDialogService,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit() {
    this.route.params.subscribe(params => {
      if (params && Number(params.id)) {
        this.workorderId = params.id;
        this.viewdeletetedbutton = params.viewdeletetedbutton === 'true' ? true : false;
      }
    });
  }

  // Get JHA Data by ID
  getById(id) {
    this.loading = true;
    this.subscription.add(
      this.reportService.getDataById(this.workorderId, 'Order').subscribe({
        next: (res: AllReportModel) => {
          this.loading = false;
          this.reportMasterList = res;
          this.masterReportDataList = this.reportMasterList.reports;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  @Input() set nonconformanceList(ncList: MasterReportModel[]) {
    this.masterReportDataList = ncList;
    if (ncList.length) {
      this.report = ncList.find(report => report.isFinal === true);
      this.reportId = this.report.reportGuid;
    }
  }

  redirectToNcImage() {
    this.storageService.set('reportGuid', this.reportId);
  }

  // Non-Conformance
  dropNonConformance(event: CdkDragDrop<string[]>) {
    let i = 1;
    moveItemInArray(this.masterReportDataList[0].nonConformances, event.previousIndex, event.currentIndex);
    for (const item of this.masterReportDataList[0].nonConformances) {
      item.order = i++;
    }
    this.masterReportDataList[0].ncOrderChange = true;
    this.nCOrderChange.emit(this.masterReportDataList[0].ncOrderChange);
  }

  // Sort by component
  sortbyComponent() {
    let i = 1;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Non-Conformance list reorder based on components.'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.masterReportDataList[0].nonConformances.sort((a, b) => (a.componentStr > b.componentStr ? 1 : -1));
        for (const item of this.masterReportDataList[0].nonConformances) {
          item.order = i++;
        }
        this.masterReportDataList[0].ncOrderChange = true;
        this.nCOrderChange.emit(this.masterReportDataList[0].ncOrderChange);
      }
    });
  }

  // edit popup
  onClick(ncItem, isFinal, isAdd, reportGuid, componentList) {
    const activeComponent = componentList.filter(item => item.isActive);
    if (ncItem) {
      if (!isAdd && ncItem && ncItem.isComponentDeleted) {
        const foundDeletedCmp = activeComponent.find(item => item.id === ncItem.component);
        if (!foundDeletedCmp) {
          activeComponent.push({
            id: ncItem.component,
            name: `${ncItem.componentStr} (Deleted)`
          });
        }
      } else if (!isAdd && ncItem && !ncItem.isComponentActive) {
        const foundActiveCmp = activeComponent.find(item => item.id === ncItem.component);
        if (!foundActiveCmp) {
          activeComponent.push({
            id: ncItem.component,
            name: `${ncItem.componentStr} (In-Active)`
          });
        }
      }
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-lg',
        initialState: {
          editNC: JSON.parse(JSON.stringify(ncItem)),
          selectedisFinal: isFinal,
          componentList: activeComponent,
          assessmentType: this.reportDetails.assesmentType,
          issueList: this.reportDetails.ncIssues.filter(item => item.isActive),
          actionList: this.reportDetails.ncActions.filter(item => item.isActive),
          viewdeletetedbutton: this.viewdeletetedbutton
        }
      };
      this.modalRef = this.modalService.show(AddEditNonconformanceComponent, ngModalOptions);

      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.getById(this.workorderId);
          this.ncItemAddEditChange.emit(true);
        }
        this.loading = false;
      });
    } else {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-lg',
        initialState: {
          editNC: new MasterNonConformanceDto(),
          selectedisFinal: isFinal,
          isAdd: isAdd,
          reportGuid: reportGuid,
          componentList: activeComponent,
          assessmentType: this.reportDetails.assesmentType,
          issueList: this.reportDetails.ncIssues.filter(item => item.isActive),
          actionList: this.reportDetails.ncActions.filter(item => item.isActive),
          viewdeletetedbutton: this.viewdeletetedbutton
        }
      };
      this.modalRef = this.modalService.show(AddEditNonconformanceComponent, ngModalOptions);

      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.getById(this.workorderId);
          this.ncItemAddEditChange.emit(true);
        }
        this.loading = false;
      });
    }
  }

  open() {
    this.dialogService
      .open(DialogComponent, {
        hasBackdrop: true,
        closeOnBackdropClick: false
      })
      .onClose.subscribe(() => {
        this.getById(this.workorderId);
      });
  }

  // Open Site Image popup
  onClickOnSiteImage(ncItem, isFinal, ncList, siteImages, ncCoOrdinates) {
    if (siteImages.length > 0) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'large-modal',
        initialState: {
          ncItem: ncItem,
          selectedisFinal: isFinal,
          reportGuid: ncItem.reportGuid,
          ncList: ncList,
          workorderId: this.workorderId,
          siteImages: JSON.parse(JSON.stringify(siteImages)),
          ncCoOrdinates: ncCoOrdinates,
          viewdeletetedbutton: this.viewdeletetedbutton
        }
      };
      this.modalRef = this.modalService.show(NcSiteReportImagesComponent, ngModalOptions);
      this.modalRef.onHide.subscribe(res => {
        if (res) {
          this.siteReportImagesCloseEvent.emit(true);
        }
      });
    } else {
      this.alertService.showErrorToast('No sitemap available!');
    }
  }

  // Single User Report Image
  onClickReportSiteImage(ncItem, siteReportImages) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'large-modal',
      initialState: {
        ncItem: ncItem,
        reportGuid: this.reportId,
        workorderId: this.workorderId,
        reportSiteImages: siteReportImages
      }
    };
    this.modalRef = this.modalService.show(NcUserSiteReportImagesComponent, ngModalOptions);
  }

  // Display Final SiteMap Image
  onClickFinalSiteImage(reportSiteImages) {
    if (reportSiteImages.length > 0) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        class: 'modal-lg',
        initialState: {
          imageList: reportSiteImages
        }
      };
      this.modalRef = this.modalService.show(ModelComponent, ngModalOptions);
    } else {
      this.alertService.showErrorToast('No site map available!');
    }
  }

  onDelete(ncGuid, i) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete this non-conformance item?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.subscription.add(
          this.reportService.deleteNonConformance(ncGuid).subscribe({
            next: res => {
              if (res) {
                this.alertService.showSuccessToast(res.message);
                for (const [index, item] of this.masterReportDataList[0].nonConformances.entries()) {
                  if (index > i) {
                    item.order = index;
                  }
                }
                this.masterReportDataList[0].nonConformances.splice(i, 1);
                this.masterReportDataList[0].ncOrderChange = true;
                this.nCOrderChange.emit(this.masterReportDataList[0].ncOrderChange);
              }
            }
          })
        );
      }
    });
  }

  mergeComponentCheck() {
    if (this.isMerge) {
      let count = 0;
      let otherComponentSelect = false;
      for (const i of this.masterReportDataList[0].nonConformances) {
        if (i.isSelected) {
          count++;
          if (this.selectedData && this.selectedData !== i.component) {
            otherComponentSelect = true;
            break;
          } else {
            this.selectedData = i.component;
            this.selectedComponentStr = i.componentStr;
            this.items.push(i.order);
          }
        }
      }
      if (otherComponentSelect) {
        this.alertService.showErrorToast('Only same components are allow to merge.');
      } else if (count === 0) {
        this.isMerge = false;
      } else if (count < 2) {
        this.alertService.showErrorToast('Minimum two records need to perform merge.');
      } else {
        this.openModalButton.nativeElement.click();
      }
    } else {
      this.isMerge = true;
    }
  }

  openMergeModal(template: TemplateRef<any>) {
    this.mergeModalRef = this.modalService.show(template, { keyboard: false, ignoreBackdropClick: true });
  }

  mergeComponent() {
    this.loading = true;
    let firstIndex = null;
    let count = 0;
    for (const [index, value] of this.masterReportDataList[0].nonConformances.entries()) {
      if (value.component === this.selectedData && value.isSelected) {
        if (firstIndex === null) {
          firstIndex = index;
          this.mergeComponentObj.newNCGuid = value.ncGuid;
        } else {
          count++;
          this.masterReportDataList[0].nonConformances[firstIndex].images = this.mergeImages(
            this.masterReportDataList[0].nonConformances[firstIndex].images,
            value.images,
            this.masterReportDataList[0].nonConformances[firstIndex].ncGuid,
            this.masterReportDataList[0].nonConformances[firstIndex].order
          );
          this.masterReportDataList[0].nonConformances[firstIndex].issue += ` ${value.issue}`;
          this.masterReportDataList[0].nonConformances[firstIndex].location += ` ${value.location}`;
          this.masterReportDataList[0].nonConformances[firstIndex].actions += ` ${value.actions}`;
          this.masterReportDataList[0].nonConformances[firstIndex].isResolve = this.returnStatus(
            this.masterReportDataList[0].nonConformances[firstIndex].isResolve,
            value.isResolve,
            false
          );
          this.masterReportDataList[0].nonConformances[firstIndex].isUrgent = this.returnStatus(
            this.masterReportDataList[0].nonConformances[firstIndex].isUrgent,
            value.isUrgent,
            true
          );
          this.mergeComponentObj.removeGuid.push(value.ncGuid);
        }
      } else if (count > 0) {
        this.masterReportDataList[0].nonConformances[index].order = this.masterReportDataList[0].nonConformances[index].order - count;
      }
    }
    setTimeout(() => {
      this.subscription.add(
        this.reportService.editNonConfReport(this.masterReportDataList[0].nonConformances[firstIndex]).subscribe({
          next: res => {
            this.reportService.deleteMergedNonConformance(this.mergeComponentObj).subscribe({
              next: data => {
                this.loading = false;
                this.getById(this.workorderId);
                this.masterReportDataList[0].ncOrderChange = true;
                this.nCOrderChange.emit(this.masterReportDataList[0].ncOrderChange);
                this.alertService.showSuccessToast(res.message);
              },
              error: e => {
                this.loading = false;
              }
            });
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    }, 0);
    this.mergeModalRef.hide();
  }

  returnStatus(val1: boolean, val2: boolean, defaultVal: boolean) {
    if (val1 === val2) {
      return val1;
    } else {
      return defaultVal;
    }
  }

  mergeImages(toData: NonConformanceImagesById[], fromData: NonConformanceImagesById[], ncGuid, order) {
    const data: NonConformanceImagesById[] = toData.concat(fromData);
    for (const i of data) {
      i.order = order;
      i.ncGuid = ncGuid;
    }
    return data;
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    if (this.mergeModalRef) {
      this.mergeModalRef.hide();
    }
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
