import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from '../../../@shared/shared.module';
import { CustomDashboardAddEditModalComponent } from './custom-dashboard/custom-dashboard-add-edit-modal/custom-dashboard-add-edit-modal.component';
import { CustomDashboardComponent } from './custom-dashboard/custom-dashboard.component';
import { OperationsReportsRoutingModule } from './operations-reports-routing.module';
import { OperationsReportsComponent } from './operations-reports.component';
import { PmCompletionComponent } from './pm-completion/pm-completion.component';
import { WorkOrderCompletionPlanComponent } from './pm-completion/work-order-completion-plan/work-order-completion-plan.component';
import { RescheduleCategoryChartComponent } from './reschedule-pivot/reschedule-category-chart/reschedule-category-chart.component';
import { ReschedulePivotComponent } from './reschedule-pivot/reschedule-pivot.component';

@NgModule({
  imports: [CommonModule, SharedModule, OperationsReportsRoutingModule],
  declarations: [
    OperationsReportsComponent,
    PmCompletionComponent,
    ReschedulePivotComponent,
    RescheduleCategoryChartComponent,
    WorkOrderCompletionPlanComponent,
    CustomDashboardComponent,
    CustomDashboardAddEditModalComponent
  ]
})
export class OperationsReportsModule {}
