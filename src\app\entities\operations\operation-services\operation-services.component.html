<nb-card class="dataSourceSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Services</h6>
        <button
          class="linear-mode-button ms-auto"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="navigateToAddService()"
          type="button"
        >
          <span class="d-none d-md-inline-block">Add Service</span>
          <i class="d-inline-block d-md-none fa-solid fa-calendar-plus-o"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive">
        <table class="table table-hover table-bordered" aria-describedby="data source">
          <thead>
            <tr>
              <th class="text-start" id="name">ID</th>
              <th class="text-start" id="frequencyType">Class</th>
              <th class="text-start" id="lastRun">Category</th>
              <th class="text-start" id="lastRunResult">Mfg Specific</th>
              <th class="text-start" id="createdBy">Mfg Cert/Training</th>
              <th class="text-start" id="updatedBy">Prevailing Wage</th>
              <th class="text-start" id="updatedOn">Service</th>
              <th class="text-start" id="updatedOn">Rate Type</th>
              <th class="text-center" id="action">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let srv of servicesLists">
              <td>{{ srv?.id }}</td>
              <td>{{ srv?.class }}</td>
              <td>{{ srv?.category }}</td>
              <td>{{ srv?.mfgSpecific === true ? 'Yes' : 'No' }}</td>
              <td>{{ srv?.mfgCertTraining === true ? 'Yes' : 'No' }}</td>
              <td>{{ srv?.prevailingWage === true ? 'Yes' : 'No' }}</td>
              <td>{{ srv?.service }}</td>
              <td>{{ srv?.rateType }}</td>
              <td>
                <div class="d-flex justify-content-center">
                  <a class="listgrid-icon text-primary" [routerLink]="['../edit/' + srv?.id]">
                    <em class="fa fa-edit" nbTooltip="Edit Service" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                  <a class="px-2 text-danger listgrid-icon" (click)="onDelete(srv?.id, srv?.isOpertaionServiceInUse)">
                    <em class="fa fa-trash" nbTooltip="Delete Service" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                  </a>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </nb-card-body>
</nb-card>
