<!-- Modal -->
<div class="modal-content" id="ncaddedit" data-focus-on="input:first" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <div class="container">
      <div class="row">
        <div class="col-4">
          <h6 *ngIf="!reportGuid">Item # {{ editNC?.order }}</h6>
          <h6 *ngIf="reportGuid">Add Non Conformance</h6>
        </div>
        <div class="col-8 text-end">
          <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
            <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
          </button>
        </div>
      </div>
    </div>
  </div>
  <form name="ncForm" #ncForm="ngForm" aria-labelledby="title" autocomplete="off">
    <div class="modal-body">
      <div class="row">
        <div class="col-lg-6">
          <h6>Non-Conformance</h6>
        </div>
        <div class="col-lg-6">
          <div class="row g-2">
            <div class="col-6 text-end mb-2 mb-sm-0">
              <label class="label"
                >Resolved
                <nb-toggle
                  status="primary"
                  labelPosition="start"
                  [(checked)]="editNC.isResolve"
                  class="nonConformancetable"
                  [disabled]="!selectedisFinal || viewdeletetedbutton"
                ></nb-toggle
              ></label>
            </div>
            <div class="col-6">
              <em class="fa fa-circle text-danger m-1" style="font-size: 9px !important" aria-hidden="true"></em>
              <label class="label">
                Make it Urgent
                <nb-toggle
                  status="primary"
                  [(checked)]="editNC.isUrgent"
                  class="nonConformancetable"
                  labelPosition="start"
                  [disabled]="!selectedisFinal || viewdeletetedbutton"
                ></nb-toggle>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-lg-6 mb-2">
          <label class="label" for="input-component">Component</label>
          <ng-select
            id="component"
            class="model-dd"
            name="component"
            [items]="componentList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="editNC.component"
            (change)="getIssueListBasedOnComponent(editNC.component, '', '', true)"
            notFoundText="No Components Found"
            placeholder="Select Component"
            [clearable]="false"
            appendTo="body"
            [disabled]="!selectedisFinal || viewdeletetedbutton"
          >
          </ng-select>
        </div>
        <div class="col-12 col-lg-6 mb-2">
          <label class="label" for="input-Location/Indentity">Location/Device Name<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            fullWidth
            [(ngModel)]="editNC.location"
            #location="ngModel"
            name="Location/Indentity"
            [disabled]="!selectedisFinal || viewdeletetedbutton"
            class="form-control"
            spellcheck="true"
            contenteditable="true"
            [status]="location.dirty ? (location.invalid ? 'danger' : 'success') : 'basic'"
            required
            [attr.aria-invalid]="location.invalid && location.touched ? true : null"
          />
          <sfl-error-msg [control]="location" [isFormSubmitted]="ncForm?.submitted" fieldName="Location/Device Name"></sfl-error-msg>
        </div>
        <div class="col-12 col-lg-6 mb-2" *ngIf="assessmentType === 'SV'">
          <label class="label" for="input-Issues/Observation">Issues/Observation<span class="ms-1 text-danger">*</span></label>
          <ng-select
            id="issuesObservation"
            class="model-dd"
            name="issuesObservation"
            [items]="filteredIssueList"
            bindLabel="issueObservation"
            bindValue="issueObservation"
            [(ngModel)]="editNC.issue"
            (change)="getActionListBasedOnIssue(editNC.issue, editNC.actions, true)"
            #issuesObservation="ngModel"
            notFoundText="No Issues/Observations Found"
            placeholder="Select Issue/Observation"
            [clearable]="true"
            appendTo="body"
            required
            [disabled]="!selectedisFinal || viewdeletetedbutton"
          >
          </ng-select>
          <sfl-error-msg [control]="issuesObservation" [isFormSubmitted]="ncForm?.submitted" fieldName="Issues/Observation"></sfl-error-msg>
        </div>

        <div class="col-12 col-lg-6 mb-2" *ngIf="assessmentType === 'SV'">
          <label class="label" for="input-actions">Actions/Recommendation<span class="ms-1 text-danger">*</span></label>
          <ng-select
            id="input-actions"
            class="model-dd"
            name="actionsRecommendation"
            #actionsRecommendation="ngModel"
            [items]="filteredActionList"
            bindLabel="actionRecommendation"
            bindValue="actionRecommendation"
            [(ngModel)]="editNC.actions"
            notFoundText="No Actions/Recommendation Found"
            placeholder="Select Actions/Recommendation"
            [clearable]="true"
            appendTo="body"
            required
            [disabled]="!selectedisFinal || viewdeletetedbutton"
          >
          </ng-select>
          <sfl-error-msg
            [control]="actionsRecommendation"
            [isFormSubmitted]="ncForm?.submitted"
            fieldName="Actions/Recommendation"
          ></sfl-error-msg>
        </div>
      </div>
      <div class="row mt-2" *ngIf="assessmentType === 'SV'">
        <div class="col-12 col-lg-6 mb-2" *ngIf="editNC.issue === NC_ACTION_ISSUE_TYPE.OTHER">
          <label class="label">Other Issues/Observation<span class="ms-1 text-danger">*</span></label>
          <textarea
            nbInput
            fullWidth
            [(ngModel)]="otherSelectedNcIssueAction.issueText"
            class="form-control textareaFont"
            #issue="ngModel"
            [disabled]="!selectedisFinal || viewdeletetedbutton"
            name="Issues/Observation"
            spellcheck="true"
            contenteditable="true"
            id="input-issuesObservation"
            [status]="issue.dirty ? (issue.invalid ? 'danger' : 'success') : 'basic'"
            required
            [attr.aria-invalid]="issue.invalid && issue.touched ? true : null"
          >
          </textarea>
          <sfl-error-msg [control]="issue" [isFormSubmitted]="ncForm?.submitted" fieldName="Other Issues/Observation"></sfl-error-msg>
        </div>
        <div class="col-12 col-lg-6 mb-2" *ngIf="editNC.actions === NC_ACTION_ISSUE_TYPE.OTHER">
          <label class="label">Other Actions/Recommendation<span class="ms-1 text-danger">*</span></label>
          <textarea
            nbInput
            fullWidth
            [(ngModel)]="otherSelectedNcIssueAction.actionsText"
            class="form-control textareaFont"
            #actions="ngModel"
            [disabled]="!selectedisFinal || viewdeletetedbutton"
            name="actions"
            spellcheck="true"
            contenteditable="true"
            id="input-actions"
            [status]="actions.dirty ? (actions.invalid ? 'danger' : 'success') : 'basic'"
            required
            [attr.aria-invalid]="actions.invalid && actions.touched ? true : null"
          >
          </textarea>
          <sfl-error-msg [control]="actions" [isFormSubmitted]="ncForm?.submitted" fieldName="Other Actions/Recommendation"></sfl-error-msg>
        </div>
      </div>
      <div class="row mt-2" *ngIf="assessmentType !== 'SV'">
        <div class="col-12 col-lg-6 mb-2">
          <label class="label">Issues/Observation<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            fullWidth
            [(ngModel)]="editNC.issue"
            class="form-control textareaFont"
            #issue="ngModel"
            [disabled]="!selectedisFinal || viewdeletetedbutton"
            name="Issues/Observation"
            spellcheck="true"
            contenteditable="true"
            id="input-issuesObservation"
            [status]="issue.dirty ? (issue.invalid ? 'danger' : 'success') : 'basic'"
            required
            [attr.aria-invalid]="issue.invalid && issue.touched ? true : null"
          />

          <sfl-error-msg [control]="issue" [isFormSubmitted]="ncForm?.submitted" fieldName="Issues/Observation"></sfl-error-msg>
        </div>
        <div class="col-12 col-lg-6 mb-2">
          <label class="label">Actions/Recommendation<span class="ms-1 text-danger">*</span></label>
          <textarea
            nbInput
            fullWidth
            [(ngModel)]="editNC.actions"
            class="form-control textareaFont"
            #issue="ngModel"
            [disabled]="!selectedisFinal || viewdeletetedbutton"
            name="Issues/Observation"
            spellcheck="true"
            contenteditable="true"
            id="input-issuesObservation"
            [status]="issue.dirty ? (issue.invalid ? 'danger' : 'success') : 'basic'"
            required
            [attr.aria-invalid]="issue.invalid && issue.touched ? true : null"
          >
          </textarea>
          <sfl-error-msg [control]="issue" [isFormSubmitted]="ncForm?.submitted" fieldName="Other Issues/Observation"></sfl-error-msg>
        </div>
      </div>
      <div class="row mb-2" *ngIf="assessmentType === 'SV'">
        <div class="col-4 col-lg-3">
          <label class="label">Estimated Hours </label>
          <input
            nbInput
            fullWidth
            name="estimatedHours"
            id="EstimatedHours"
            #estimatedHours="ngModel"
            [(ngModel)]="editNC.estimatedHours"
            currencyMask
            [options]="{ allowNegative: false, precision: 1, prefix: '' }"
            maxlength="4"
          />
        </div>
        <div class="col-5 col-lg-2">
          <label class="label">Special Tools </label>
          <input
            nbInput
            fullWidth
            name="specialTools"
            id="SpecialTools"
            #specialTools="ngModel"
            maxlength="128"
            [(ngModel)]="editNC.specialTools"
          />
        </div>
        <div class="col-4 col-lg-3">
          <label class="label">Estimated Materials </label>
          <input
            nbInput
            fullWidth
            name="materialsEstimate"
            id="MaterialsEstimate"
            #materialsEstimate="ngModel"
            maxlength="512"
            [(ngModel)]="editNC.materialsEstimate"
          />
        </div>
        <div class="col-1 col-lg-1 d-flex flex-column">
          <label class="label mb-2" for="isLiftRequired">Lift</label>
          <nb-checkbox id="isLiftRequired" class="sfl-track-checkbox mt-2" [(ngModel)]="editNC.isLiftRequired" name="isLiftRequired">
          </nb-checkbox>
        </div>
        <div class="col-4 col-lg-3">
          <label class="label"># of People Required </label>
          <input
            nbInput
            name="noOfPeople"
            id="noOfPeople"
            #noOfPeople="ngModel"
            [(ngModel)]="editNC.numberOfPeopleRequired"
            fullWidth
            autocomplete="off"
            OnlyNumber
            maxlength="4"
          />
        </div>
      </div>
      <div class="container mt-3" *ngIf="(editNC.ncGuid || !isEdit) && !viewdeletetedbutton">
        <div class="row">
          <div class="col-md-12">
            <div
              *ngIf="selectedisFinal"
              class="dropZone"
              ngFileDragDrop
              (fileDropped)="getUploadedFiles($event, editNC.ncGuid, editNC.reportGuid, editNC.order, true)"
            >
              <input
                type="file"
                #file
                accept="image/*"
                multiple
                (change)="getUploadedFiles($event, editNC.ncGuid, editNC.reportGuid, editNC.order, true)"
              />
              <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
              <h3 class="fw-bold">Drop Image To Attach</h3>
              <label style="text-transform: none" class="fw-bold">OR Click to Browse </label>
            </div>
          </div>
        </div>
      </div>
      <!-- Show pending images for new NC creation -->
      <div class="form-group row mt-4" *ngIf="!editNC.ncGuid && pendingImages && pendingImages.length > 0">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
              <p class="fw-bold">Selected Images ({{ pendingImages.length }})</p>
              <label class="label mb-0"><strong>Note:- </strong> These images will be uploaded after saving the non-conformance.</label>
            </div>
            <button
              type="button"
              class="btn btn-sm text-danger"
              (click)="pendingImages = []"
              nbTooltip="Clear all images"
              nbTooltipPlacement="top"
              nbTooltipStatus="danger"
            >
              <i class="fa fa-trash"></i> Clear All
            </button>
          </div>
          <!-- List of individual pending images -->
          <div class="pending-images-list">
            <div class="row" *ngFor="let image of pendingImages; let i = index; trackBy: trackByFunction">
              <div class="col-4 d-flex align-items-center">
                <i class="fa fa-image text-primary me-2"></i>
                <span class="text-truncate">{{ image.name }}</span>
              </div>
              <div class="col-2 text-end">
                <button
                  type="button"
                  class="btn btn-sm text-danger"
                  (click)="removePendingImage(i)"
                  nbTooltip="Remove image"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="danger"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="form-group row mt-4" *ngIf="editNC.ncGuid && editNC.images && editNC.images?.length > 0">
        <div class="col-12">
          <label class="fw-bold">Drag card to organize images</label>
        </div>
      </div>
      <div class="container" *ngIf="editNC.ncGuid">
        <div class="row mt-4 justify-content-center">
          <div class="col-md-12 row" cdkDropListGroup>
            <div class="col-sm-5 cdkImagesborder me-2 mb-2" cdkDropList id="cdklist" [cdkDropListData]="index"></div>
            <div
              id="cdklist"
              cdkDropList
              [cdkDropListData]="index"
              *ngFor="let item of editNC?.images | orderBy : 'order'; let index = index; trackBy: trackByFunction"
              class="col-sm-5 cdkImagesborder me-2 mb-2"
            >
              <div cdkDrag [cdkDragData]="index" (cdkDragEntered)="dragEntered($event, editNC.images)" class="example-box cdkImagesMargin">
                <a
                  (click)="deleteNonConflImages(item.fileId)"
                  class="removeNonConformImagesIcon"
                  *ngIf="selectedisFinal && !viewdeletetedbutton"
                >
                  <em
                    class="fa fa-times-circle text-dark"
                    nbTooltip="Delete"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="text-dark"
                    aria-hidden="true"
                  ></em>
                </a>
                <a class="checkNCImageIcon text-primary" *ngIf="selectedisFinal && !viewdeletetedbutton">
                  <nb-checkbox
                    class="chkRadioIncludebtn"
                    (change)="includeReport(item?.fileId)"
                    [(ngModel)]="item.isIncludeinReport"
                    [ngModelOptions]="{ standalone: true }"
                    nbTooltip="Include in report"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                  ></nb-checkbox>
                </a>
                <a class="checkNCImageDeletedIcon text-primary" *ngIf="viewdeletetedbutton && item.isIncludeinReport">
                  <em class="fa fa-check-circle" nbTooltip="Include in report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em
                ></a>
                <img
                  (click)="imagePopup(index, item.imageGuid, true)"
                  href="#slideImg"
                  [src]="item?.thumbnailUrl || item?.imageUrl"
                  alt="Non Conformance Image"
                  class="text-center cursor-pointer card-img-top nonConformanceImage"
                  onError="this.src='assets/images/no-image-found.jpg'"
                />
              </div>
              <div
                cdkDrag
                [cdkDragData]="index"
                class="example-box cdkImagesMargin"
                (cdkDragEntered)="dragEntered($event, editNC.images)"
                *ngIf="viewdeletetedbutton"
              >
                <a class="checkGalleryIcon text-primary" *ngIf="item.isIncludeinReport">
                  <em class="fa fa-check-circle" nbTooltip="Include in report" nbTooltipPlacement="top" nbTooltipStatus="primary"></em
                ></a>
                <img
                  (click)="imagePopup(index, item.imageGuid, true)"
                  [src]="item?.thumbnailUrl || item?.imageUrl"
                  alt="General Image"
                  class="text-center cursor-pointer card-img-top galleryImage"
                  onError="this.src='assets/images/no-image-found.jpg'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button
        nbButton
        status="primary"
        size="medium"
        *ngIf="selectedisFinal && !viewdeletetedbutton"
        (click)="createEditNC()"
        [disabled]="!ncForm.valid"
      >
        Save
      </button>
      <button nbButton status="basic" size="medium" (click)="onCancel()">Cancel</button>
    </div>
  </form>
</div>
