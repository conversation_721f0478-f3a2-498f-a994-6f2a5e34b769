import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiUrl } from '../../../@shared/constants';
import { OperationServicesList, ServiceAuditHistory, ServicesList, ServicesRateDropDownList } from './operation-services.modal';

@Injectable({
  providedIn: 'root'
})
export class OperationServicesService {
  constructor(private readonly http: HttpClient) {}

  getServicesLists(): Observable<ServicesList[]> {
    return this.http.post<ServicesList[]>(ApiUrl.SERVICES_LISTING, {});
  }

  getServiceDetail(serviceId: number): Observable<OperationServicesList> {
    return this.http.get<OperationServicesList>(`${ApiUrl.GET_A_SERVICES}?id=${serviceId}`, {});
  }

  getServiceHistoryData(serviceId): Observable<ServiceAuditHistory[]> {
    return this.http.get<ServiceAuditHistory[]>(`${ApiUrl.GET_SERVICES_HISTORY_LIST}/${serviceId}`);
  }

  createUpdateServices(data: OperationServicesList): Observable<OperationServicesList> {
    if (data.id) return this.http.put<OperationServicesList>(ApiUrl.SERVICES_ROOT, data);
    return this.http.post<OperationServicesList>(ApiUrl.SERVICES_ROOT, data);
  }

  getServiceDropDownList(): Observable<ServicesRateDropDownList[]> {
    return this.http.get<ServicesRateDropDownList[]>(`${ApiUrl.GET_SERVICES_RATE_DROP_DOWN_LIST}`, {});
  }

  deleteServices(serviceId: string) {
    return this.http.delete(`${ApiUrl.SERVICES_ROOT}/${serviceId}`);
  }
}
