import { CdkDrag, CdkDragEnter, CdkDragMove, CdkDropList, CdkDropListGroup, moveItemInArray } from '@angular/cdk/drag-drop';
import { ViewportRuler } from '@angular/cdk/scrolling';
import { DecimalPipe } from '@angular/common';
import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription, forkJoin } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { ImageCropperComponent } from '../../../../../@shared/components/image-cropper/image-cropper.component';
import { ErrorMessages } from '../../../../../@shared/constants';
import { Dropdown } from '../../../../../@shared/models/dropdown.model';
import {
  AllReportModel,
  EquipmentStatusImages,
  MasterEquipmentStatus,
  MasterGeneralImagesModel,
  MasterReportModel
} from '../../../../../@shared/models/report.model';
import { TowDecimalPipe } from '../../../../../@shared/pipes/decimal.pipe';
import { AlertService } from '../../../../../@shared/services';
import { SiteDevice } from '../../../../site-device/site-device.model';
import { SiteDeviceService } from '../../../../site-device/site-device.service';
import { ModelComponent } from '../../../model/model.component';
import { ReportService } from '../../../report.service';

@Component({
  selector: 'sfl-add-edit-mvpm-equipment-status',
  templateUrl: './add-edit-mvpm-equipment-status.component.html',
  styleUrls: ['./add-edit-mvpm-equipment-status.component.scss'],
  providers: [TowDecimalPipe]
})
export class AddEditMvpmEquipmentStatusComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(CdkDropListGroup) listGroup: CdkDropListGroup<CdkDropList>;
  @ViewChild(CdkDropList) placeholder: CdkDropList;
  masterReportDataList: MasterReportModel[];
  @Input() workId;
  public onClose: Subject<boolean>;
  @Input() editNC: MasterEquipmentStatus = new MasterEquipmentStatus();
  @Input() selectedisFinal: boolean;
  @Input() isAdd: boolean;
  @Input() reportGuid: string;
  @Input() componentList;
  @Input() siteId: number;
  mvDisconnectImages: EquipmentStatusImages[];
  public event: EventEmitter<any> = new EventEmitter();
  modalRef: BsModalRef;
  loading = false;
  subscription: Subscription = new Subscription();
  generalImagesGroupId = '7';
  ncGuid: string;
  isFinal: boolean;
  reportMasterList: AllReportModel;
  nonconformanceList: MasterReportModel[];
  selectedItem;
  dir;
  isEdit = false;
  selectedComponent;
  @Input() viewdeletetedbutton;
  uploadedFiles: any = [];
  deviceTypeList: Dropdown[] = [];
  sitedDeviceTypeList: Dropdown[] = [];
  siteDeviceModel: SiteDevice = new SiteDevice();
  public target: CdkDropList;
  public targetIndex: number;
  public source: CdkDropList;
  public sourceIndex: number;
  public dragIndex: number;
  public activeContainer;
  files: File[] = [];
  namePlateImageFile: File = null;
  isIncludeInReport;
  siteDeviceName: string;
  siteDeviceId: number;
  imgId: any = 0;
  liquidLevelList = ['Low', 'Slightly Low', 'Normal', 'Slightly High', 'High'];
  xfmrPhotoList = [
    'Exterior (Front)',
    'Exterior (Fins)',
    'Interior - Doors Open',
    'Primary Bushings',
    'Secondary Bushings',
    'Gauges',
    'Sample Valve Closed',
    'Floor/Ground'
  ];
  isNotRequired = {
    liquidLevel: false,
    oilTemp: false,
    pressure: false,
    detc: false,
    grdtest: false,
    sF6Level: false
  };
  xfmrPhotos: { [key: string]: File } = {};
  xfmrPhotosLinks: { [key: string]: EquipmentStatusImages } = {};
  cTpsFiles: any[] = [];
  fusingFiles: any[] = [];
  instrumentPowerFiles: any[] = [];
  sF6LevelList = ['Okay To Operate', 'Operate But Refill', 'Do Not Operate'];

  constructor(
    private readonly reportService: ReportService,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly viewportRuler: ViewportRuler,
    public readonly _bsModalRef: BsModalRef,
    public readonly siteDeviceService: SiteDeviceService,
    public readonly decimalPipe: DecimalPipe
  ) {
    this.target = null;
    this.source = null;
  }

  ngOnInit(): void {
    this.onClose = new Subject();
    this.reportService.resetApiCounts();
    if (this.editNC.esGuid) {
      if (this.editNC?.deviceTypeId === 9 && this.editNC?.equipmentStatusImages?.length) {
        this.setXfmrPhotos();
      }
      this.getById(true);
      this.isEdit = true;
    } else {
      this.isEdit = false;
    }
    this.getAllDeviceType();
    this.subscription.add(
      this.reportService.getApiCounts().subscribe(res => {
        this.loading = !!res;
      })
    );
  }

  // Get By id
  getById(formComponent) {
    this.reportService.incrementApiCounts();
    this.subscription.add(
      this.reportService.getByEquipmentStatusId(this.editNC.esGuid).subscribe({
        next: (res: MasterEquipmentStatus) => {
          if (res?.deviceTypeId) {
            this.editNC.equipmentStatusImages = res.equipmentStatusImages.filter(item => item.sectionId === null);
            this.mvDisconnectImages = res.equipmentStatusImages;
            if (this.editNC?.deviceTypeId === 9) {
              this.setXfmrPhotos();
              this.setDisabledFields();
              this.editNC.transformerEquipmentStatus.nameplateImageFileId = res.transformerEquipmentStatus?.nameplateImageFileId;
              this.editNC.transformerEquipmentStatus.nameplateImageGuid = res.transformerEquipmentStatus.nameplateImageGuid;
              if (res?.transformerEquipmentStatus?.nameplateImageURL) {
                this.editNC.transformerEquipmentStatus.nameplateImageURL = res?.transformerEquipmentStatus?.nameplateImageURL;
              } else {
                this.editNC.transformerEquipmentStatus.nameplateImageURL = null;
              }
            }
            if (this.editNC?.deviceTypeId === 33) {
              this.setDisabledFieldsForMcDisconnect();
            }
            if (this.editNC?.protectiveRelayStatus?.replaceDate || this.editNC?.protectiveRelayStatus?.relayCalibrationDate) {
              this.editNC.protectiveRelayStatus.replaceDate = res?.protectiveRelayStatus?.replaceDate;
              this.editNC.protectiveRelayStatus.relayCalibrationDate = res?.protectiveRelayStatus?.relayCalibrationDate;
            }
            if (this.editNC?.protectiveRelayStatus?.replaceDate) {
              this.editNC.protectiveRelayStatus.replaceDate = new Date(this.editNC.protectiveRelayStatus.replaceDate);
            }
            if (this.editNC?.protectiveRelayStatus?.relayCalibrationDate) {
              this.editNC.protectiveRelayStatus.relayCalibrationDate = new Date(this.editNC.protectiveRelayStatus.relayCalibrationDate);
            }
            if (!formComponent) {
              this.editNC.equipmentStatusImages = res.equipmentStatusImages.filter(item => item.sectionId === null);
              this.mvDisconnectImages = res.equipmentStatusImages;
            }
            if (!formComponent && res?.transformerEquipmentStatus?.nameplateImageURL) {
              this.editNC.transformerEquipmentStatus.nameplateImageURL = res?.transformerEquipmentStatus?.nameplateImageURL;
            }
          } else {
            this.alertService.showErrorToast('Data not found');
          }
          this.reportService.decrementApiCounts();
        },
        error: e => {
          this.reportService.decrementApiCounts();
        }
      })
    );
  }
  getAllDeviceType() {
    this.reportService.incrementApiCounts();
    this.subscription.add(
      this.siteDeviceService.getAllMVPMDeviceType().subscribe({
        next: (res: Dropdown[]) => {
          this.deviceTypeList = res;
          this.DeviceTypeSelect(this.editNC.deviceTypeId);
          this.reportService.decrementApiCounts();
        },
        error: e => {
          this.reportService.decrementApiCounts();
        }
      })
    );
  }

  // Get JHA Data by ID
  getByDetail(id) {
    this.reportService.incrementApiCounts();
    this.subscription.add(
      this.reportService.getDataById(this.workId, 'Order').subscribe({
        next: (res: AllReportModel) => {
          this.reportService.decrementApiCounts();
          this.reportMasterList = res;
          this.masterReportDataList = this.reportMasterList.reports;
        },
        error: e => {
          this.reportService.decrementApiCounts();
        }
      })
    );
  }

  // Save Changes
  createEditEquipmentStatus() {
    if (this.editNC.deviceTypeId === 9) {
      this.reportService.incrementApiCounts();
      if (this.editNC.esGuid) {
        this.editNC.siteDeviceId = this.siteDeviceId;
        if (Object.keys(this.xfmrPhotos).length + this.editNC.equipmentStatusImages.length === 8) {
          if (this.namePlateImageFile || this.editNC.transformerEquipmentStatus.nameplateImageURL) {
            this.subscription.add(
              this.reportService.editUploadEquipmentStatus(this.editNC).subscribe({
                next: res => {
                  this.onModelSuccess(res.message, this.editNC.esGuid, this.editNC.reportId);
                  this.reportService.decrementApiCounts();
                },
                error: e => {
                  this.reportService.decrementApiCounts();
                }
              })
            );
          } else {
            this.alertService.showErrorToast('Nameplate Image is required');
            this.reportService.decrementApiCounts();
          }
        } else {
          this.alertService.showErrorToast('Add all 8 images to include in reports');
          this.reportService.decrementApiCounts();
        }
      } else {
        if (Object.keys(this.xfmrPhotos).length === 8) {
          if (this.namePlateImageFile) {
            this.reportService.incrementApiCounts();
            this.editNC.reportId = this.reportGuid;
            this.editNC.transformerEquipmentStatus.nameplateImageGuid = null;
            this.subscription.add(
              this.reportService.uploadEquipmentTransStatus(this.editNC).subscribe({
                next: res => {
                  if (Object.keys(this.xfmrPhotos).length) {
                    this.getUploadedFiles(res.id, this.reportGuid);
                  } else {
                    this.alertService.showSuccessToast(res.message);
                    this.reportService.decrementApiCounts();
                    this.event.emit(this.editNC);
                    this._bsModalRef.hide();
                  }
                },
                error: e => {
                  this.reportService.decrementApiCounts();
                }
              })
            );
          } else {
            this.alertService.showErrorToast('Nameplate Image is required');
            this.reportService.decrementApiCounts();
          }
        } else {
          this.alertService.showErrorToast('Add all 8 images to include in reports');
          this.reportService.decrementApiCounts();
        }
      }
    } else if (this.editNC.deviceTypeId === 8) {
      this.uploadPREquipmentStatus();
    } else if (this.editNC.deviceTypeId === 33) {
      this.uploadMVEquipmentStatus();
    }
  }

  onModelSuccess(msg, id, reportGuid) {
    if (Object.keys(this.xfmrPhotos).length || this.files.length) {
      this.getUploadedFiles(id, reportGuid);
    } else if (this.editNC.deviceTypeId === 33 && !this.files.length) {
      this.mvdDisconnectOtherImageUpload(id, reportGuid);
    } else {
      this.alertService.showSuccessToast(msg);
      this.event.emit(this.editNC);
      this.reportService.decrementApiCounts();
      this._bsModalRef.hide();
    }
  }

  uploadPREquipmentStatus() {
    this.reportService.incrementApiCounts();
    const date = new Date();
    this.editNC.protectiveRelayStatus.replaceDate = this.editNC.protectiveRelayStatus.replaceDate
      ? new Date(
          Date.UTC(
            this.editNC.protectiveRelayStatus.replaceDate.getFullYear(),
            this.editNC.protectiveRelayStatus.replaceDate.getMonth(),
            this.editNC.protectiveRelayStatus.replaceDate.getDate(),
            date.getHours(),
            date.getMinutes(),
            0,
            0
          )
        )
      : null;
    this.editNC.protectiveRelayStatus.relayCalibrationDate = this.editNC.protectiveRelayStatus.relayCalibrationDate
      ? new Date(
          Date.UTC(
            this.editNC.protectiveRelayStatus.relayCalibrationDate.getFullYear(),
            this.editNC.protectiveRelayStatus.relayCalibrationDate.getMonth(),
            this.editNC.protectiveRelayStatus.relayCalibrationDate.getDate(),
            date.getHours(),
            date.getMinutes(),
            0,
            0
          )
        )
      : null;
    if (this.editNC.esGuid) {
      this.editNC.siteDeviceId = this.siteDeviceId;
      if (this.files.length + this.editNC.equipmentStatusImages.length >= 2) {
        this.subscription.add(
          this.reportService.editUploadEquipmentPRStatus(this.editNC).subscribe({
            next: res => {
              this.onModelSuccess(res.message, this.editNC.esGuid, this.editNC.reportId);
            },
            error: e => {
              this.reportService.decrementApiCounts();
            }
          })
        );
      } else {
        this.alertService.showErrorToast('Add at least 2 images to include in report');
        this.reportService.decrementApiCounts();
      }
    } else {
      if (this.files.length >= 2) {
        this.reportService.incrementApiCounts();

        this.editNC.reportId = this.reportGuid;
        this.editNC.protectiveRelayStatus.nameplateImageGuid = null;
        this.subscription.add(
          this.reportService.uploadEquipmentPRStatus(this.editNC).subscribe({
            next: res => {
              this.onModelSuccess(res.message, res.id, this.reportGuid);
            },
            error: e => {
              this.reportService.decrementApiCounts();
            }
          })
        );
      } else {
        this.alertService.showErrorToast('Add at least 2 images to include in report');
        this.reportService.decrementApiCounts();
      }
    }
  }

  uploadMVEquipmentStatus() {
    this.reportService.incrementApiCounts();
    if (this.editNC.esGuid) {
      this.editNC.siteDeviceId = this.siteDeviceId;
      if (this.files.length + this.editNC.equipmentStatusImages.length >= 2) {
        this.subscription.add(
          this.reportService.editUploadEquipmentMVStatus(this.editNC).subscribe({
            next: res => {
              this.onModelSuccess(res.message, this.editNC.esGuid, this.editNC.reportId);
            },
            error: e => {
              this.reportService.decrementApiCounts();
            }
          })
        );
      } else {
        this.alertService.showErrorToast('Add at least 2 images to include in report');
        this.reportService.decrementApiCounts();
      }
    } else {
      if (this.files.length >= 2) {
        this.reportService.incrementApiCounts();
        this.editNC.reportId = this.reportGuid;
        this.editNC.mvDisconnectedStatus.nameplateImageGuid = null;
        this.subscription.add(
          this.reportService.uploadEquipmentMVStatus(this.editNC).subscribe({
            next: res => {
              this.onModelSuccess(res.message, res.id, this.reportGuid);
            },
            error: e => {
              this.reportService.decrementApiCounts();
            }
          })
        );
      } else {
        this.alertService.showErrorToast('Add at least 2 images to include in reports');
        this.reportService.decrementApiCounts();
      }
    }
  }

  DeviceTypeSelect(id) {
    this.subscription.add(
      this.siteDeviceService.deviceTypeName(id, this.siteId).subscribe({
        next: (res: Dropdown[]) => {
          if (id === 9) {
            this.siteDeviceName = 'Transformer';
          } else if (id === 8) {
            this.siteDeviceName = 'Protective Relay';
          } else if (id === 33) {
            this.siteDeviceName = 'MV Disconnect';
          }
          this.sitedDeviceTypeList = res;
          this.editNC.siteDeviceId = null;
        }
      })
    );
  }

  onDeviceTypeDeSelect() {
    this.siteDeviceModel.deviceTypeData = [];
    this.siteDeviceModel.deviceModelId = null;
    this.siteDeviceModel.size = null;
    this.siteDeviceModel.mfg = null;
  }

  getUpload(event: any) {
    const existingImagesLength = this.editNC?.equipmentStatusImages?.length || 0;
    const newFilesLength = this.files?.length || 0;
    const totalImages = existingImagesLength + newFilesLength;

    for (const i of event.target.files) {
      if (i.size >= 10485760) {
        this.alertService.showErrorToast('File size should not be allowed more than 10MB');
        return;
      }
    }
    if (totalImages + event.target.files.length > 6) {
      this.alertService.showErrorToast('Add Maximum 6 images to include in reports');
      return;
    }
    const data =
      event.target.files.length > 1
        ? { imageFile: null, imageFiles: event.target.files }
        : { imageFile: event.target.files[0], imageFiles: [] };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: data,
      class: 'modal-xl'
    };
    this.modalRef = this.modalService.show(ImageCropperComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (Array.isArray(result)) {
          for (let i = 0; i < event.target.files.length; i++) {
            const imageBlob = this.reportService.getDataURItoBlob(result[i]);
            const imageName = event.target.files[i].name;
            const imageFile = new File([imageBlob], imageName, { type: event.target.files[i].type });
            this.files.push(imageFile);
          }
        } else {
          const imageBlob = this.reportService.getDataURItoBlob(result);
          const imageName = event.target.files[0].name;
          const imageFile = new File([imageBlob], imageName, { type: event.target.files[0].type });
          this.files.push(imageFile);
        }
      }
    });
  }

  // Upload General Images
  getUploadedFiles(ncGuid, reportId) {
    this.reportService.incrementApiCounts();
    this.isIncludeInReport = true;
    const mimeType = this.editNC.deviceTypeId === 9 ? this.xfmrPhotos[Object.keys(this.xfmrPhotos)[0]].type : this.files[0].type;
    if (mimeType.match(/image\/*/) === null) {
      this.alertService.showErrorToast('The image is invalid, or not supported. Allowed types: png, jpg, jpeg');
      this.reportService.decrementApiCounts();
      return;
    }
    const tempArray: any = [];
    if (this.editNC.deviceTypeId === 9) {
      Object.entries(this.xfmrPhotos).forEach(([key, value]) => {
        const formData: FormData = new FormData();
        formData.append('fileImage', value as File);
        formData.append('ReportId', reportId);
        formData.append('NCIId', '');
        formData.append('IMAGEID', '');
        formData.append('eSGuid', ncGuid);
        formData.append('order', (Number(key) + 1).toString());
        formData.append('GroupId', this.generalImagesGroupId);
        formData.append('IsIncludeInReport', this.isIncludeInReport);
        formData.append('OriginalImageGuid', '');
        formData.append('ImageURL', '');
        formData.append('sectionId', 'null');
        tempArray.push(this.reportService.getUploadedEquipmentImage(formData));
      });
    } else {
      this.files.forEach((file, i) => {
        const formData: FormData = new FormData();
        formData.append('fileImage', file as File);
        formData.append('ReportId', reportId);
        formData.append('NCIId', '');
        formData.append('IMAGEID', '');
        formData.append('eSGuid', ncGuid);
        formData.append('order', (i + 1).toString());
        formData.append('GroupId', this.generalImagesGroupId);
        formData.append('IsIncludeInReport', this.isIncludeInReport);
        formData.append('OriginalImageGuid', '');
        formData.append('ImageURL', '');
        formData.append('sectionId', 'null');
        tempArray.push(this.reportService.getUploadedEquipmentImage(formData));
      });
      if (this.editNC.deviceTypeId === 33 && (this.cTpsFiles.length || this.fusingFiles.length || this.instrumentPowerFiles.length)) {
        this.mvdDisconnectOtherImageUpload(ncGuid, reportId);
      }
    }

    forkJoin(tempArray).subscribe({
      next: (res: any) => {
        if (res) {
          if (!this.editNC.esGuid && this.namePlateImageFile) {
            this.getUploadedNamePlateImage(this.namePlateImageFile, ncGuid, this.reportGuid);
          } else {
            this.files = [];
            this.alertService.showSuccessToast('Equipment status data added');
            this.event.emit(this.editNC);
            this.reportService.decrementApiCounts();
            this._bsModalRef.hide();
          }
        }
      },
      error: e => {
        this.reportService.decrementApiCounts();
      }
    });
  }

  mvdDisconnectOtherImageUpload(ncGuid: string, reportId: string): void {
    const tempArray: any[] = [];

    if (this.cTpsFiles.length) {
      this.cTpsFiles.forEach((file, i) => {
        const formData: FormData = new FormData();
        formData.append('fileImage', file as File);
        formData.append('ReportId', reportId);
        formData.append('NCIId', '');
        formData.append('IMAGEID', '');
        formData.append('eSGuid', ncGuid);
        formData.append('order', (i + 1).toString());
        formData.append('GroupId', this.generalImagesGroupId);
        formData.append('IsIncludeInReport', this.isIncludeInReport);
        formData.append('OriginalImageGuid', '');
        formData.append('ImageURL', '');
        formData.append('sectionId', '1');
        formData.append('isNameplateImage', 'false');
        tempArray.push(this.reportService.getUploadedEquipmentImage(formData));
      });
    }
    if (this.fusingFiles.length) {
      this.fusingFiles.forEach((file, i) => {
        const formData: FormData = new FormData();
        formData.append('fileImage', file as File);
        formData.append('ReportId', reportId);
        formData.append('NCIId', '');
        formData.append('IMAGEID', '');
        formData.append('eSGuid', ncGuid);
        formData.append('order', (i + 1).toString());
        formData.append('GroupId', this.generalImagesGroupId);
        formData.append('IsIncludeInReport', this.isIncludeInReport);
        formData.append('OriginalImageGuid', '');
        formData.append('ImageURL', '');
        formData.append('sectionId', '2');
        formData.append('isNameplateImage', 'false');
        tempArray.push(this.reportService.getUploadedEquipmentImage(formData));
      });
    }
    if (this.instrumentPowerFiles.length) {
      this.instrumentPowerFiles.forEach((file, i) => {
        const formData: FormData = new FormData();
        formData.append('fileImage', file as File);
        formData.append('ReportId', reportId);
        formData.append('NCIId', '');
        formData.append('IMAGEID', '');
        formData.append('eSGuid', ncGuid);
        formData.append('order', (i + 1).toString());
        formData.append('GroupId', this.generalImagesGroupId);
        formData.append('IsIncludeInReport', this.isIncludeInReport);
        formData.append('OriginalImageGuid', '');
        formData.append('ImageURL', '');
        formData.append('sectionId', '3');
        formData.append('isNameplateImage', 'false');
        tempArray.push(this.reportService.getUploadedEquipmentImage(formData));
      });
    }
    if (tempArray.length === 0) {
      this.alertService.showSuccessToast('Equipment status data added');
      this.event.emit(this.editNC);
      this.reportService.decrementApiCounts();
      this._bsModalRef.hide();
      return;
    }
    forkJoin(tempArray).subscribe({
      next: (res: any) => {
        if (res) {
          this.cTpsFiles = [];
          this.fusingFiles = [];
          this.instrumentPowerFiles = [];
          this.alertService.showSuccessToast('Equipment status data added');
          this.event.emit(this.editNC);
          this.reportService.decrementApiCounts();
          this._bsModalRef.hide();
        }
      },
      error: () => {
        this.reportService.decrementApiCounts();
      }
    });
  }

  clearXfmrFile(i: number) {
    delete this.xfmrPhotos[i.toString()];
  }

  getXfmrUpload(files, i: number) {
    const mimeType = files[0].type;
    if (mimeType.match(/image\/*/) === null) {
      this.alertService.showErrorToast('The image is invalid, or not supported. Allowed types: png, jpg, jpeg');
      this.reportService.decrementApiCounts();
      return;
    }
    if (files[0].size <= 10485760) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          imageFile: files[0],
          imageFiles: []
        },
        class: 'modal-xl'
      };
      this.modalRef = this.modalService.show(ImageCropperComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          const imageBlob = this.reportService.getDataURItoBlob(result),
            imageName = files[0].name,
            imageFile = new File([imageBlob], imageName, { type: files[0].type });
          this.xfmrPhotos[i.toString()] = imageFile;
        }
      });
    } else {
      this.alertService.showErrorToast('File size should not be allowed more than 10MB');
    }
  }

  getNamePlateImageUpload(files) {
    const mimeType = files[0].type;
    if (mimeType.match(/image\/*/) === null) {
      this.alertService.showErrorToast('The image is invalid, or not supported. Allowed types: png, jpg, jpeg');
      this.reportService.decrementApiCounts();
      return;
    }
    if (files[0].size <= 10485760) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          imageFile: files[0],
          imageFiles: []
        },
        class: 'modal-xl'
      };
      this.modalRef = this.modalService.show(ImageCropperComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          const imageBlob = this.reportService.getDataURItoBlob(result);
          const imageName = files[0].name;
          const imageFile = new File([imageBlob], imageName, { type: files[0].type });
          this.namePlateImageFile = imageFile;
          if (this.editNC.esGuid) {
            this.getUploadedNamePlateImage(this.namePlateImageFile, this.editNC.esGuid, this.editNC.reportId);
          }
        }
      });
    } else {
      this.alertService.showErrorToast('File size should not be allowed more than 10MB');
    }
  }

  // Upload General Images
  getUploadedNamePlateImage(namePlateImageFile, ncGuid, reportId) {
    this.reportService.incrementApiCounts();
    this.isIncludeInReport = true;
    const formData: FormData = new FormData();
    formData.append('FileImage', namePlateImageFile as File);
    formData.append('ReportId', reportId);
    formData.append('NCIId', '');
    formData.append('IMAGEID', '');
    formData.append('order', '0');
    formData.append('beforeAfterImageGuid', ncGuid);
    formData.append('GroupId', this.generalImagesGroupId);
    formData.append('IsIncludeInReport', this.isIncludeInReport);
    formData.append('OriginalImageGuid', '');
    formData.append('ImageURL', '');
    this.subscription.add(
      this.reportService.getUploadedNamePlateImage(formData).subscribe({
        next: res => {
          if (res) {
            this.namePlateImageFile = null;
            if (this.editNC.esGuid) {
              this.alertService.showSuccessToast('Nameplate image Uploaded Successfully');
              this.reportService.decrementApiCounts();
              this.getById(false);
            } else {
              this.alertService.showSuccessToast('Equipment status data added');
              this.event.emit(this.editNC);

              this._bsModalRef.hide();
            }
          }
        },
        error: e => {
          this.reportService.decrementApiCounts();
        }
      })
    );
  }

  // Image Pop Up
  imagePopup(index) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: {
        imageList: this.editNC.equipmentStatusImages,
        selectedImageIndex: index
      }
    };
    this.modalRef = this.modalService.show(ModelComponent, ngModalOptions);
  }

  // drop image
  dropListDropped() {
    let i = 0;
    if (!this.target) {
      return;
    }

    const phElement = this.placeholder.element.nativeElement;
    const parent = phElement.parentElement;

    phElement.style.display = 'none';

    parent.removeChild(phElement);
    parent.appendChild(phElement);
    parent.insertBefore(this.source.element.nativeElement, parent.children[this.sourceIndex]);

    this.target = null;
    this.source = null;

    if (this.sourceIndex !== this.targetIndex) {
      moveItemInArray(this.editNC.equipmentStatusImages, this.sourceIndex, this.targetIndex);
    }
    for (const item of this.editNC.equipmentStatusImages) {
      item.order = i++;
    }
  }

  // Drag and drop image
  dropListEnterPredicate = (drag: CdkDrag, drop: CdkDropList) => {
    if (drop === this.placeholder) {
      return true;
    }
    if (drop !== this.activeContainer) {
      return false;
    }
    const phElement = this.placeholder.element.nativeElement;
    const sourceElement = drag.dropContainer.element.nativeElement;
    const dropElement = drop.element.nativeElement;

    const dragIndex = __indexOf(dropElement.parentElement.children, this.source ? phElement : sourceElement);
    const dropIndex = __indexOf(dropElement.parentElement.children, dropElement);

    if (!this.source) {
      this.sourceIndex = dragIndex;
      this.source = drag.dropContainer;

      phElement.style.width = sourceElement.clientWidth + 'px';
      phElement.style.height = sourceElement.clientHeight + 'px';

      sourceElement.parentElement.removeChild(sourceElement);
    }

    this.targetIndex = dropIndex;
    this.target = drop;

    phElement.style.display = '';
    dropElement.parentElement.insertBefore(phElement, dropIndex > dragIndex ? dropElement.nextSibling : dropElement);

    this.placeholder._dropListRef.enter(drag._dragRef, drag.element.nativeElement.offsetLeft, drag.element.nativeElement.offsetTop);
    return false;
  };

  /** Determines the point of the page that was touched by the user. */
  getPointerPositionOnPage(event: MouseEvent | TouchEvent) {
    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.
    const point = __isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : event;
    const scrollPosition = this.viewportRuler.getViewportScrollPosition();

    return {
      x: point.pageX - scrollPosition.left,
      y: point.pageY - scrollPosition.top
    };
  }

  // After view init
  ngAfterViewInit() {
    if (this.placeholder && this.placeholder.element) {
      const phElement = this.placeholder.element.nativeElement;
      phElement.style.display = 'none';
      phElement.parentElement.removeChild(phElement);
    }
  }

  // Move images
  dragMoved(e: CdkDragMove) {
    const point = this.getPointerPositionOnPage(e.event);
    if (this.listGroup) {
      this.listGroup._items.forEach(dropList => {
        if (__isInsideDropListClientRect(dropList, point.x, point.y)) {
          this.activeContainer = dropList;
          return;
        }
      });
    }
  }

  // JHA select/deseclect
  includeReport(imageGuid) {
    this.reportService.incrementApiCounts();
    this.subscription.add(
      this.reportService.updateIncludeImage(imageGuid).subscribe({
        next: res => {
          this.alertService.showSuccessToast(res.message);
          this.reportService.decrementApiCounts();
        },
        error: e => {
          this.reportService.decrementApiCounts();
        }
      })
    );
  }

  // Image delete in General Images section
  deleteEquipmentStatusImages(imageGuid, value) {
    const currentImages = this.editNC?.equipmentStatusImages?.length || 0;
    const newFiles = this.files?.length || 0;
    const totalImages = currentImages + newFiles;
    if (totalImages <= 2) {
      this.alertService.showErrorToast(
        `Cannot delete. At least 2 images are required. Please upload a new image before deleting.`
      );
      return;
    }

    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: ErrorMessages.deleteMessage
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.subscription.add(
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.reportService.incrementApiCounts();
          this.subscription.add(
            this.reportService.deleteGeneralImages(imageGuid).subscribe({
              next: res => {
                this.reportService.decrementApiCounts();
                if (res) {
                  if (value === true) {
                    this.alertService.showSuccessToast('Nameplate image deleted');
                  } else {
                    this.alertService.showSuccessToast(res.message);
                  }
                  this.getById(false);
                }
              },
              error: e => {
                this.reportService.decrementApiCounts();
              }
            })
          );
        }
      })
    );
  }

  private validateRequirements(): boolean {
    if (!this.isEdit) {
      return true;
    }
    const deviceTypeId = this.editNC.deviceTypeId;
    if (deviceTypeId === 9) {
      const existingImages = this.editNC?.equipmentStatusImages?.length || 0;
      const totalImages = existingImages;
      const hasNameplate = !!(this.namePlateImageFile || this.editNC.transformerEquipmentStatus?.nameplateImageURL);
      if (totalImages !== 8) {
        this.alertService.showErrorToast(
          `Ensure 8 images are uploaded to save changes.`
        );
      }
      if (!hasNameplate) {
        this.alertService.showErrorToast(`Nameplate Image is required.`);
      }
      return totalImages === 8 && hasNameplate;
    } else {
      const currentImages = this.editNC?.equipmentStatusImages?.length || 0;
      const totalImages = currentImages;
      if (totalImages < 2) {
        this.alertService.showErrorToast(
          `Ensure at least 2 images are uploaded to save changes.`
        );
      }
      return totalImages >= 2;
    }
  }

  public onConfirm(): void {
    if (this.validateRequirements()) {
      this.onClose.next(true);
      this._bsModalRef.hide();
    }
  }

  public onCancel(): void {
    if (this.validateRequirements()) {
      this.onClose.next(false);
      this._bsModalRef.hide();
    }
  }

  dragEntered(event: CdkDragEnter<number>, generalImages: MasterGeneralImagesModel[]) {
    let i = 0;
    const drag = event.item;
    const dropList = event.container;
    const dragIndex = drag.data;
    const dropIndex = dropList.data;

    const phContainer = dropList.element.nativeElement;
    const phElement = phContainer.querySelector('.cdk-drag-placeholder');
    phContainer.removeChild(phElement);
    phContainer.parentElement.insertBefore(phElement, phContainer);

    moveItemInArray(generalImages, dragIndex, dropIndex);
    for (const item of this.editNC.equipmentStatusImages) {
      item.order = i++;
    }
  }

  private setXfmrPhotos(): void {
    this.xfmrPhotosLinks = {};
    this.editNC.equipmentStatusImages.forEach((image, i) => {
      if (!image.order) {
        this.xfmrPhotosLinks[i.toString()] = image;
      }
      this.xfmrPhotosLinks[(Number(image?.order) - 1).toString()] = image;
    });
  }

  setDisabledFieldsForMcDisconnect(): void {
    const status = this.editNC?.mvDisconnectedStatus;
    status.liquidLevel = this.liquidLevelList.includes(status.liquidLevel) ? status.liquidLevel : null;
    status.sF6Level = this.sF6LevelList.includes(status.sF6Level) ? status.sF6Level : null;
    if (!status.liquidLevel) {
      this.isNotRequired.liquidLevel = true;
    }
    if (!status.sF6Level) {
      this.isNotRequired.sF6Level = true;
    }
  }

  private setDisabledFields(): void {
    if (!this.editNC?.transformerEquipmentStatus?.liquidLevel) {
      this.isNotRequired.liquidLevel = true;
    }
    if (!this.editNC?.transformerEquipmentStatus?.topOilTemperature && !this.editNC?.transformerEquipmentStatus?.maxOilTemperature) {
      this.isNotRequired.oilTemp = true;
    }
    if (!this.editNC?.transformerEquipmentStatus?.pressureAsFound && !this.editNC?.transformerEquipmentStatus?.pressureAsLeft) {
      this.isNotRequired.pressure = true;
    }
    if (!this.editNC?.transformerEquipmentStatus?.detcPosition) {
      this.isNotRequired.detc = true;
    }
    if (!this.editNC?.transformerEquipmentStatus?.groundingTestResults) {
      this.isNotRequired.grdtest = true;
    }
  }

  deleteFile(index: number) {
    const currentImages = this.editNC?.equipmentStatusImages?.length || 0;
    const newFiles = this.files?.length || 0;
    const totalImages = currentImages + newFiles;
    if (totalImages <= 2) {
      this.alertService.showErrorToast(
        `Cannot delete. At least 2 images are required. Please upload a new image before deleting.`
      );
      return;
    }
    this.files.splice(index, 1);
  }

  deleteImageFile(index: number, type: string) {
    if (type === 'cTpsFiles') {
      this.cTpsFiles.splice(index, 1);
    } else if (type === 'fusingFiles') {
      this.fusingFiles.splice(index, 1);
    } else {
      this.instrumentPowerFiles.splice(index, 1);
    }
  }

  getMVDisconnectOtherSectionUpload(event: any, type: string) {
    let existingImagesLength = 0;
    let newFilesLength = event.target.files.length;
    if (this.editNC.esGuid) {
      const cTpsFiles = this.mvDisconnectImages.filter(item => Number(item.sectionId) === 1);
      const fusingFiles = this.mvDisconnectImages.filter(item => Number(item.sectionId) === 2);
      const instrumentPowerFiles = this.mvDisconnectImages.filter(item => Number(item.sectionId) === 3);

      if (type === 'cTpsFiles') {
        existingImagesLength = cTpsFiles.length || 0;
      } else if (type === 'fusingFiles') {
        existingImagesLength = fusingFiles.length || 0;
      } else {
        existingImagesLength = instrumentPowerFiles.length || 0;
      }
    } else {
      if (type === 'cTpsFiles') {
        existingImagesLength = this.cTpsFiles.length || 0;
      } else if (type === 'fusingFiles') {
        existingImagesLength = this.fusingFiles.length || 0;
      } else {
        existingImagesLength = this.instrumentPowerFiles.length || 0;
      }
    }

    const totalImages = existingImagesLength + newFilesLength;

    for (const i of event.target.files) {
      if (i.size >= 10485760) {
        this.alertService.showErrorToast('File size should not be allowed more than 10MB');
        return;
      }
    }
    if (totalImages > 4) {
      this.alertService.showErrorToast('Add Maximum 4 images to include in reports');
      return;
    }
    const data =
      event.target.files.length > 1
        ? { imageFile: null, imageFiles: event.target.files }
        : { imageFile: event.target.files[0], imageFiles: [] };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: data,
      class: 'modal-xl'
    };
    this.modalRef = this.modalService.show(ImageCropperComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (Array.isArray(result)) {
          for (let i = 0; i < event.target.files.length; i++) {
            const imageBlob = this.reportService.getDataURItoBlob(result[i]);
            const imageName = event.target.files[i].name;
            const imageFile = new File([imageBlob], imageName, { type: event.target.files[i].type });
            if (type === 'cTpsFiles') {
              this.cTpsFiles.push(imageFile);
            } else if (type === 'fusingFiles') {
              this.fusingFiles.push(imageFile);
            } else if (type === 'instrumentPowerFiles') {
              this.instrumentPowerFiles.push(imageFile);
            }
          }
        } else {
          const imageBlob = this.reportService.getDataURItoBlob(result);
          const imageName = event.target.files[0].name;
          const imageFile = new File([imageBlob], imageName, { type: result.type });
          if (type === 'cTpsFiles') {
            this.cTpsFiles.push(imageFile);
          } else if (type === 'fusingFiles') {
            this.fusingFiles.push(imageFile);
          } else if (type === 'instrumentPowerFiles') {
            this.instrumentPowerFiles.push(imageFile);
          }
        }
      }
    });
  }

  validateDecimal(event: InputEvent, decimalPlaces = 2) {
    const inputElement = <HTMLInputElement>event.target;
    let inputValue = inputElement.value;
    inputValue = inputValue.replace(/[^0-9.-]/g, '');

    // Ensure there is only one '-' at the beginning and one '.' in the input
    const minusIndex = inputValue.indexOf('-');
    if (minusIndex > 0) {
      inputValue = inputValue.replace(/-/g, '');
    }
    const dotIndex = inputValue.indexOf('.');
    if (dotIndex !== -1) {
      inputValue = inputValue.substring(0, dotIndex + 1) + inputValue.substring(dotIndex + 1).replace(/\./g, '');
    }

    // Ensure at least one digit before the decimal
    if (dotIndex === 0 || (dotIndex === 1 && inputValue[0] === '-')) {
      inputValue = inputValue.slice(0, dotIndex) + '0' + inputValue.slice(dotIndex);
    }
    const decimalPart = inputValue.split('.')[1];
    if (decimalPart && decimalPart.length > decimalPlaces) {
      inputValue = inputValue.substring(0, dotIndex + 1 + decimalPlaces);
    }
    inputElement.value = inputValue;
  }

  // Destroy
  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.subscription.unsubscribe();
  }
}
function __indexOf(collection, node) {
  return Array.prototype.indexOf.call(collection, node);
}

/** Determines whether an event is a touch event. */
function __isTouchEvent(event: MouseEvent | TouchEvent): event is TouchEvent {
  return event.type.startsWith('touch');
}

function __isInsideDropListClientRect(dropList: CdkDropList, x: number, y: number) {
  const { top, bottom, left, right } = dropList.element.nativeElement.getBoundingClientRect();
  return y >= top && y <= bottom && x >= left && x <= right;
}
