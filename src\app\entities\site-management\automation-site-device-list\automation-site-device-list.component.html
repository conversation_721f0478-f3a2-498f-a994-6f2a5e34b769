<div class="row">
  <div class="col-12 mb-3">
    <button
      *ngIf="
        automationDeviceList.length &&
        viewMode === true &&
        checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST])
      "
      nbButton
      status="primary"
      size="medium"
      type="button"
      id="siteSubmit"
      class="float-end"
      (click)="gotoEditableDevice()"
    >
      <span class="d-none d-lg-inline-block">Edit Device List</span>
      <i class="d-inline-block d-lg-none fa-solid fa-pen"></i>
    </button>
    <button
      *ngIf="
        automationDeviceList.length &&
        viewMode === false &&
        checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST])
      "
      nbButton
      status="primary"
      size="medium"
      type="button"
      id="siteSubmit"
      class="float-end"
      (click)="Cancel()"
    >
      <span class="d-none d-lg-inline-block">Cancel</span>
      <i class="d-inline-block d-lg-none fa fa-cancel"></i>
    </button>
    <button
      *ngIf="
        automationDeviceList.length &&
        viewMode === false &&
        checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST])
      "
      nbButton
      status="primary"
      size="medium"
      type="button"
      id="siteSubmit"
      class="float-end me-2"
      (click)="updateAutomationDeviceLIst()"
    >
      <span class="d-none d-lg-inline-block">Save Changes</span>
      <i class="d-inline-block d-lg-none fa-solid fa-save"></i>
    </button>
    <button nbButton status="primary" type="button" (click)="redirectToDeviceImport()" size="medium" class="float-end me-2">
      <span class="d-none d-lg-inline-block">Import Devices From Data Source</span>
      <i class="d-inline-block d-lg-none fa fa-file-import"></i>
    </button>
    <button class="linear-mode-button me-2 float-end" (click)="redirectToDeviceAdd()" nbButton status="primary" size="medium" type="button">
      <span class="d-none d-lg-inline-block">Add Site Device</span>
      <i class="d-inline-block d-lg-none fa fa-plus"></i>
    </button>
    <button
      *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.ANALYST, roleType.PORTFOLIOMANAGER]"
      nbButton
      [nbContextMenu]="bulkSiteAutomationDevicesItems"
      [nbContextMenuTag]="bulkSiteAutomationDevicesMenuTag"
      status="primary"
      size="medium"
      type="button"
      class="linear-mode-button me-2 float-end"
      (click)="onBulkSiteAutomationDevicesMenuOpen()"
    >
      <em class="fa-solid fa-bars"></em>
    </button>
    <input
      type="file"
      #uploadSiteAutomationDevicesFileInput
      class="d-none"
      accept=".xlsx"
      (change)="uploadSiteAutomationDeviceFile($event.target.files)"
    />
    <span *ngIf="missingDCLoadCount" class="dc-load-Count float-end">{{ missingDCLoadCount }}</span>
    <button
      class="linear-mode-button me-2 float-end"
      (click)="reFetchValueCell()"
      [disabled]="isReFetching"
      nbButton
      status="primary"
      size="medium"
      type="button"
    >
      <span class="d-none d-lg-inline-block">{{ isReFetching ? 'Re-Fetching...' : 'Re-fetch' }}</span>
      <nb-icon
        *ngIf="isReFetching"
        class="ms-auto cursor-pointer"
        status="primary"
        icon="sync"
        nbTooltip="Re-Fetch"
        nbTooltipPlacement="top"
        nbTooltipStatus="primary"
        [ngClass]="{ fetchIcon: isReFetching }"
      ></nb-icon>
    </button>
  </div>
  <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
    <table class="table table-hover table-bordered" aria-describedby="Import Device List">
      <thead>
        <tr>
          <th scope="col">QE Device Name</th>
          <th scope="col">Device Type</th>
          <th scope="col">Model</th>
          <th scope="col">Mfg</th>
          <th scope="col">Size</th>
          <th scope="col">AC Nameplate (KW)</th>
          <th scope="col" style="min-width: 80px">DC Load (KW)</th>
          <th scope="col" style="min-width: 80px">Serial Number</th>
          <th scope="col">Data Source</th>
          <th scope="col">Site - Account</th>
          <th scope="col">API ID</th>
          <th scope="col">API Device Name</th>
          <th scope="col">Value</th>
          <th scope="col">Plotting Unit</th>
          <th scope="col">Reporting Unit</th>
          <th scope="col" style="min-width: 80px">Order</th>
          <th id="action" class="text-center action">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of automationDeviceList; let i = index">
          <td data-title="QE Device Name">{{ item.qeDeviceName }}</td>
          <td data-title="Device Type">{{ item.deviceType }}</td>
          <td data-title="Model">{{ item.deviceModel }}</td>
          <td data-title="Mfg">{{ item.mfg || '-' }}</td>
          <td data-title="Size">{{ item.size || '-' }}</td>
          <td data-title="AC Nameplate (KW)">{{ item.acNameplateKW || '-' }}</td>
          <td
            data-title="DC Load (KW)"
            [ngClass]="{ 'missing-dc-load': (item?.deviceType === 'Inverter' || item?.deviceType === 'Meter') && item?.dcLoad === '' }"
          >
            <input
              *ngIf="viewMode === false"
              nbInput
              name="{{ 'dcLoad-' + i }}"
              id="{{ 'input-dcLoad-' + i }}"
              #dcLoad="ngModel"
              [(ngModel)]="item.dcLoad"
              fullWidth
              autocomplete="off"
              [disabled]="item.deviceType === 'Weather Sensors'"
            />
            <span
              *ngIf="
                checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST]) &&
                viewMode === true
              "
              >{{ item.dcLoad || '-' }}</span
            >
          </td>
          <td data-title="Serial Number">
            <input
              *ngIf="viewMode === false"
              nbInput
              name="{{ 'serialNumber-' + i }}"
              id="{{ 'input-serialNumber-' + i }}"
              #serialNumber="ngModel"
              [(ngModel)]="item.serialNumber"
              fullWidth
              autocomplete="off"
            />
            <span
              *ngIf="
                checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST]) &&
                viewMode === true
              "
              >{{ item.serialNumber || '-' }}</span
            >
          </td>
          <td data-title="Data Source">{{ item.datasource }}</td>
          <td data-title="Site - Account">{{ item.partnerName }}</td>
          <td data-title="API ID">{{ item.hardwareId }}</td>
          <td data-title="API Device Name	">{{ item.deviceName }}</td>
          <td data-title="Value" [ngClass]="{ 'missing-dc-load': item?.isDeviceAlert && item.hardwareId }">
            <ng-container *ngIf="!item.isDeviceAlert; else refetchError">
              {{ item.binValue }}
            </ng-container>
            <ng-template #refetchError>
              <div *ngIf="item.refetchError" class="text-danger">
                <div nbTooltip="{{ item.refetchError }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                  <sfl-read-more [content]="item.refetchError" [textLength]="10"></sfl-read-more>
                </div>
              </div>
            </ng-template>
          </td>
          <td data-title="Plotting Unit">
            <ng-select
              *ngIf="viewMode === false"
              name="{{ 'plottingUnit-' + i }}"
              id="{{ 'input-plottingUnit-' + i }}"
              #plottingUnit="ngModel"
              bindLabel="unitName"
              bindValue="siteDeviceUnitId"
              (change)="item.plottingUnit = $event.unitName"
              [items]="listOfPlottingUnit[i]"
              [(ngModel)]="item.plottingUnitId"
              notFoundText="No plotting unit"
              appendTo="body"
              [clearable]="false"
              required
              [disabled]="!item.hardwareId"
            >
            </ng-select>
            <span
              *ngIf="
                checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST]) &&
                viewMode === true
              "
            >
              {{ item.plottingUnit || '-' }}
            </span>
          </td>
          <td data-title="Reporting Unit">
            <ng-select
              *ngIf="viewMode === false"
              name="{{ 'reportingUnit-' + i }}"
              id="{{ 'input-reportingUnit-' + i }}"
              #reportingUnit="ngModel"
              bindLabel="unitName"
              bindValue="siteDeviceUnitId"
              (change)="item.reportingUnit = $event.unitName"
              [items]="listOfReportingUnit[i]"
              [(ngModel)]="item.reportingUnitId"
              notFoundText="No Reporting Unit"
              appendTo="body"
              [clearable]="false"
              required
              [disabled]="
                checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST]) &&
                !item.hardwareId
              "
            >
            </ng-select>
            <span
              *ngIf="
                checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST]) &&
                viewMode === true
              "
            >
              {{ item.reportingUnit || '-' }}
            </span>
          </td>
          <td data-title="Order">
            <input
              *ngIf="viewMode === false"
              nbInput
              name="{{ 'rank-' + i }}"
              id="{{ 'input-rank-' + i }}"
              #rank="ngModel"
              [(ngModel)]="item.rank"
              fullWidth
              autocomplete="off"
              OnlyNumber
            />
            <span
              *ngIf="
                checkAuthorisationsFn([roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER, roleType.ANALYST]) &&
                viewMode === true
              "
            >
              {{ item.rank || '-' }}
            </span>
          </td>
          <td data-title="Action" class="text-center">
            <div class="d-md-flex justify-content-center">
              <a class="px-2 listgrid-icon" (click)="viewSiteDeviceDetails(item.qeDeviceId)">
                <em class="fa fa-eye" nbTooltip="Click to view device details" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
              </a>
              <a
                class="listgrid-icon px-2"
                *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                [routerLink]="['/entities/site-device/edit/' + item.qeDeviceId]"
              >
                <em class="fa fa-edit" nbTooltip="Edit" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
              </a>
            </div>
          </td>
        </tr>
        <tr *ngIf="!automationDeviceList.length">
          <td colspan="15" class="no-record text-center">No Data Found</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
