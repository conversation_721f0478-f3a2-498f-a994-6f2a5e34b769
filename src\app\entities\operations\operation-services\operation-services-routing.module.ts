import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AddEditOperationServicesComponent } from './add-edit-operation-services/add-edit-operation-services.component';
import { OperationServicesComponent } from './operation-services.component';

const routes: Routes = [
  {
    path: '',
    component: OperationServicesComponent
  },
  {
    path: 'add',
    component: AddEditOperationServicesComponent
  },
  {
    path: 'edit/:id',
    component: AddEditOperationServicesComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class OperationServicesRoutingModule {}
