import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { NbThemeService } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { AppConstants } from '../../../../../@shared/constants';
import { ReschedulerModalComponent } from '../../../../workorder-management/rescheduler-modal/rescheduler-modal.component';
import { BulkRescheduleDetails, WO_STATUSES } from '../../../../workorder-management/workorder.model';
import {
  EChartObject,
  FilterModel,
  PmCompletionWoPaginationParam,
  PmEmitEvent,
  PMTableData,
  PMTableSortingKey,
  PMTableStatus,
  PMTableStatusStr,
  PmWoTableSearch
} from '../../operation-reports.model';
import { OperationsReportsService } from '../../operations-reports.service';

@Component({
  selector: 'sfl-work-order-completion-plan',
  templateUrl: './work-order-completion-plan.component.html',
  styleUrls: ['./work-order-completion-plan.component.scss']
})
export class WorkOrderCompletionPlanComponent implements OnInit, OnChanges, OnDestroy {
  @Input() workOrderPlanChart: any = {};
  @Input() workOrderPlanInfo: EChartObject[] = [];
  @Input() assessmentType: string;
  @Input() chartLabel: string;
  @Input() forecastChart: any = {};
  @Input() selectedYear: number;
  @Input() filterModel: FilterModel;
  @Input() appliedFilterModel: FilterModel;
  @Output() emitUpdatedFilter = new EventEmitter<PmEmitEvent>();
  subscription: Subscription = new Subscription();
  loading = false;
  workOrderChart: any;
  completionChart: any;
  currentTheme = 'dark';
  addStack = ['Planned', 'Tentative', 'Pending Reschedule', 'New Schedule', 'Re-Scheduled'];
  rescheduleData = new PMTableData();
  selectedAllWorkOrders = false;
  selectedStatus: string;
  selectedWorkOrderIds: number[] = [];
  paginationParams: PmCompletionWoPaginationParam = {
    pageSize: 5,
    currentPage: 1,
    woStatus: 0
  };
  sortOptionList = {
    customername: 'ASC',
    sitename: 'ASC',
    assesmenttype: 'ASC',
    tentativemonth: 'ASC',
    datescheduled: 'ASC',
    frequency: 'ASC'
  };
  searchParams = new PmWoTableSearch();
  pmTableSortingKey = PMTableSortingKey;
  searchModelChanged = new Subject<void>();
  fullDateFormat = AppConstants.fullDateFormat;
  modalRef: BsModalRef;
  PMTableStatusStr = PMTableStatusStr;
  isSelectAllDisabled = true;
  constructor(
    private readonly themeService: NbThemeService,
    private readonly operationsReportsService: OperationsReportsService,
    private readonly modalService: BsModalService
  ) {}

  ngOnInit(): void {
    this.selectedStatus = this.workOrderPlanInfo[this.filterModel.pmTableStatus].name;
    this.paginationParams.pageSize = this.filterModel.itemsCount;
    this.getWorkOrders();
    this.themeService.onThemeChange().subscribe(themeName => {
      this.currentTheme = themeName.name;
    });
    this.searchModelChanged.pipe(debounceTime(500)).subscribe(() => {
      this.filterModel.search = this.searchParams;
      this.updateValueInParentAndGetAll();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes.workOrderPlanChart && changes.workOrderPlanChart.currentValue) ||
      (changes.forecastChart && changes.forecastChart.currentValue)
    ) {
      this.loading = true;
      const data = {
        color: ['#FFFFFF', '#BDD7EE', '#49D36A', '#B8EE82', '#4472C4', '#FF4343', '#FF8A45'],
        grid: {
          containLabel: true,
          height: '55%',
          top: 30,
          right: '10%'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            showContent: true
          }
        },
        legend: {
          data: [],
          // type: 'scroll',
          bottom: 10
        },
        xAxis: [
          {
            type: 'value',
            show: false
          }
        ],
        yAxis: {
          type: 'category',
          show: true,
          data: [{ value: '', textStyle: {} }],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: []
      };
      data.legend.data = this.workOrderPlanChart['dataOf'];
      data.xAxis[0]['data'] = this.workOrderPlanChart['labels'];
      this.workOrderPlanChart['eChartObjects']?.forEach((item, index) => {
        data.series.push({
          ...item,
          type: 'bar',
          stack: 'Total',
          emphasis: {
            focus: 'series'
          },
          label: {
            show: false,
            position: 'top'
          },
          itemStyle: {
            borderColor: 'rgba(0, 0, 0, 0.3)',
            borderWidth: 0.5
          }
        });

        if (this.workOrderPlanChart['eChartObjects']?.length - 1 === index) this.workOrderChart = data;
      });
      this.loading = false;
    }
    const completionChart = {
      title: {
        text: `${this.chartLabel}`,
        left: 'center',
        top: 45,
        textStyle: {
          fontSize: '0.9375rem'
        }
      },
      color: [
        '#4472C4',
        '#BDD7EE',
        '#B49FEF',
        '#D9D9D9',
        '#49D36A',
        '#B8EE82',
        '#FF4343',
        '#FF8A45',
        '#95B7FA',
        '#8FDCB2',
        '#DED5F8',
        '#DCE5F4'
      ],
      grid: {
        y: 100,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'none',
          showContent: true
        }
      },
      legend: {
        data: [],
        type: 'scroll',
        bottom: 10
      },
      toolbox: {
        show: true,
        orient: 'horizontal',
        feature: {
          dataZoom: { show: true },
          dataView: {
            show: true,
            readOnly: true,
            optionToContent: opt => {
              let series = opt.series;
              let axisData = opt.xAxis[0]['data'];

              let table = '<table id="site-performance-table" class="table table-bordered"><thead><tr>' + '<th>Month</th>';
              for (let i = 0, l = series.length; i < l; i++) {
                table += `<th>${series[i].name}</th>`;
              }
              table += '</tr></thead><tbody style="color:#000000">';
              for (let i = 0, l = series[0].data.length; i < l; i++) {
                table += '<tr>';
                table += `<td style="color:#000000">${axisData[i]}</td>`;
                for (let j = 0, m = series.length; j < m; j++) {
                  table += `<td style="text-align:right; color:#000000;">${series[j].data[i].toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  })}</td>`;
                }
                table += '</tr>';
              }
              table += '</thead></table>';
              return table;
            }
          },
          restore: {},
          saveAsImage: {}
        },
        top: 7,
        right: 40
      },
      xAxis: [
        {
          type: 'category'
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: `# of ${this.assessmentType}`,
          nameTextStyle: {
            fontWeight: 'bolder',
            align: 'right',
            padding: [0, 6, 0, 0]
          }
        },
        {
          type: 'value',
          name: `# of ${this.assessmentType} Completion %`,
          position: 'right',
          splitLine: {
            show: false
          },
          align: 'center',
          axisLabel: {
            formatter: '{value}%'
          },
          min: 0,
          max: 100
        }
      ],
      series: []
    };
    completionChart.legend.data = this.forecastChart['dataOf'];
    completionChart.xAxis[0]['data'] = this.forecastChart['labels'];

    this.forecastChart['eChartObjects']?.forEach((item, index) => {
      const isPercentage = item.name.includes('%');
      completionChart.series.push({
        ...item,
        type: isPercentage ? 'line' : 'bar',
        stack: isPercentage ? '' : this.addStack.includes(item.name) ? 'QE' : 'SOLAR',
        yAxisIndex: isPercentage ? 1 : 0,
        emphasis: {
          focus: 'series'
        },
        smooth: true,
        showSymbol: false,
        tooltip: {
          valueFormatter: function (value: any) {
            return (value as number).toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          }
        }
      });

      if (this.forecastChart['eChartObjects']?.length - 1 === index) this.completionChart = completionChart;
    });
  }

  trackByFunc(index, item) {
    return item.id;
  }

  selectAllWo() {
    this.selectedWorkOrderIds = [];
    if (this.selectedAllWorkOrders) {
      this.rescheduleData.pmTableDto.forEach(wo => {
        if (!wo.isCheckboxDisabled) {
          this.selectedWorkOrderIds.push(wo.workOrderId);
        }
      });
    }

    this.rescheduleData.pmTableDto.forEach(wo => {
      if (!wo.isCheckboxDisabled) {
        wo.isSelected = this.selectedAllWorkOrders;
      }
    });
    if (this.selectedWorkOrderIds.length) {
      this.selectedAllWorkOrders = true;
    } else {
      this.selectedAllWorkOrders = false;
    }
  }

  woCheckboxChanged(event, index: number) {
    this.rescheduleData.pmTableDto[index].isSelected = event.target.checked;
    if (event.target.checked) {
      this.selectedWorkOrderIds.push(this.rescheduleData.pmTableDto[index].workOrderId);
    } else {
      this.selectedWorkOrderIds.splice(this.selectedWorkOrderIds.indexOf(this.rescheduleData.pmTableDto[index].workOrderId), 1);
    }

    const notSelectedSite = this.rescheduleData.pmTableDto.findIndex(x => !x.isSelected);
    if (notSelectedSite !== -1) {
      this.selectedAllWorkOrders = false;
    } else {
      this.selectedAllWorkOrders = true;
    }
  }

  get filterHasNotChanged(): boolean {
    return JSON.stringify(this.filterModel) === JSON.stringify(this.appliedFilterModel);
  }

  private getWorkOrders(): void {
    this.loading = true;
    this.subscription.add(
      this.operationsReportsService.getPmWorkOrders(this.filterModel).subscribe(response => {
        if (response.total) {
          response.pmTableDto.forEach(wo => {
            wo.isSelected = this.selectedWorkOrderIds.includes(wo.workOrderId);
            wo.isCheckboxDisabled =
              (wo.isReschedule && !wo.rescheduleDate && this.selectedStatus !== this.PMTableStatusStr.PendingReschedule) ||
              wo.workOrderStatus === WO_STATUSES.REPORT_COMPLETE.id;
            wo.customerPortFolio = `${wo.customerName} (${wo.portfolioName})`;
          });
          const notSelectedSite = response.pmTableDto.findIndex(x => !x.isSelected);
          if (notSelectedSite !== -1) {
            this.selectedAllWorkOrders = false;
          } else {
            this.selectedAllWorkOrders = true;
          }
        } else {
          this.selectedAllWorkOrders = false;
        }
        this.rescheduleData = response;

        this.isSelectAllDisabled = response.pmTableDto.every(wo => wo.isCheckboxDisabled);
        this.loading = false;
      })
    );
  }

  onStatusChange(statusName: string) {
    if (statusName !== this.selectedStatus && this.filterHasNotChanged) {
      this.setWoStatusParam(statusName);
      this.selectedStatus = statusName;
      this.selectedWorkOrderIds = [];
    }
  }

  private setPaginationParams() {
    this.filterModel.itemsCount = this.paginationParams.pageSize;
    this.filterModel.page = this.paginationParams.currentPage - 1;
    this.filterModel.pmTableStatus = this.paginationParams.woStatus;
    this.updateValueInParentAndGetAll();
  }

  private setWoStatusParam(statusName: string) {
    this.paginationParams.woStatus = PMTableStatus[statusName] ?? PMTableStatus.Total;
    this.paginationParams.currentPage = 1;
    this.filterModel.search = this.searchParams = new PmWoTableSearch();
    this.setPaginationParams();
  }

  onChangeSize() {
    this.paginationParams.currentPage = 1;
    this.setPaginationParams();
  }

  onPageChange(pageNumber: number) {
    this.paginationParams.currentPage = pageNumber;
    this.setPaginationParams();
  }

  openRescheduleModal() {
    const bulkRescheduleDetails: BulkRescheduleDetails = {
      WoStatus: this.paginationParams.woStatus,
      selectedWoIds: this.selectedWorkOrderIds
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      ignoreBackdropClick: true,
      initialState: {
        bulkRescheduleDetails
      },
      class: 'rescheduler-modal'
    };

    this.modalRef = this.modalService.show(ReschedulerModalComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(async res => {
      if (res) {
        this.selectedWorkOrderIds = [];
        this.selectedAllWorkOrders = false;
        this.updateValueInParentAndGetAll(true);
      }
    });
  }

  sort(sortBy: PMTableSortingKey) {
    if (this.sortOptionList[sortBy] === 'ASC') {
      this.sortOptionList[sortBy] = 'DESC';
    } else {
      this.sortOptionList[sortBy] = 'ASC';
    }
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = this.sortOptionList[sortBy];
    this.updateValueInParentAndGetAll();
  }

  onSearchChanged() {
    this.paginationParams.currentPage = 1;
    this.searchModelChanged.next();
  }

  updateValueInParentAndGetAll(reload = false) {
    this.emitUpdatedFilter.emit({ filter: this.filterModel, reload });
    if (!reload) {
      this.getWorkOrders();
    }
  }

  ngOnDestroy(): void {
    this.searchModelChanged.complete();
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
