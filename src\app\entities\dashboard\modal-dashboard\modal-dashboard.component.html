<div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <div class="d-flex">
      <button
        type="button"
        class="close me-1"
        aria-label="Back"
        (click)="isCompactView = true; isWorkOrderDetail = false"
        *ngIf="!isCompactView && isWorkOrderDetail"
      >
        <span aria-hidden="true"><i class="fa-solid fa-arrow-left fa-xl"></i></span>
      </button>
      <h6 class="modal-title ModalBody">Work Order Detail {{ isCompactView ? 'For ' + (day ? day + '/' + year : month) : '' }}</h6>
    </div>
    <div class="d-flex align-items-center justify-content-center">
      <button
        *ngIf="message"
        nbButton
        (click)="openDropBoxImageGallery()"
        [disabled]="message?.workorders[0]?.imageGalleryCount === 0"
        status="primary"
        size="small"
        id="DropBoxImageGallery"
        class="me-2"
      >
        <span class="d-flex"><em class="pi pi-images me-2"></em> ({{ message?.workorders[0].imageGalleryCount }})</span>
      </button>
      <button type="button" class="close" aria-label="Close" (click)="onClose()">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>
  </div>
  <ng-container>
    <ng-container *ngTemplateOutlet="isCompactView ? compactViewModal : detailViewModal"></ng-container>
  </ng-container>

  <ng-template #compactViewModal>
    <div class="modal-body">
      <ul class="workOrderList">
        <li class="workOrderDetail">
          <div class="row align-items-center">
            <div class="col-6">
              <strong>Customer</strong>
            </div>
            <div class="col-6">
              <span class="text-pre-wrap">{{ assessmentDTO?.customerName }}</span>
            </div>
          </div>
        </li>
        <li class="workOrderDetail">
          <div class="row align-items-center">
            <div class="col-6">
              <strong>Portfolio</strong>
            </div>
            <div class="col-6">
              <span class="text-pre-wrap">{{ assessmentDTO?.portfolioName }}</span>
            </div>
          </div>
        </li>
        <li class="workOrderDetail">
          <div class="row align-items-center">
            <div class="col-6">
              <strong>Site</strong>
            </div>
            <div class="col-6">
              <span class="text-pre-wrap">{{ assessmentDTO?.siteName }}</span>
            </div>
          </div>
        </li>
        <li class="workOrderDetail">
          <div class="row align-items-center">
            <div class="col-6">
              <strong>kwDc</strong>
            </div>
            <div class="col-6">
              <span class="text-pre-wrap">{{ assessmentDTO?.dcSize | sflRound | sflNumberWithCommas }}</span>
            </div>
          </div>
        </li>
        <li class="workOrderDetail">
          <div class="row align-items-center">
            <div class="col-6">
              <strong>State</strong>
            </div>
            <div class="col-6">
              <span class="text-pre-wrap">{{ assessmentDTO?.stateAbb }}</span>
            </div>
          </div>
        </li>
        <li class="workOrderDetail" *ngFor="let workOrderType of workOrderTypeList">
          <div class="row align-items-center">
            <div class="col-6">
              <strong>{{ workOrderType.woName }}</strong>
            </div>
            <div
              class="col-6 text-start"
              [class.text-center]="
                !(
                  (assessmentDTO[workOrderType.task] === 'NA' && assessmentDTO[workOrderType.task] === 'Alltime') ||
                  !(assessmentDTO[workOrderType.keyName] | sortByMonth : month : day).length
                )
              "
            >
              <ng-container *ngFor="let wo of assessmentDTO[workOrderType.keyName] | sortByMonth : month : day">
                <span
                  class="counts my-1 badge-border text-white"
                  [ngClass]="{
                    QQ:
                      wo?.assementTypestr === 'Q1' ||
                      wo?.assementTypestr === 'Q2' ||
                      wo?.assementTypestr === 'Q3' ||
                      wo?.assementTypestr === 'Q4',
                    AQ: wo?.assementTypestr === 'A',
                    HQ: wo?.assementTypestr === 'H1' || wo?.assementTypestr === 'H2',
                    HasNoData: wo.status === WoStatuses.NOT_FOUND.id,
                    ReportCompleted: wo.status === WoStatuses.REPORT_COMPLETE.id,
                    WorkCompleted: wo.status === WoStatuses.FIELD_WORK_COMPLETE.id,
                    NotYet: wo.status === WoStatuses.PENDING.id,
                    PartiallyComplete: wo.status === WoStatuses.FIELD_WORK_PARTIALLY_COMPLETE.id,
                    Late: wo.status === WoStatuses.DRAFT.id,
                    'border-orange': wo?.isFieldTechFound,
                    'border-yellow': WoStatuses.PENDING.id && wo.tentativeMonth && !(wo?.rescheduleDate || wo?.dateScheduled)
                  }"
                  *ngIf="
                    workOrderType.woType === wo.woType &&
                    assessmentDTO[workOrderType.task] !== 'NA' &&
                    assessmentDTO[workOrderType.task] !== 'Alltime'
                  "
                  (click)="onWOClick(wo)"
                >
                  {{ wo?.assementTypestr }}
                </span>
              </ng-container>
              <span
                class="workOrderNotRequire"
                *ngIf="
                  (assessmentDTO[workOrderType.task] === 'NA' && assessmentDTO[workOrderType.task] === 'Alltime') ||
                  !(assessmentDTO[workOrderType.keyName] | sortByMonth : month : day).length
                "
              >
                -
              </span>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </ng-template>

  <ng-template #detailViewModal>
    <div class="modal-body ModalBody">
      <ul class="workOrderList" *ngFor="let item of message?.workorders">
        <ng-container *ngFor="let data of item.data | orderBy : 'order'">
          <li class="workOrderDetail" *ngIf="data?.label === 'Field Tech' ? !checkAuthorisationsFn([roleType.CUSTOMER]) : true">
            <div class="row">
              <div class="col-6 workOrderDetailLabel">
                <strong>{{ data?.label }}</strong>
              </div>
              <div class="col-6 workOrderDetailValue overflow-auto" *ngIf="!data?.href">
                <span *ngIf="data?.label === 'Failure Rate %'" class="text-pre-wrap" [innerText]="data?.value + '%' || '-'"></span>
                <span *ngIf="data?.label !== 'Failure Rate %'" class="text-pre-wrap" [innerText]="data?.value || '-'"></span>
              </div>
              <div class="col-6" *ngIf="data?.href">
                <div class="pointerReportLink mb-1" *ngFor="let link of data?.href">
                  <a class="btn btn-light text-break" *ngIf="link?.reportLabel" (click)="onPDFOrPPTReportLink(link.reportLink, true)">
                    <span class="fa fa-file-pdf pe-2 pdf-icon"></span>{{ link?.reportLabel }}
                  </a>
                  <a
                    class="btn btn-light text-break"
                    target="_blank"
                    *ngIf="!link?.reportLabel"
                    [href]="
                      link?.reportLink.startsWith('http://') || link?.reportLink.startsWith('https://')
                        ? link?.reportLink
                        : 'http://' + link?.reportLink
                    "
                  >
                    <span class="fa fa-external-link pe-2"></span>
                    {{ link.reportLink.length > 20 ? (link.reportLink | slice : 0 : 20) + '...' : link.reportLink }}
                  </a>
                </div>
              </div>
            </div>
          </li>
        </ng-container>
      </ul>
    </div>
    <div class="modal-footer ModalFooter">
      <button
        class="flex-start"
        nbButton
        status="primary"
        size="small"
        *ngIf="allowStartNewReport && !checkAuthorisationsFn([roleType.CUSTOMER])"
        (click)="startNewReport()"
      >
        {{ newReportBtnText }}
      </button>
      <button class="flex-start" nbButton status="primary" size="small" *ngIf="allowRescheduleWO(message)" (click)="checkWOLockingStatus()">
        Reschedule
      </button>
      <button
        class="flex-start"
        nbButton
        status="primary"
        size="small"
        *ngIf="
          editPermission &&
          message?.workorders[0]?.data[2]?.label === 'Type of Work Order' &&
          message?.workorders[0]?.data[2]?.value === 'Medium Voltage PM' &&
          message?.workorders[0]?.data[11]?.label === 'Work Order Status' &&
          message?.workorders[0]?.data[11]?.value !== 'Report Complete' &&
          message?.workorders[0]?.data[6]?.label === 'Scheduled Date' &&
          message?.workorders[0]?.data[6]?.value !== '' &&
          message?.workorders[0]?.data[6]?.value !== '-' &&
          checkAuthorisationsFn([roleType.PORTFOLIOMANAGER, roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT])
        "
        (click)="showRescheduler()"
      >
        Reschedule
      </button>
      <button
        nbButton
        status="primary"
        size="small"
        *ngIf="
          (message?.workorders[0]?.data[11]?.value === 'Field Work Complete' ||
            message?.workorders[0]?.data[11]?.value === 'Report Complete') &&
          !checkAuthorisationsFn([roleType.CUSTOMER]) &&
          (message?.workorders[0].assementType === 'SV' ||
            message?.workorders[0].assementType === 'VGT' ||
            message?.workorders[0].assementType === 'MVPM')
        "
        (click)="onViewReport(message?.workorders[0].assementType)"
      >
        Review Report
      </button>
      <button
        nbButton
        status="primary"
        size="small"
        *ngIf="
          (message?.workorders[0]?.data[11]?.value === 'Field Work Complete' ||
            message?.workorders[0]?.data[11]?.value === 'Report Complete' ||
            message?.workorders[0]?.data[9]?.value === 'Field Work Complete' ||
            message?.workorders[0]?.data[9]?.value === 'Report Complete') &&
          (message?.workorders[0].assementType === 'IPM' || message?.workorders[0].assementType === 'TRQ')
        "
        (click)="
          message?.workorders[0].assementType === 'TRQ'
            ? openTemplateModal(viewModuleTorqueFormsTemplate, message?.workorders[0].id, message?.workorders[0].assementType === 'TRQ')
            : openTemplateModal(viewFormsTemplate, message?.workorders[0].id)
        "
      >
        Review Forms
      </button>
      <button
        nbButton
        status="primary"
        size="small"
        *ngIf="editPermission && !checkAuthorisationsFn([roleType.CUSTOMER])"
        (click)="onEdit()"
      >
        Edit
      </button>
      <button nbButton size="small" (click)="onClose()">Close</button>
    </div>
  </ng-template>
</div>

<ng-template #viewFormsTemplate>
  <div class="alert-box">
    <div class="modal-header align-items-start">
      <div>
        <h6 class="modal-title">Forms</h6>
      </div>
      <div>
        <button
          *ngIf="uploadedFormsData?.length && completedFormMapId.length > 1"
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="downloadAllForms()"
          type="button"
        >
          Download All
        </button>
        <button type="button" class="close" aria-label="Close" (click)="viewFromModalRef.hide(); _bsModalRef.setClass('d-block')">
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
    <div class="modal-body row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Forms List">
          <thead>
            <tr>
              <th class="text-center" id="FormName">Form Name</th>
              <th class="text-center" id="InverterName">Inverter Name</th>
              <th class="text-center" id="Status" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">Status</th>
              <th class="text-center" id="UploadedDate" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">Uploaded Date</th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let viewForms of uploadedFormsData
                  | paginate
                    : {
                        id: 'viewForms',
                        itemsPerPage: pagination.itemsCount,
                        currentPage: pagination.currentPage,
                        totalItems: pagination.totalCount
                      }
              "
            >
              <td class="text-center" data-title="Form Name">{{ viewForms.formName }}</td>
              <td class="text-center" data-title="Inverter Name">{{ viewForms.deviceName }}</td>
              <td class="text-center" data-title="Status" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                {{ getStatusLabel(viewForms.formStatus) }}
              </td>
              <td class="text-center" data-title="Uploaded Date" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                {{ viewForms.updatedDate ? (viewForms.updatedDate | date : dateFormat) : '-' }}
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    *ngIf="viewForms.formStatus === 3"
                    class="fa fa-download cursor-pointer text-primary me-2"
                    nbTooltip="Download"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="downloadUploadedForm(viewForms)"
                  ></em>
                  <em
                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="goToFormFillingPage(viewForms, pageOpenFromConstant.VIEW_MODEL_SCREEN)"
                  ></em>
                  <em
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT]"
                    class="fa fa-trash text-danger cursor-pointer me-2"
                    nbTooltip="Delete"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="danger"
                    (click)="onDeleteUploadedForm(viewForms?.qestwoMapId)"
                  ></em>
                  <em
                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && viewForms.formStatus === 3"
                    class="fa-solid fa-clock text-primary cursor-pointer"
                    nbTooltip="Form History"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="getHistoryForQESTForms(historyModalTemplate, viewForms)"
                  ></em>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="4" *ngIf="!uploadedFormsData?.length" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="uploadedFormsData?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pagination.pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ pagination.totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls id="viewForms" (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #historyModalTemplate>
  <div class="alert-box">
    <div class="modal-header align-items-start">
      <div>
        <h6 class="modal-title">{{ viewFromDetails.formName | uppercase }} FORM HISTORY</h6>
      </div>
      <button
        type="button"
        class="close"
        aria-label="Close"
        (click)="
          historyModalRef.hide();
          openTemplateModal(
            message?.workorders[0].assementType === 'TRQ' ? viewModuleTorqueFormsTemplate : viewFormsTemplate,
            null,
            true,
            false
          )
        "
      >
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body px-3 py-1">
      <div id="fixed-table" setTableHeight class="table-card-view" *ngIf="formEditNoteHistory.length">
        <ul class="list-unstyled m-0" *ngFor="let history of formEditNoteHistory">
          <li class="py-2 list-border">
            <p class="mb-2" *ngFor="let note of history.auditLogDetails">
              <strong>{{ note.newValue }}</strong>
            </p>
            <small class="pb-2">{{ history.userName }} - {{ history.logDate }}</small>
          </li>
        </ul>
      </div>
      <div *ngIf="!formEditNoteHistory.length" class="text-center">
        <p>No changes found for this form.</p>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #viewModuleTorqueFormsTemplate>
  <div class="alert-box">
    <div class="modal-header align-items-start">
      <div>
        <h6 class="modal-title">Forms MT</h6>
      </div>
      <div>
        <button
          *ngIf="selectedZones?.length && completedMTFormMapId.length > 1"
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="downloadAllForms(true)"
          type="button"
        >
          Download All
        </button>
        <button type="button" class="close" aria-label="Close" (click)="viewModuleTorqueFormsRef.hide(); _bsModalRef.setClass('d-block')">
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
    <div class="modal-body row">
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Forms List">
          <thead>
            <tr>
              <th class="text-center" id="FormName">Form Name</th>
              <th class="text-center" id="ZoneName">Zone Name</th>
              <th class="text-center" id="Status">Status</th>
              <th class="text-center" id="UploadedDate">Uploaded Date</th>
              <th class="text-center" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let viewForms of selectedZones
                  | paginate
                    : {
                        id: 'viewForms',
                        itemsPerPage: pagination.itemsCount,
                        currentPage: pagination.currentPage,
                        totalItems: pagination.totalCount
                      }
              "
            >
              <td class="text-center" data-title="Form Name">{{ viewForms.formName }}</td>
              <td class="text-center" data-title="Zone Name">{{ viewForms.zoneName }}</td>
              <td class="text-center" data-title="Status">
                {{ getStatusLabel(viewForms.formStatus) }}
              </td>
              <td class="text-center" data-title="Uploaded Date">
                {{ viewForms.updatedDate ? (viewForms.updatedDate | date : dateFormat) : '-' }}
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    *ngIf="viewForms.formStatus === 3"
                    class="fa fa-download cursor-pointer text-primary me-2"
                    nbTooltip="Download"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="downloadUploadedForm(viewForms, true)"
                  ></em>
                  <em
                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="
                      this.qestFormId = viewForms.qestFormId; goToFormFillingPage(viewForms, pageOpenFromConstant.VIEW_MT_FORMS_MODEL)
                    "
                  ></em>
                  <em
                    *appHasPermission="[roleType.ADMIN, roleType.MANAGER, roleType.SUPPORT]"
                    class="fa fa-trash text-danger cursor-pointer me-2"
                    nbTooltip="Delete"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="danger"
                    (click)="onDeleteUploadedForm(viewForms?.qestwoMapId)"
                  ></em>
                  <em
                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && viewForms.formStatus === 3"
                    class="fa-solid fa-clock text-primary cursor-pointer"
                    nbTooltip="Form History"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    (click)="getHistoryForQESTForms(historyModalTemplate, viewForms, true)"
                  ></em>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="4" *ngIf="!selectedZones?.length" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="selectedZones?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pagination.pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize(true)">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ pagination.totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls id="viewForms" (pageChange)="onPageChange($event, true)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </div>
</ng-template>
