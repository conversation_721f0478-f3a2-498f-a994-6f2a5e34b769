import { Location } from '@angular/common';
import { After<PERSON>ontent<PERSON>hecked, ChangeDetector<PERSON><PERSON>, Component, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import {
  BehaviorSubject,
  Observable,
  Subject,
  Subscription,
  catchError,
  concat,
  concatMap,
  distinctUntilChanged,
  finalize,
  forkJoin,
  of,
  switchMap,
  takeUntil,
  tap
} from 'rxjs';
import { CommonDropboxFileUploadComponent } from '../../../@shared/components/common-dropbox-file-upload/common-dropbox-file-upload.component';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { ImageDropboxGalleryComponent } from '../../../@shared/components/image-dropbox-gallery/image-dropbox-gallery.component';
import { APP_ROUTES, AppConstants } from '../../../@shared/constants';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { MessageVM } from '../../../@shared/models/messageVM.model';
import { ChunkUploadProgressDetails } from '../../../@shared/models/share';
import { AlertService } from '../../../@shared/services/alert.service';
import { CommonService } from '../../../@shared/services/common.service';
import { DropboxImageGalleryService } from '../../../@shared/services/dropbox-image-gallery.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { NotesEntityName, NotesEntityType } from '../../notes-management/notes-management.model';
import { PortfolioService } from '../../portfolio-management/portfolio.service';
import { JhaService } from '../../safety/jha/jha.service';
import { SiteService } from '../../site-management/site.service';
import { ActivityLogComponent } from '../activity-log/activity-log.component';
import { AddEditRmaSideModelComponent } from '../add-edit-rma-side-model/add-edit-rma-side-model.component';
import { CommentComponent } from '../comment/comment.component';
import { DeviceModelComponent } from '../device-model/device-model.component';
import { EstimateModelComponent } from '../estimate-model/estimate-model.component';
import { ExclusionModelComponent } from '../exclusion-model/exclusion-model.component';
import { LinkIssueComponent } from '../link-issue/link-issue.component';
import { BulkTicketCloseReOpenActionComponent } from '../ticket-listing/bulk-ticket-close-re-open-action/bulk-ticket-close-re-open-action.component';
import {
  AttachmentListResponse,
  ContactDropdown,
  CustomerContact,
  CustomerContactList,
  CustomerDropdown,
  DeviceListForTicketActivity,
  FileListPaginationParams,
  ProductionLossList,
  ReletedTicketsMapDto,
  TicketAction,
  TicketActivityLogs,
  TicketAdd,
  TicketAlerts,
  TicketAttachment,
  TicketBillingStatusesResponse,
  TicketComment,
  TicketCommentType,
  TicketCostType,
  TicketDeviceMaps,
  TicketDeviceModelList,
  TicketDeviceTypeList,
  TicketEstimates,
  TicketExclusions,
  TicketMaterials,
  TicketPriorityMapping,
  TicketSiteDeviceOutage,
  TicketStatusMapping,
  jhaDetail,
  ReopenedDirectionType,
  statusDropDownList
} from '../ticket.model';
import { TicketService } from '../ticket.service';
import { ExclusionsService } from '../../availability/exclusions/exclusions.service';
import moment from 'moment';
import { environment } from '../../../../environments/environment';
import { ROLE_TYPE } from '../../../@shared/enums';
import { checkAuthorisations } from '../../../@shared/utils';
@Component({
  selector: 'sfl-ticket-detail',
  templateUrl: './ticket-detail.component.html',
  styleUrls: ['./ticket-detail.component.scss']
})
export class TicketDetailComponent implements OnInit, OnDestroy, AfterContentChecked {
  @ViewChild('addAlertTemplate') addAlertTemplate: TemplateRef<any>;
  loading = false;
  exclusionTypeData: string;
  siteInformation = true;
  deviceInformation = true;
  ticketInformation = true;
  attachments = true;
  otherInformation = true;
  ticketActivityInformation = true;
  timeTrackingAccordion = true;
  impactAccordion = true;
  peopleAccordion = true;
  historyAccordion = false;
  ticketExclusion = true;
  showJha = false;
  estimateInformation = true;
  issueAccordion = true;
  commentTypeList = TicketCommentType;
  commentText: string;
  commentTypeId = 0;
  ticketId: number;
  ticketNumber: string;
  isCreate = false;
  isDetail = false;
  isEdit = false;
  isViewEdit = false;
  ticketModel: TicketAdd = new TicketAdd();
  ticketDetailModel: TicketAdd = new TicketAdd();
  ticketComments: TicketComment[] = [];
  ticketActivityLogs: TicketActivityLogs[] = [];
  ticketActions: TicketAction[] = [];
  customerList: CustomerDropdown[] = [];
  portfolioList: ContactDropdown[] = [];
  siteList: ContactDropdown[] = [];
  exclusionTypeList: Dropdown[] = [];

  deviceTypeList: TicketDeviceTypeList[] = [];
  deviceModelList: TicketDeviceModelList[] = [];
  selectedDevice: TicketDeviceModelList;
  subscription: Subscription = new Subscription();
  ticketStatusList = TicketStatusMapping;
  priorityList = TicketPriorityMapping;
  reopenedDirectionType = ReopenedDirectionType;
  productionLossList = ProductionLossList;
  resolution: string;
  customerEmails: string[] = [];
  truckRollList: Dropdown[] = [];
  customers: string[] = [];
  modalRef: BsModalRef;
  files: File[] = [];
  estFiles = [];
  estFile: File = null;
  minDate: Date = new Date();
  openMaxDate: Date = new Date();
  dateLoaded = false;
  user: string;
  userId: number;
  dateFormat = AppConstants.fullDateFormat;
  dateTimeFormat = AppConstants.dateTimeFormat;
  exclusionsLoading = false;
  actionLoading = false;
  commentLoading = false;
  activityLoading = true;
  exclusionLoading = false;
  deviceLoading = false;
  activeTab = 'Comments';
  isAuditDispatch = false;
  lossTypeList: Dropdown[] = [];
  isSendEmail = false;
  siteContact: CustomerContactList[] = [];
  customerContact = '';
  ticketDevice = '';
  fromDetailToEdit = false;
  isRmaUpload = false;
  siteKwac: string;
  userDateTimeFormat = AppConstants.dateTimeFormat;
  userMomentDateTimeFormat = AppConstants.momentDateTimeFormat;
  linkTypes: Dropdown[] = [];
  ticketList: Observable<Dropdown[]>;
  ticketLoading = false;
  ticketInput = new Subject<string>();
  showIssueBlock = true;
  customerId: number;
  jhaDetail: jhaDetail[] = [];
  previousCustomerId: number;
  fileUrl: string;
  jhaSelectedReport: any;
  deviceInfo = {};
  isMasterSel = false;
  alertsList: TicketAlerts[] = [];
  ticketData = {
    ticketNumber: null,
    isCreated: false
  };
  backNavigationURL: string;
  resolvedVal: boolean;
  orderForm: FormGroup;
  isEstimateEnable = false;
  isEstimateEnableIndex: number;
  isEstimateAdded = false;
  estTableData: TicketEstimates[] = [];
  statusDropDownList = statusDropDownList;
  @ViewChild('imageInput') imageInput;
  @ViewChild('videoInput') videoInput;
  attachmentsLoading = false;
  createFileUploadList = [];
  currentAttachmentTab = 'Files';
  fileAttachments: AttachmentListResponse = new AttachmentListResponse();
  imageAttachments: AttachmentListResponse = new AttachmentListResponse();
  videoAttachments: AttachmentListResponse = new AttachmentListResponse();
  filesPaginationParams: FileListPaginationParams = new FileListPaginationParams();
  imagesPaginationParams: FileListPaginationParams = new FileListPaginationParams();
  videoPaginationParams: FileListPaginationParams = new FileListPaginationParams();
  ticketBillingStatuses: TicketBillingStatusesResponse[] = [];
  ticketTypeList: Dropdown[] = [];
  costTypeList: TicketCostType[] = [];

  chunkUploadInProgress: boolean;
  remainingChunks$ = new BehaviorSubject<number>(0);
  private destroy$ = new Subject<void>();
  ticketNotesLoading = false;
  entityTypeName = NotesEntityName[NotesEntityType.CM_TICKETS];
  entityTypeId = NotesEntityType.CM_TICKETS;
  env = environment;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly portfolioService: PortfolioService,
    private readonly siteService: SiteService,
    private readonly route: ActivatedRoute,
    private readonly alertService: AlertService,
    private readonly router: Router,
    private readonly ticketService: TicketService,
    private readonly modalService: BsModalService,
    private readonly commonService: CommonService,
    private readonly _location: Location,
    private readonly storageService: StorageService,
    private readonly jhaService: JhaService,
    private readonly fb: FormBuilder,
    public readonly dropBoxService: DropboxImageGalleryService,
    public readonly exclusionsService: ExclusionsService,
    public readonly cdref: ChangeDetectorRef
  ) {}

  ngAfterContentChecked() {
    this.cdref.detectChanges();
  }

  ngOnInit(): void {
    this.orderForm = this.fb.group({
      items: this.fb.array([])
    });
    this.user = this.storageService.get('user').authorities;
    this.userId = Number(this.storageService.get('userID'));
    this.truckRollList = this.ticketService.getDDList(0, 10, '');
    // this.deviceInfo = this.storageService.get('deviceDetail');
    this.route.params.subscribe(params => {
      if (params?.ticketNumber && params?.mode) {
        this.ticketNumber = params.ticketNumber;
        if (params.mode === 'edit') {
          this.isEdit = true;
          this.isViewEdit = true;
        } else {
          this.isDetail = true;
        }
        this.getTicketDetailByTicketNumber(this.ticketNumber);
      } else {
        this.isCreate = true;
        const apiArray = [this.ticketService.getAllCustomer(), this.ticketService.getlossTypeList(), this.ticketService.getlinkTypeList()];
        const tempObj = ['customerList', 'lossTypeList', 'linkTypes'];
        this.loading = true;
        forkJoin(apiArray).subscribe({
          next: (res: any) => {
            for (const [index, value] of tempObj.entries()) {
              this[value] = res[index];
            }
            this.loading = false;
          },
          error: e => {
            this.loading = false;
          }
        });
        this.minDate = new Date(this.ticketDetailModel.open);
        this.minDate.setHours(0, 0, 0, 0); // Sets the time to 00:00:00.000
        this.dateLoaded = true;
      }
    });
    this.route.queryParams.subscribe(params => {
      this.backNavigationURL = params.back;
    });

    this.getTicketBillingStatuses();
    this.getTicketTypeList();
    this.getCostTypeList();

    this.commonService.commonUploadFinish$.subscribe(res => {
      setTimeout(() => {
        if (res && this.currentAttachmentTab === 'Files') {
          const requestParams = {
            fileType: 'document',
            page: 0,
            itemsCount: 10
          };
          this.getTicketAttachmentsList(requestParams, this.currentAttachmentTab);
        }
      }, 1000);
    });
  }
  get items() {
    return this.orderForm.controls['items'] as FormArray;
  }

  getAllExclusionTypeByCustomer(customerId: number, id?: number) {
    this.subscription.add(
      this.exclusionsService.getAllExclusionTypeByCustomer(customerId).subscribe({
        next: (res: Dropdown[]) => {
          this.exclusionTypeList = res;
          if (this.ticketDetailModel && this.ticketDetailModel.exclusionTypeId) {
            const exclusionType = this.exclusionTypeList?.find(type => type.id === this.ticketDetailModel.exclusionTypeId);
            if (exclusionType) {
              this.exclusionTypeData = exclusionType.name;
            }
          }
        },
        error: e => {
          this.exclusionTypeList = [];
        }
      })
    );
  }
  getALlListsForUpdate(forEdit = false) {
    this.dateLoaded = false;
    if (forEdit) {
      this.ticketModel = JSON.parse(JSON.stringify(this.ticketDetailModel));
      if (this.ticketModel.ticketDeviceMaps) {
        this.ticketModel.ticketDeviceMaps =
          this.ticketModel.ticketDeviceMaps && this.ticketModel.ticketDeviceMaps.length
            ? this.ticketModel.ticketDeviceMaps.map(item => {
                if (item.refTicketNumber !== this.ticketNumber) {
                  item.isADeviceOutage = false;
                  item.isPlannedDowntime = false;
                  item.comments = '';
                  item.overrideLosesKW = null;
                  item.endDateTimeUTCDate = null;
                  item.endDateTimeUTC = null;
                } else if (item.refTicketNumber) {
                  item.isADeviceOutage = true;
                }
                return {
                  ...item,
                  minStartDateTimeUTCDate: this.setMinStartDateTimeUTCDate(),
                  maxStartDateTimeUTCDate: this.setMaxStartDateTimeUTCDate(),
                  startDateTimeUTCDate: item.startDateTimeUTC ? this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '') : null,
                  startDateTimeUTC: item.startDateTimeUTC ? this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '').toString() : '',
                  minEndDateTimeUTCDate: this.setMinEndDateTimeUTCDate(item.startDateTimeUTC ?? ''),
                  maxEndDateTimeUTCDate: this.setMaxEndDateTimeUTCDate(),
                  endDateTimeUTCDate: item.endDateTimeUTC ? this.convertToLocalTime(item.endDateTimeUTC) : null
                };
              })
            : [];
      }
      if (this.ticketModel.close) {
        this.ticketModel.close = new Date(this.ticketModel.close);
      }
      this.dateLoaded = true;
    }
    this.loading = true;
    const apiArray = [
      this.ticketService.getAllCustomer(),
      this.ticketService.getAllFieldtechUsers(),
      this.portfolioService.getAllPortfoliosByCustomerId(true, this.ticketModel.customerId),
      this.siteService.getAllSitesByPortfolioId(true, this.ticketModel.portfolioId),
      this.ticketService.getDeviceTypeBySiteId(this.ticketModel.siteId),
      this.ticketService.getlossTypeList(),
      this.ticketService.getlinkTypeList()
    ];
    const tempObj = ['customerList', 'assigneeList', 'portfolioList', 'siteList', 'deviceTypeList', 'lossTypeList', 'linkTypes'];
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of tempObj.entries()) {
          this[value] = res[index];
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
    this.getAllExclusionTypeByCustomer(this.ticketModel.customerId);
  }

  hasReopenedFromTicket(): boolean {
    if (!this.ticketDetailModel?.reletedTicketsDetails?.length) return false;

    return this.ticketDetailModel.reletedTicketsDetails.some(reletedTicket =>
      reletedTicket.tickets?.some(ticket => ticket?.['reopenedDirectionType'] === this.reopenedDirectionType.ReopenedFrom)
    );
  }

  hasReopenedAsTicket(): boolean {
    if (!this.ticketDetailModel?.reletedTicketsDetails?.length) return false;

    return this.ticketDetailModel.reletedTicketsDetails.some(reletedTicket =>
      reletedTicket.tickets?.some(ticket => ticket?.['reopenedDirectionType'] === this.reopenedDirectionType.ReopenedAs)
    );
  }

  getJhaDetailsById(id: number) {
    this.loading = true;
    this.ticketService.getJhaDetailsById(id).subscribe({
      next: res => {
        this.jhaSelectedReport = res;
        this.loading = false;
      }
    });
  }

  setTicketActivityMaterials(ticketActivityMaterials: TicketMaterials[], costTypeID: number): TicketMaterials[] {
    return ticketActivityMaterials.filter(item => item.costTypeID === costTypeID);
  }

  getTicketDetailByTicketNumber(ticketNumber: string) {
    this.loading = true;
    this.dateLoaded = false;
    this.estFiles = [];
    this.subscription.add(
      this.ticketService.getTicketDetailByTicketNumber(ticketNumber).subscribe({
        next: (res: TicketAdd) => {
          if (res.status === 404) {
            this.router.navigate([APP_ROUTES.NO_DATA_FOUND]);
            this.loading = false;
          } else {
            if (Number(ticketNumber).toString() !== 'NaN') {
              return this.router.navigateByUrl(this.router.url.replace(ticketNumber, res.ticketNumber));
            }

            this.ticketDetailModel = res;
            this.getAllExclusionTypeByCustomer(this.ticketDetailModel.customerId, this.ticketDetailModel.exclusionTypeId);
            this.ticketId = res.id;

            this.showIssueBlock = true;
            if (this.ticketDetailModel.open) {
              this.minDate = new Date(this.ticketDetailModel.open);
              this.minDate.setHours(0, 0, 0, 0); // Sets the time to 00:00:00.000
            }
            if (this.ticketDetailModel.customerContact.length) {
              let str = '';
              for (let [i, v] of this.ticketDetailModel.customerContact.entries()) {
                str += i !== 0 ? `, ${v.customerEmail}` : v.customerEmail;
              }
              this.customerContact = str;
            }
            if (this.ticketDetailModel.ticketDeviceMaps.length) {
              let str = '';
              for (let [i, v] of this.ticketDetailModel.ticketDeviceMaps.entries()) {
                str += i !== 0 ? `, ${v.deviceTypeName}` : v.deviceTypeName;
              }
              this.ticketDevice = str;
            }
            if (this.ticketDetailModel.ticketExclusions.length) {
              this.ticketDetailModel.ticketExclusions.forEach(ex => {
                if (ex.from) {
                  this.ticketService.formateDate(ex.from);
                }
                if (ex.to) {
                  this.ticketService.formateDate(ex.to);
                }
              });
            }
            this.ticketModel = JSON.parse(JSON.stringify(this.ticketDetailModel));
            if (this.ticketModel.ticketDeviceMaps) {
              this.ticketModel.ticketDeviceMaps =
                this.ticketModel.ticketDeviceMaps && this.ticketModel.ticketDeviceMaps.length
                  ? this.ticketModel.ticketDeviceMaps.map(item => {
                      if (item.refTicketNumber !== this.ticketNumber) {
                        item.isADeviceOutage = false;
                        item.isPlannedDowntime = false;
                        item.comments = '';
                        item.overrideLosesKW = null;
                        item.endDateTimeUTCDate = null;
                        item.endDateTimeUTC = null;
                      } else if (item.refTicketNumber) {
                        item.isADeviceOutage = true;
                      }
                      return {
                        ...item,
                        minStartDateTimeUTCDate: this.setMinStartDateTimeUTCDate(),
                        maxStartDateTimeUTCDate: this.setMaxStartDateTimeUTCDate(),
                        startDateTimeUTCDate: item.startDateTimeUTC ? this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '') : null,
                        startDateTimeUTC: item.startDateTimeUTC ? this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '').toString() : '',
                        minEndDateTimeUTCDate: this.setMinEndDateTimeUTCDate(item.startDateTimeUTC ?? ''),
                        maxEndDateTimeUTCDate: this.setMaxEndDateTimeUTCDate(),
                        endDateTimeUTCDate: item.endDateTimeUTC ? this.convertToLocalTime(item.endDateTimeUTC) : null
                      };
                    })
                  : [];
            }
            if (this.ticketModel.open) {
              this.minDate = new Date(this.ticketModel.open);
              this.minDate.setHours(0, 0, 0, 0); // Sets the time to 00:00:00.000
            }
            if (this.ticketModel.close) {
              this.ticketModel.close = new Date(this.ticketModel.close);
            }
            setTimeout(() => {
              this.dateLoaded = true;
              this.changeTab(null);
              this.onAttachmentTabChange(this.currentAttachmentTab);
            }, 0);
            this.getTicketActivityLogsById();
            this.getJhaList();
            if (this.isEdit) {
              this.getALlListsForUpdate();
            } else {
              this.loading = false;
            }
            this.fb.array([]);
            this.items.setValue([]);
            this.estTableData = this.ticketModel.ticketEstimates;
            // TODO remove once completed testing
            // this.ticketModel.ticketEstimates.forEach(element => {
            //   const item = {
            //     ticketId: element.ticketId,
            //     ticketEstId: element.ticketEstId,
            //     estNumber: element.estNumber,
            //     additionalHours: element.additionalHours,
            //     estimateTotal: element.estimateTotal,
            //     ticketEstimateAttachments: element.ticketEstimateAttachments,
            //     isDeleted: false,
            //     randomGUID: element.randomGUID
            //   };
            //   this.createItem(item);
            //   this.items.push(this.createItem(item));
            // });
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  compareDates = (actualDate: string, minDate: string): boolean => {
    return new Date(actualDate) < new Date(minDate);
  };

  getAllCustomer() {
    this.loading = true;
    this.subscription.add(
      this.ticketService.getAllCustomer().subscribe((res: CustomerDropdown[]) => {
        this.customerList = res;
        this.loading = false;
      })
    );
  }

  getlossTypeList() {
    this.loading = true;
    this.subscription.add(
      this.ticketService.getlossTypeList().subscribe({
        next: (res: Dropdown[]) => {
          this.lossTypeList = res;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getCostTypeList() {
    this.loading = true;
    this.subscription.add(
      this.ticketService.getCostTypeList().subscribe({
        next: res => {
          this.costTypeList = res;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onCustomerSelect(event: CustomerDropdown) {
    const customersId = event.id;
    if (
      (this.previousCustomerId && this.previousCustomerId !== this.ticketModel.customerId) ||
      (this.ticketDetailModel.customerId && this.ticketDetailModel.customerId !== event.id)
    ) {
      this.onCustomerDeSelect();
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'After changing the customer, issue link will be deleted.Do you want to continue?',
          isWarning: true
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.previousCustomerId = customersId;
          this.ticketModel.reletedTickets = this.ticketModel.reletedTickets.filter(x => x.relatedTicketId);
          this.ticketModel.ticketDeviceMaps = [];
          this.ticketDetailModel.ticketDeviceMaps = [];
          this.getAllPortfolioByCustomer();
        } else {
          this.ticketModel.customerId = this.previousCustomerId;
          if (this.ticketDetailModel.customerId) {
            this.ticketModel.customerId = this.ticketDetailModel.customerId;
          }
          this.getAllPortfolioByCustomer();
        }
      });
    } else if (this.ticketModel.customerId) {
      this.ticketModel.customerName = event.name;
      this.getAllPortfolioByCustomer();
      this.previousCustomerId = customersId;
    }
    if (this.isEdit && this.ticketDetailModel.customerId === event.id) {
      this.showIssueBlock = true;
    } else if (this.isEdit) {
      this.showIssueBlock = false;
    }
    this.getAllExclusionTypeByCustomer(this.ticketModel.customerId);
  }

  onCustomerDeSelect() {
    this.ticketModel.customerName = null;
    this.ticketModel.portfolioId = null;
    this.portfolioList = [];
    this.onPortfolioDeSelect();
  }

  getAllPortfolioByCustomer() {
    this.loading = true;
    this.subscription.add(
      this.portfolioService.getAllPortfoliosByCustomerId(true, this.ticketModel.customerId).subscribe({
        next: (res: ContactDropdown[]) => {
          this.portfolioList = res;
          if (res.length) {
            this.ticketModel.portfolioId = res[0].id;
            this.onPortfolioSelect(res[0]);
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onPortfolioSelect(event) {
    this.onPortfolioDeSelect();
    if (this.ticketModel.portfolioId) {
      this.getAllSiteByPortfolio();
      this.ticketModel.portfolioName = event.name;
    }
  }

  onPortfolioDeSelect() {
    this.siteList = [];
    this.ticketModel.siteId = null;
    this.onSiteDeSelect();
  }

  getAllSiteByPortfolio() {
    this.loading = true;
    this.subscription.add(
      this.siteService.getAllSitesByPortfolioId(true, this.ticketModel.portfolioId).subscribe({
        next: (res: ContactDropdown[]) => {
          this.siteList = res;
          if (res.length) {
            this.ticketModel.siteId = res[0].id;
            this.onSiteSelect(res[0]);
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onSiteSelect(event) {
    this.onSiteDeSelect();
    if (this.ticketModel.siteId) {
      this.ticketModel.siteName = event.name;
      this.ticketModel.qeSiteId = event.qeSiteId;
      this.siteContact = event.contactEmails;
      this.siteKwac = event.affectedKwac;
      this.ticketModel.ticketDeviceMaps = [];
      this.ticketDetailModel.ticketDeviceMaps = [];
      this.getAllSiteDeviceById();
      this.setCustomerContact();
      this.setSiteDetails();
    }
  }

  onSiteDeSelect() {
    this.siteContact = [];
    this.ticketModel.siteName = null;
    this.ticketModel.qeSiteId = null;
    this.ticketModel.deviceTypeName = null;
    // this.ticketModel.siteDeviceName = null;
    // this.ticketModel.deviceTypeId = null;
    // this.ticketModel.siteDeviceId = null;
    // this.ticketModel.label = null;
    // this.ticketModel.manufacturer = null;
    this.deviceTypeList = [];
    this.deviceModelList = [];
    this.selectedDevice = new TicketDeviceModelList();
  }

  getAllSiteDeviceById() {
    this.loading = true;
    this.subscription.add(
      this.ticketService.getDeviceTypeBySiteId(this.ticketModel.siteId).subscribe({
        next: (res: TicketDeviceTypeList[]) => {
          this.deviceTypeList = res;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  setCustomerContact() {
    this.loading = true;
    this.subscription.add(
      this.ticketService.getCustomerContactBySiteId(this.ticketModel.siteId).subscribe({
        next: (res: CustomerContact[]) => {
          this.ticketModel.customerContact = res;
          let str = '';
          for (let [i, v] of this.ticketModel.customerContact.entries()) {
            str += i !== 0 ? `, ${v.customerEmail}` : v.customerEmail;
          }
          this.customerContact = str;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }
  setSiteDetails() {
    this.loading = true;
    this.subscription.add(
      this.ticketService.getDeviceListForTicketActivity(this.ticketModel.siteId, this.ticketNumber || null).subscribe({
        next: res => {
          this.ticketModel.isGADSReporting = res['isGADSReporting'];
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }
  getDeviceModelById(id: number) {
    const tempObj = this.deviceTypeList.find(item => item.id === id);
    if (tempObj && tempObj.siteDevices) {
      this.deviceModelList = tempObj.siteDevices;
    }
  }

  saveTicket() {
    this.loading = true;
    // this.ticketModel.affectedkWac = this.deviceInfo['affectedKwac'];
    this.ticketModel.estNumber = this.ticketModel.estNumber ? Number(this.ticketModel.estNumber) : null;
    this.ticketModel.additionalHours = this.ticketModel.additionalHours ? Number(this.ticketModel.additionalHours) : null;
    this.ticketModel.estKWhLoss = this.ticketModel.estKWhLoss ? Number(this.ticketModel.estKWhLoss) : null;
    const date = new Date();
    this.ticketModel.close = this.ticketModel.close ? this.ticketService.formateDate(this.ticketModel.close) : null;
    const contact = this.customerContact?.split(',');
    this.ticketModel.customerContact = [];
    for (let i of contact) {
      if (i) {
        i = i.trim();
        const email = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$';
        if (i.match(email)) {
          const index = this.ticketModel.customerContact.findIndex(j => j.customerEmail === i);
          if (index === -1) {
            const customer: CustomerContact = new CustomerContact();
            customer.customerEmail = i;
            customer.ticketId = this.ticketId ? Number(this.ticketId) : 0;
            this.ticketModel.customerContact.push(customer);
          }
        } else {
          this.alertService.showErrorToast('Invalid email in customer contact.');
          this.loading = false;
          return;
        }
      }
    }
    this.ticketModel.ticketEstimates = this.isCreate ? this.estTableData : [];
    // for new ticket making the default entry for billing status :4185
    if (!this.ticketModel.ticketBillingStatusID) {
      this.ticketModel.ticketBillingStatusID = null;
      this.ticketModel.ticketBillingStatusDescription = null;
    }
    if (this.ticketModel.ticketDeviceMaps.length > 0) {
      this.ticketModel.ticketDeviceMaps.forEach((element, index) => {
        if (element.startDateTimeUTCDate) {
          element.startDateTimeUTC = this.getFormatedDateTime(element.startDateTimeUTCDate.toString());
        }
        if (element.endDateTimeUTCDate) {
          element.endDateTimeUTC = this.getFormatedDateTime(element.endDateTimeUTCDate.toString());
        }
      });
      if (this.isCreate) {
        this.ticketModel.open = this.ticketModel.open ? this.ticketService.formateDate(this.ticketModel.open) : null;
        this.subscription.add(
          this.ticketService.createTicket(this.ticketModel).subscribe({
            next: (res: any) => {
              this.alertsList = res.listOutage;
              this.ticketId = res.entryid;
              this.ticketNumber = res.ticketNumber;
              if (res.ticketEstimates) {
                res.ticketEstimates.forEach(element => {
                  let fileIndex = this.estTableData.findIndex(x => x.randomGUID === element.randomGUID);
                  if (this.estTableData[fileIndex].ticketEstimateAttachments.length > 0) {
                    if (fileIndex !== -1 && this.estTableData[fileIndex].ticketEstimateAttachments[0].id === 0) {
                      this.estTableData[fileIndex].ticketEstimateAttachments[0].ticketEstId = element.ticketEstId;
                      this.estTableData[fileIndex].ticketEstimateAttachments[0].ticketId = element.ticketId;
                      this.estFiles.push(this.estTableData[fileIndex].ticketEstimateAttachments[0]);
                    }
                  }
                });
              }
              if (this.createFileUploadList.length || this.estFile || this.isRmaUpload || this.estFiles.length) {
                this.uploadFiles(this.ticketId, true);
              } else {
                this.checkToSendEmail(res.ticketNumber);
              }
            },
            error: e => {
              this.ticketModel.open = new Date(this.ticketModel.open);
              this.ticketModel.close = new Date(this.ticketModel.close);
              this.loading = false;
            }
          })
        );
      } else {
        // this.ticketModel.deviceTypeId = this.ticketModel.deviceTypeId === 0 ? null : this.ticketModel.deviceTypeId;
        this.subscription.add(
          this.ticketService.updateTicket(this.ticketModel).subscribe({
            next: (res: any) => {
              this.alertsList = res.listOutage;
              if (res.ticketEstimates) {
                res.ticketEstimates.forEach(element => {
                  let fileIndex = this.estTableData.findIndex(x => x.randomGUID === element.randomGUID);
                  if (this.estTableData[fileIndex].ticketEstimateAttachments.length > 0) {
                    if (fileIndex !== -1 && this.estTableData[fileIndex].ticketEstimateAttachments[0].id === 0) {
                      this.estTableData[fileIndex].ticketEstimateAttachments[0].ticketEstId = element.ticketEstId;
                      this.estTableData[fileIndex].ticketEstimateAttachments[0].ticketId = element.ticketId;
                      this.estFiles.push(this.estTableData[fileIndex].ticketEstimateAttachments[0]);
                    }
                  }
                });
              }
              if (this.createFileUploadList.length || this.estFile || this.isRmaUpload || this.estFiles.length) {
                this.uploadFiles(this.ticketModel.id, false);
              } else {
                this.checkToSendEmail(this.ticketModel.ticketNumber, true);
              }
            },
            error: e => {
              this.ticketModel.open = new Date(this.ticketModel.open);
              this.ticketModel.close = new Date(this.ticketModel.close);
              this.loading = false;
            }
          })
        );
      }
    } else {
      this.alertService.showErrorToast('Please Select At Least One Device.');
      this.loading = false;
      return;
    }
  }

  getFormatedDateTime(requestedDateTime: string): string {
    const dateTimeformat = 'MM/DD/YYYY hh:mm A';
    if (requestedDateTime) return moment(new Date(requestedDateTime)).format(dateTimeformat);
    return null;
  }

  checkToSendEmail(ticketNumber, fromUpdate = false) {
    if (this.isSendEmail) {
      this.ticketData = {
        ticketNumber: ticketNumber,
        isCreated: !fromUpdate
      };

      this.subscription.add(
        this.ticketService.sendTicketEmail(this.ticketData).subscribe({
          next: () => {
            if (this.alertsList?.length) {
              this.loading = false;

              const ngModalOptions: ModalOptions = {
                backdrop: 'static',
                keyboard: false,
                animated: true,
                class: 'modal-full'
              };
              this.modalRef = this.modalService.show(this.addAlertTemplate, ngModalOptions);
            } else {
              this.ticketAction(ticketNumber, fromUpdate);
            }
          },
          error: e => {
            this.ticketAction(ticketNumber, fromUpdate);
            this.alertService.showErrorToast(`Ticket ${fromUpdate ? 'updated' : 'created'} but fail to send an email.`);
            this.loading = false;
          }
        })
      );
    } else {
      this.ticketData = {
        ticketNumber: ticketNumber,
        isCreated: !fromUpdate
      };
      if (this.alertsList?.length) {
        this.loading = false;

        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          class: 'modal-full'
        };
        this.modalRef = this.modalService.show(this.addAlertTemplate, ngModalOptions);
      } else {
        this.ticketAction(ticketNumber, fromUpdate);
      }
    }
  }

  ticketAction(ticketNumber, fromUpdate) {
    if (fromUpdate) {
      this.ticketUpdated();
    } else {
      this.ticketCreated(ticketNumber);
    }
  }

  ticketCreated(ticketNumber: string) {
    this.storageService.set(AppConstants.isPerviousPageTicketCreate, 'true');
    this.alertService.showSuccessToast('Ticket created.');
    this.router.navigateByUrl(APP_ROUTES.TICKET + APP_ROUTES.DETAIL + APP_ROUTES.VIEW + '/' + ticketNumber);
    this.loading = false;
    this.showIssueBlock = true;
    this.alertsList = [];
    this.isMasterSel = false;
  }

  ticketUpdated() {
    this.alertService.showSuccessToast('Ticket updated.');
    this.isDetail = true;
    this.isEdit = false;
    this.fromDetailToEdit = false;
    this.ticketDetailModel = JSON.parse(JSON.stringify(this.ticketModel));
    this.loading = false;
    this.showIssueBlock = true;
    this.alertsList = [];
    this.isMasterSel = false;
    this.getTicketDetailByTicketNumber(this.ticketNumber);
  }

  getESTUpload(files, items, index) {
    if (files[0].size <= 10485760) {
      this.estFile = files[0];
    } else {
      this.alertService.showErrorToast('File size should not be allowed more than 10MB');
    }
    if (this.ticketModel.id) {
      this.loading = true;
      const formData: FormData = new FormData();
      formData.append('file', this.estFile as File);
      formData.append(
        'fileType',
        this.estFile.type.split('/')[0] && this.estFile.type.split('/')[0] === 'application' ? 'document' : this.estFile.type.split('/')[0]
      );
      formData.append('fileExtension', this.estFile.type.split('/')[1]);
      formData.append('id', '0');
      formData.append('ticketId', `${this.ticketModel.id}`);
      formData.append('documentUrl', '');
      formData.append('isDeleted', 'false');
      formData.append('ticketEstId', items.ticketEstId);
      formData.append('randomGUID', items.randomGUID);
      this.ticketService.uploadESTFiles(this.ticketModel.id, formData).subscribe({
        next: res => {
          this.ticketDetailModel.ticketEstimateAttachments.push(res);
          this.ticketModel.ticketEstimateAttachments.push(res);

          this.alertService.showSuccessToast(`File uploaded.`);
          this.estFile = null;
          this.loading = false;
        },
        error: e => {
          this.alertService.showWarningToast('Fail to upload file.');
          this.loading = false;
        }
      });
    }
  }

  uploadFiles(id: number, fromCreate = false) {
    this.loading = true;
    const tempArray: any = [];
    if (this.createFileUploadList.length) {
      for (const fileObj of this.createFileUploadList) {
        const formData: FormData = new FormData();
        formData.append('files', fileObj.file as File);
        if (fileObj.fileTag.length) {
          for (const tag of fileObj.fileTag) {
            formData.append('fileTagIds', `${tag}`);
          }
        }
        formData.append('customerId', `${this.ticketModel.customerId}`);
        formData.append('id', '0');
        formData.append('portfolioId', `${this.ticketModel.portfolioId}`);
        formData.append('siteId', `${this.ticketModel.siteId}`);
        formData.append('entityId', `${this.ticketId}`);
        formData.append('entityNumber', `${this.ticketNumber}`);
        formData.append('moduleType', '3');
        formData.append('fileType', `${fileObj.fileType}`);
        if (fileObj.notes) {
          formData.append('notes', `${fileObj.notes}`);
        }
        tempArray.push(this.dropBoxService.uploadFilesToGallery(formData));
      }
    }
    if (this.estFile) {
      const formData: FormData = new FormData();
      formData.append('file', this.estFile as File);
      formData.append('fileType', this.estFile.type.split('/')[0]);
      formData.append('fileExtension', this.estFile.type.split('/')[1]);
      formData.append('id', '0');
      formData.append('ticketId', `${id}`);
      formData.append('documentUrl', '');
      formData.append('isDeleted', 'false');
      tempArray.push(this.ticketService.uploadESTFiles(id, formData));
    }
    if (this.estFiles) {
      this.estFiles.forEach(element => {
        const formData: FormData = new FormData();
        formData.append('file', element.file as File);
        formData.append('fileType', element.fileExtension);
        formData.append('fileExtension', element.fileType);
        formData.append('id', '0');
        formData.append('ticketId', element.ticketId);
        formData.append('documentUrl', '');
        formData.append('isDeleted', 'false');
        formData.append('ticketEstId', element.ticketEstId);

        tempArray.push(this.ticketService.uploadESTFiles(id, formData));
      });
    }
    if (this.isRmaUpload) {
      let attachmentList = [];
      this.ticketModel.ticketRMAs.forEach(element => {
        if (!element.isDeleted) {
          const deviceAttachments = element.deviceAttachments;
          const returnAttachments = element.returnAttachments;
          const attachments = [...deviceAttachments, ...returnAttachments];
          attachmentList.push(...attachments);
        }
      });
      if (attachmentList.length > 0) {
        attachmentList.forEach(obj => {
          obj.ticketId = obj.ticketId ? obj.ticketId : id;

          if (obj.isDeleted === true || obj.attachFile) {
            const formData = new FormData();
            formData.append('attachFile', obj.attachFile ? obj.attachFile : '');
            formData.append('ticketId', obj.ticketId);
            formData.append('rmaDocId', obj.rmaDocId);
            formData.append('rmaId', obj.rmaId);
            formData.append('siteDeviceId', obj.siteDeviceId);
            formData.append('documentUrl', obj.documentUrl);
            formData.append('fileName', obj.fileName);
            formData.append('fileType', obj.fileType);
            formData.append('fileExtension', obj.fileExtension);
            formData.append('isDeleted', obj.isDeleted);
            formData.append('docType', obj.docType);
            tempArray.push(this.ticketService.uploadRmaAttachment(formData));
          }
        });
      }
    }
    forkJoin(tempArray).subscribe({
      next: (res: any) => {
        this.files = [];
        this.createFileUploadList = [];
        this.estFile = null;
        if (fromCreate) {
          this.checkToSendEmail(this.ticketNumber);
        } else {
          this.checkToSendEmail(this.ticketModel.ticketNumber, true);
          for (const i of res) {
            this.ticketDetailModel.ticketAttachments.push(i);
          }
          if (!this.isRmaUpload) {
            this.alertService.showSuccessToast(`File${this.createFileUploadList.length > 1 ? 's' : ''} uploaded.`);
          }
          this.isRmaUpload = false;
          this.loading = false;
        }
      },
      error: e => {
        if (fromCreate) {
          this.alertService.showWarningToast('Ticket created but fail to upload files.');
          this.router.navigateByUrl(`/entities/ticket/detail/edit/${this.ticketNumber}`);
        }
        this.alertService.showWarningToast('Fail to upload files.');
        this.loading = false;
      }
    });
  }

  deleteJhaReport(id: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          this.loading = true;
          const modal = {
            reportId: id,
            ticketWoId: this.ticketId,
            isTicket: true
          };
          this.subscription.add(
            this.ticketService.deleteJhaReport(modal).subscribe({
              next: (res: MessageVM) => {
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  this.getJhaList();
                  this.loading = false;
                } else {
                  this.loading = false;
                }
              }
            })
          );
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  downloadJhaReport(id, name) {
    this.loading = true;
    this.jhaService.generateJhaPdfReport(id).subscribe({
      next: data => {
        this.jhaService.downloadJhaPdfReport(id).subscribe({
          next: data => {
            if (data) {
              const link = this.commonService.createObject(data, 'application/pdf');
              const finalReportName = name;
              link.download = finalReportName + '.pdf';
              link.click();
              this.loading = false;
            } else {
              this.loading = false;
            }
          }
        });
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  deleteESTImage(id: number, index) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          this.loading = true;
          this.subscription.add(
            this.ticketService.deleteESTFiles(id).subscribe({
              next: res => {
                this.alertService.showSuccessToast('File deleted.');
                this.loading = false;
                this.ticketDetailModel.ticketEstimateAttachments = [];
                this.ticketModel.ticketEstimateAttachments = [];
                this.items.controls[index]['controls']['ticketEstimateAttachments'].setValue = [];
                this.items.controls[index]['controls']['ticketEstimateAttachments'].value = [];
                this.items.value[index].ticketEstimateAttachments = [];
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  getValue(id: number, list: Dropdown[], val = 'name') {
    const obj = list.filter(item => item.id === id);
    if (obj.length) {
      return obj[0][val];
    }
  }

  getFullTime(minutes: number) {
    if (minutes) {
      let hours = 0,
        str = '';
      if (minutes > 60) {
        hours = minutes / 60;
        hours = parseInt(hours.toString().split('.')[0]);
        minutes = minutes - hours * 60;
      }
      str += hours > 0 ? ` ${hours} hour${hours > 1 ? 's' : ''}` : '';
      if (str === '' && minutes === 0) {
        return '-';
      }
      return str;
    }
    return '-';
  }

  onAddComment(commentItem, isEdit) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        commentItem: commentItem ? JSON.parse(JSON.stringify(commentItem)) : new TicketComment(),
        ticketId: this.ticketId,
        ticketNumber: this.ticketDetailModel.ticketNumber,
        isEdit: isEdit
      }
    };
    this.modalRef = this.modalService.show(CommentComponent, ngModalOptions);
    this.modalRef.content.event.subscribe(res => {
      if (res) {
        this.loading = false;
        this.getTicketDetailByTicketNumber(this.ticketModel.ticketNumber);
      }
    });
  }

  addActivityLog(activityLogItem, isBool) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        activityLogItem: activityLogItem ? JSON.parse(JSON.stringify(activityLogItem)) : new TicketActivityLogs(),
        isEdit: isBool,
        ticketId: this.ticketId,
        jhaList: this.jhaDetail,
        siteId: this.ticketModel.siteId,
        ticketActivityLog: this.ticketActivityLogs,
        ticketModel: this.ticketModel,
        openTicket: this.ticketService.formateDate(this.ticketModel.open),
        closeTicket: this.ticketService.formateDate(this.ticketModel.close),
        ticketNumber: this.ticketNumber,
        ticketStatus: this.ticketModel.status
        // activityId
      }
    };
    this.modalRef = this.modalService.show(ActivityLogComponent, ngModalOptions);
    this.modalRef.content.event.subscribe(res => {
      if (res) {
        this.loading = false;
        this.getTicketDetailByTicketNumber(this.ticketModel.ticketNumber);
      }
    });
  }

  prductionLossChange(event) {
    if (event && (event.id === 1 || event.id === 2)) {
      this.ticketModel.affectedkWac = null;
    } else if (event && event.id === 0) {
      // const meterObject = this.deviceInfo.find(obj => obj.deviceTypeName === 'Meters');
      // const inverterObject = this.deviceInfo.find(obj => obj.deviceTypeName === 'Inverters');
      // if (meterObject && inverterObject) {
      //   this.ticketModel.affectedkWac = meterObject.size;
      // } else {
      //   if (meterObject) {
      //     this.ticketModel.affectedkWac = meterObject.size;
      //   } else if (inverterObject) {
      //     this.ticketModel.affectedkWac = inverterObject.size;
      //   } else {
      //     this.ticketModel.affectedkWac = null;
      //   }
      // }
    }
  }

  deleteTicket() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          this.loading = true;
          this.subscription.add(
            this.ticketService.deleteTicket(this.ticketNumber).subscribe({
              next: res => {
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  setTimeout(() => {
                    if (this.storageService.get(AppConstants.isPerviousPageTicketCreate)) {
                      this.router.navigateByUrl(APP_ROUTES.TICKET);
                      this.storageService.clear(AppConstants.isPerviousPageTicketCreate);
                    } else {
                      if (window.history.length > 1) {
                        this._location.back();
                      } else {
                        this.router.navigateByUrl(APP_ROUTES.TICKET);
                      }
                      this.loading = true;
                    }
                  }, 1000);
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  openDateChanged() {
    if (this.ticketModel.open) {
      this.minDate = new Date(this.ticketModel.open);
      this.minDate.setHours(0, 0, 0, 0); // Sets the time to 00:00:00.000
      this.setInitialValueForDeviceOutage();
    }
  }

  closedDateChanged(): void {
    if (this.ticketModel.close) {
      this.setInitialValueForDeviceOutage();
    }
  }

  setInitialValueForDeviceOutage(): void {
    this.ticketModel.ticketDeviceMaps =
      this.ticketModel.ticketDeviceMaps && this.ticketModel.ticketDeviceMaps.length > 0
        ? this.ticketModel.ticketDeviceMaps
            .map(item => ({
              ...item,
              minStartDateTimeUTCDate: this.setMinStartDateTimeUTCDate(),
              maxStartDateTimeUTCDate: this.setMaxStartDateTimeUTCDate(),
              startDateTimeUTCDate: item.startDateTimeUTCDate
                ? item.startDateTimeUTCDate
                : this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? ''),
              startDateTimeUTC: (item.startDateTimeUTCDate
                ? item.startDateTimeUTCDate
                : this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '')
              ).toString()
              // might be used
              // endDateTimeUTCDate: item.endDateTimeUTC ? this.convertToLocalTime(item.endDateTimeUTC) : null
            }))
            .map(item => ({
              ...item,
              minEndDateTimeUTCDate: this.setMinEndDateTimeUTCDate(item.startDateTimeUTCDate, true),
              maxEndDateTimeUTCDate: this.setMaxEndDateTimeUTCDate()
            }))
        : [];
  }

  statusChange(event) {
    this.ticketModel.ticketBillingStatusID = null;
    if (event && event.id === 1) {
      const isInSpecifiedStatuses = this.estTableData.some(obj => {
        const status = obj.estimateStatus;
        return status === 5;
      });
      if (isInSpecifiedStatuses) {
        setTimeout(() => {
          this.ticketModel.status = this.ticketDetailModel.status;
        }, 0);
        this.alertService.showErrorToast(
          'You cannot change the ticket status to open because one or more ticket estimates are in the billing status.'
        );
      } else {
        this.ticketModel.close = null;
      }
    } else if (event && event.id === 2) {
      this.ticketModel.close = null;
    } else if (event && event.id === 3) {
      this.ticketModel.ticketBillingStatusID = 1;
      const isAllInSpecifiedStatuses = this.estTableData.some(obj => {
        const status = obj.estimateStatus;
        if (status === 1 || status === 3) {
          //  Pending Approval, Approved,
          return true;
        }
      });
      if (isAllInSpecifiedStatuses) {
        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          initialState: {
            message:
              'One or more estimate is not ready for closing ticket. Customer needs to Approve and enter PO# or Decline all estimates.',
            isWarning: true,
            confirmBtnText: 'Save Anyway',
            cancelBtnText: 'Cancel'
          }
        };
        this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
        this.modalRef.content.onClose.subscribe(result => {
          if (!result) {
            this.ticketModel.status = this.ticketDetailModel.status;
          }
        });
      }
    }

    if (event && event.id !== 3) {
      this.setInitialValueForDeviceOutage();
    }
  }

  deleteDevice(id: number): void {
    const totalAddedDevice = this.ticketModel.ticketDeviceMaps.length;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          const objWithIndex = this.ticketModel.ticketDeviceMaps.findIndex(obj => obj.siteDeviceId === id);
          const deleteDeviceObj = this.ticketModel.ticketDeviceMaps[objWithIndex];
          this.ticketModel.ticketDeviceMaps.splice(objWithIndex, 1);
          if (totalAddedDevice === 1) {
            this.onAddDevice(this.ticketModel);
          }
          let meterArray = [];
          meterArray = this.ticketModel.ticketDeviceMaps.filter(obj => obj.deviceTypeId === 2);
          let inverterArray = [];
          inverterArray = this.ticketModel.ticketDeviceMaps.filter(obj => obj.deviceTypeId === 1);
          if (meterArray.length !== 0 && inverterArray.length !== 0) {
            let sum = 0;
            for (const obj of meterArray) {
              this.ticketModel.affectedkWac = obj.size + sum;
              sum = this.ticketModel.affectedkWac;
            }
          } else {
            if (meterArray.length !== 0) {
              let sum = 0;
              for (const obj of meterArray) {
                this.ticketModel.affectedkWac = obj.size + sum;
                sum = this.ticketModel.affectedkWac;
              }
            } else if (inverterArray.length !== 0) {
              let sum = 0;
              for (const obj of inverterArray) {
                this.ticketModel.affectedkWac = obj.size + sum;
                sum = this.ticketModel.affectedkWac;
              }
            } else {
              this.ticketModel.affectedkWac = null;
            }
          }
          this.deviceInfo['ticketDeviceMaps'] = this.ticketModel.ticketDeviceMaps;
          this.deviceInfo['affectedkWac'] = this.ticketModel.affectedkWac;
          const deleteDeviceSiteId = deleteDeviceObj.siteDeviceId;
          if (this.ticketModel.ticketRMAs.length > 0) {
            const findRmaIndex = this.ticketModel.ticketRMAs.findIndex(obj => obj.siteDeviceId === deleteDeviceSiteId);
            this.ticketModel.ticketRMAs[findRmaIndex].isDeleted = true;
          }
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  deleteActivityLog(id) {
    if (id) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(
        result => {
          if (result) {
            this.subscription.add(
              this.ticketService.deleteActivitylog(id).subscribe({
                next: res => {
                  if (res) {
                    this.alertService.showSuccessToast(res.message);
                    this.getTicketActivityLogsById();
                    if (this.activeTab === 'History') {
                      this.getTicketActionsById();
                    }
                  }
                },
                error: e => {
                  this.loading = false;
                }
              })
            );
          }
        },
        err => {
          this.loading = false;
        }
      );
    }
  }

  deleteComment(id) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          this.subscription.add(
            this.ticketService.deleteComment(id).subscribe({
              next: res => {
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  this.getTicketDetailByTicketNumber(this.ticketNumber);
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  addExclusion(exclusionItem) {
    const exclusionData = exclusionItem ? JSON.parse(JSON.stringify(exclusionItem)) : new TicketExclusions();
    if (exclusionData.from) {
      exclusionData.from = new Date(exclusionData.from);
    }
    if (exclusionData.to) {
      exclusionData.to = new Date(exclusionData.to);
    }
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        exclusionItem: exclusionData,
        ticketId: this.ticketId,
        ticketNumber: this.ticketDetailModel.ticketNumber
      }
    };
    this.modalRef = this.modalService.show(ExclusionModelComponent, ngModalOptions);
    this.modalRef.content.event.subscribe(res => {
      if (res) {
        this.loading = false;
        this.getAllExclusions();
      }
    });
  }

  onAddDevice(deviceItemArray) {
    const deviceData = deviceItemArray.ticketDeviceMaps
      ? JSON.parse(JSON.stringify(deviceItemArray.ticketDeviceMaps))
      : new TicketDeviceMaps();
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        deviceItem: deviceData,
        ticketId: this.ticketId,
        ticketNumber: this.ticketDetailModel.ticketNumber,
        siteId: this.ticketModel.siteId,
        ticketdetail: this.ticketModel,
        currentTicketNumber: this.ticketNumber
      }
    };
    this.modalRef = this.modalService.show(DeviceModelComponent, ngModalOptions);
    this.modalRef.content.event.subscribe(res => {
      if (res) {
        this.loading = false;
        this.deviceInfo = res;
        this.ticketModel = res;
        this.ticketModel.ticketDeviceMaps =
          this.ticketModel.ticketDeviceMaps && this.ticketModel.ticketDeviceMaps.length
            ? this.ticketModel.ticketDeviceMaps.map(item => {
                if (item.refTicketNumber !== this.ticketNumber) {
                  item.isADeviceOutage = false;
                  item.isPlannedDowntime = false;
                  item.comments = '';
                  item.overrideLosesKW = null;
                  item.endDateTimeUTCDate = null;
                  item.endDateTimeUTC = null;
                } else if (item.refTicketNumber) {
                  item.isADeviceOutage = true;
                }
                return {
                  ...item,
                  minStartDateTimeUTCDate: this.setMinStartDateTimeUTCDate(),
                  maxStartDateTimeUTCDate: this.setMaxStartDateTimeUTCDate(),
                  startDateTimeUTCDate: item.startDateTimeUTC ? this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '') : null,
                  startDateTimeUTC: item.startDateTimeUTC ? this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '').toString() : '',
                  minEndDateTimeUTCDate: this.setMinEndDateTimeUTCDate(item.startDateTimeUTCDate ?? ''),
                  maxEndDateTimeUTCDate: this.setMaxEndDateTimeUTCDate(),
                  endDateTimeUTCDate: item.endDateTimeUTC ? this.convertToLocalTime(item.endDateTimeUTC) : null
                };
              })
            : [];
        // this.getTicketDetailById(this.ticketDetailModel.ticketNumber);
      }
    });
  }

  hasDeviceComments(ticketDeviceMaps): boolean {
    if (ticketDeviceMaps?.length) {
      return ticketDeviceMaps?.some(device => device.comments);
    }
    // return false;
  }

  onDeviceChange(event: boolean, item: TicketDeviceMaps): void {
    if (!event && !item.isADeviceOutage) {
      item.isPlannedDowntime = false;
    }
    if (item?.startDateTimeUTC && item?.refTicketNumber && item?.refTicketNumber !== this.ticketNumber) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: `Device Outage : <a href="${this.env.baseUrl}/entities/ticket/detail/view/${item.refTicketNumber}" target="_blank">${item.refTicketNumber}</a>`,
          showConfirmButton: false,
          cancelBtnText: 'Okay'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(
        result => {
          item.isADeviceOutage = false;
        },
        err => {
          this.loading = false;
        }
      );
    } else {
      item.minStartDateTimeUTCDate = this.setMinStartDateTimeUTCDate();
      item.maxStartDateTimeUTCDate = this.setMaxStartDateTimeUTCDate();
      item.startDateTimeUTCDate = !event
        ? item.startDateTimeUTC
          ? this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '')
          : null
        : this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '');
      item.minEndDateTimeUTCDate = this.setMinEndDateTimeUTCDate(item.startDateTimeUTC ?? '');
      item.maxEndDateTimeUTCDate = this.setMaxEndDateTimeUTCDate();
      item.endDateTimeUTCDate = item.endDateTimeUTC ? this.convertToLocalTime(item.endDateTimeUTC) : null;
      Object.assign(item, item);
    }
  }

  convertToLocalTime(utcDateString: string | Date = ''): Date {
    return utcDateString !== '' ? new Date(utcDateString) : new Date();
  }

  setStartDateTimeUTCDate(utcDateString: string | Date = ''): Date {
    const utcDate = this.convertToLocalTime(utcDateString);
    const openDate = this.convertToLocalTime(this.ticketModel?.open);
    if (utcDateString) {
      openDate.setHours(utcDate.getHours(), utcDate.getMinutes(), utcDate.getSeconds(), utcDate.getMilliseconds());
    } else {
      openDate.setHours(0, 0, 0, 0);
    }
    const startDate = utcDate < this.convertToLocalTime(this.ticketModel?.open) ? this.convertToLocalTime(this.ticketModel?.open) : utcDate;
    return this.ticketModel.status === 3 ? openDate : startDate;
  }

  onActivityStartDateChange(item: TicketSiteDeviceOutage): void {
    item.minEndDateTimeUTCDate = this.setMinEndDateTimeUTCDate(item.startDateTimeUTCDate, true);
    item.maxEndDateTimeUTCDate = this.setMaxEndDateTimeUTCDate();
    item.endDateTimeUTCDate = null;
  }

  setMinStartDateTimeUTCDate(): Date {
    const openDate = this.convertToLocalTime(this.ticketModel.open);
    openDate.setHours(0, 0, 0, 0);
    return this.ticketModel.status === 3 ? openDate : this.minDate;
  }

  setMaxStartDateTimeUTCDate(): Date {
    const closeDate = this.convertToLocalTime(this.ticketModel.close);
    closeDate.setHours(23, 58, 59);
    return this.ticketModel.status === 3 ? closeDate : null;
  }

  setMinEndDateTimeUTCDate(startDateTimeUTCDate: string | Date = '', fromStartDate = false): Date {
    const minStartDate = !fromStartDate
      ? this.ticketModel.status === 3
        ? this.ticketModel.open
        : startDateTimeUTCDate
      : startDateTimeUTCDate;
    const minDate = this.convertToLocalTime(minStartDate);
    minDate.setMinutes(minDate.getMinutes() + 1);
    return minDate;
  }

  setMaxEndDateTimeUTCDate(): Date {
    const closeDate = this.convertToLocalTime(this.ticketModel.close);
    closeDate.setHours(23, 59, 59);
    return this.ticketModel.status === 3 ? closeDate : null;
  }

  nbMinEndDateTimeUTCDate(minEndDateTimeUTCDate: string | Date = ''): Date {
    const minDate = this.convertToLocalTime(minEndDateTimeUTCDate);
    minDate.setDate(minDate.getDate() - 1);
    return minDate;
  }

  nbMaxEndDateTimeUTCDate(maxEndDateTimeUTCDate: string | Date = ''): Date {
    const closeDate = this.convertToLocalTime(maxEndDateTimeUTCDate);
    closeDate.setHours(23, 59, 59);
    return this.ticketModel.status === 3 ? closeDate : null;
  }

  openRmaModel(mode, rmaIndex = -1, isFromDetail = false) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        ticketId: Number(this.ticketId) || 0,
        ticketDetail: this.ticketModel,
        mode: mode,
        rmaIndex: rmaIndex,
        isFromDetailView: isFromDetail,
        rmaDetails: this.ticketModel.ticketRMAs
      }
    };
    this.modalRef = this.modalService.show(AddEditRmaSideModelComponent, ngModalOptions);
    this.modalRef.content.event.subscribe(res => {
      if (res) {
        this.ticketDetailModel = res.ticketDetail;
        this.ticketModel = res.ticketDetail;
        this.isRmaUpload = res.isRmaUpload;
        this.loading = false;
      }
    });
  }

  deleteRmaDetails(removeRMAIndex: number) {
    if (removeRMAIndex >= 0) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(
        result => {
          if (result) {
            this.loading = true;
            this.ticketModel.ticketRMAs[removeRMAIndex].isDeleted = true;
            this.loading = false;
          }
        },
        err => {
          this.loading = false;
        }
      );
    }
  }

  deleteExclusion(ticketexclusionId) {
    if (ticketexclusionId) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(
        result => {
          if (result) {
            this.subscription.add(
              this.ticketService.deleteExclusion(ticketexclusionId).subscribe({
                next: res => {
                  if (res) {
                    this.alertService.showSuccessToast(res.message);
                    this.getTicketDetailByTicketNumber(this.ticketModel.ticketNumber);
                  }
                },
                error: e => {
                  this.loading = false;
                }
              })
            );
          }
        },
        err => {
          this.loading = false;
        }
      );
    }
  }

  changeTab(event) {
    this.activeTab = event && event.tabTitle ? event.tabTitle : this.activeTab;
    if (this.ticketDetailModel.id) {
      if (this.activeTab === 'Comments') {
        this.getTicketCommentsById();
      } else if (this.activeTab === 'Exclusions') {
      } else if (this.activeTab === 'History') {
        this.getTicketActionsById();
      }
    }
  }

  getTicketCommentsById() {
    this.commentLoading = true;
    this.subscription.add(
      this.ticketService.getTicketCommentsByTicketNumber(this.ticketDetailModel.ticketNumber).subscribe({
        next: res => {
          this.ticketComments = res;
          this.commentLoading = false;
        },
        error: e => {
          this.commentLoading = false;
        }
      })
    );
  }

  getTicketActivityLogsById() {
    this.activityLoading = true;
    this.subscription.add(
      this.ticketService.getTicketActivityLogsByTicketNumber(this.ticketDetailModel.ticketNumber).subscribe({
        next: res => {
          this.ticketActivityLogs = res;
          this.getResolved();

          this.activityLoading = false;
        },
        error: e => {
          this.activityLoading = false;
        }
      })
    );
  }

  getTicketActionsById() {
    this.actionLoading = true;
    this.subscription.add(
      this.ticketService.getTicketActionsByTicketNumber(this.ticketDetailModel.ticketNumber).subscribe({
        next: (res: TicketAction[]) => {
          for (const i of res) {
            if (i.actionSummary) {
              i.actionSummaryJson = JSON.parse(i.actionSummary);
              for (const j of i.actionSummaryJson) {
                j.Value = JSON.parse(j.Value);
              }
            }
          }
          this.ticketActions = res;
          this.actionLoading = false;
        },
        error: e => {
          this.actionLoading = false;
        }
      })
    );
  }

  onBack(): void {
    if (this.fromDetailToEdit) {
      this.isEdit = false;
      this.isDetail = true;
      this.fromDetailToEdit = false;
    } else if (this.storageService.get(AppConstants.isPerviousPageTicketCreate)) {
      this.router.navigateByUrl(APP_ROUTES.TICKET);
      this.storageService.clear(AppConstants.isPerviousPageTicketCreate);
    } else {
      if (window.history.length > 2) {
        this._location.back();
      } else {
        this.router.navigateByUrl(APP_ROUTES.TICKET);
      }
    }
  }

  getTotal(activityLog: TicketActivityLogs[], forItem: string) {
    let count: number = 0;
    for (const i of activityLog) {
      count = count + i[forItem];
    }
    return count;
  }

  getUniqueTruckRollNumbersTotal(activityLog, forItem) {
    // Create a Set to store unique truckRollNumbers
    const uniqueTruckRollNumbers = new Set();
    let count: number = 0;
    // counting of truck roll, if truckRoleBaseOnFieldTech is true, consider the truckRoleCount else
    // if we have truckRollNumber then make sum of those unique truckRollNumber
    // Iterate through the activityLog array
    activityLog.forEach(activity => {
      // Check if the object has a truckRollNumber and it's not empty
      if (activity.truckRoleBaseOnFieldTech && activity.truckRollType === 1) {
        count += activity.truckRoleCount;
      } else {
        if (activity.truckRollNumber && activity.truckRollNumber.trim() !== '' && activity.truckRollType === 1) {
          // Add the truckRollNumber to the Set
          uniqueTruckRollNumbers.add(activity.truckRollNumber);
        }
      }
    });
    // Return the size of the Set, which represents the count of unique truckRollNumbers
    return uniqueTruckRollNumbers.size + count;
  }

  getUniqueUserNameWithTotalHours(ticketFieldTechDto): string {
    const uniqueUsernames = {};
    ticketFieldTechDto.forEach(fieldTech => {
      if (uniqueUsernames[fieldTech.userName]) {
        uniqueUsernames[fieldTech.userName] += fieldTech.hours;
      } else {
        uniqueUsernames[fieldTech.userName] = fieldTech.hours;
      }
    });

    let result = '';
    Object.entries(uniqueUsernames).forEach(([userName, totalHours], index, array) => {
      result += `${userName} (${totalHours}h)`;
      if (index !== array.length - 1) {
        result += ', <span class="ms-1"></span>';
      }
    });
    return result;
  }

  getResolved() {
    for (let j of this.ticketActivityLogs) {
      if (j.isResolve === true) {
        this.resolvedVal = true;
        break;
      } else {
        this.resolvedVal = false;
      }
    }
    return this.resolvedVal;
  }

  changeExclusions(ticketNumber: string) {
    this.getAllExclusionTypeByCustomer(this.ticketModel.customerId);
    if (this.isEdit) {
      if (!this.ticketModel.hasExclusion) {
        this.exclusionTypeData = '';
        this.ticketModel.exclusionTypeId = null;
        if (this.ticketModel.ticketExclusions.length) {
          const ngModalOptions: ModalOptions = {
            backdrop: 'static',
            keyboard: false,
            animated: true,
            initialState: {
              message: 'Are you sure you want to delete exclusions?'
            }
          };
          this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
          this.modalRef.content.onClose.subscribe(
            result => {
              if (result) {
                this.loading = true;
                this.subscription.add(
                  this.ticketService.deleteAllExclusion(ticketNumber).subscribe({
                    next: res => {
                      this.alertService.showSuccessToast('Exclusions deleted.');
                      this.loading = false;
                      this.getAllExclusions();
                    },
                    error: e => {
                      this.loading = false;
                    }
                  })
                );
              } else {
                this.ticketModel.hasExclusion = true;
              }
            },
            err => {
              this.loading = false;
              this.ticketModel.hasExclusion = true;
            }
          );
        } else {
          this.changeExclusionStatus();
        }
      } else {
        this.changeExclusionStatus();
      }
    }
  }

  changeExclusionStatus() {
    this.exclusionLoading = true;
    const data = {
      flag: this.ticketModel.hasExclusion,
      ticketId: Number(this.ticketId)
    };
    this.subscription.add(
      this.ticketService.changeExclusionStatus(data).subscribe({
        next: res => {
          this.exclusionLoading = false;
          this.ticketDetailModel.hasExclusion = this.ticketModel.hasExclusion;
          this.alertService.showSuccessToast('Exclusion updated.');
        },
        error: e => {
          this.ticketModel.hasExclusion = !this.ticketModel.hasExclusion;
          this.exclusionLoading = false;
        }
      })
    );
  }

  getAllExclusions() {
    this.exclusionsLoading = true;
    this.subscription.add(
      this.ticketService.getAllExclusions(this.ticketModel.ticketNumber).subscribe({
        next: (res: TicketExclusions[]) => {
          this.ticketDetailModel.ticketExclusions = res;
          if (this.ticketDetailModel.ticketExclusions.length) {
            this.ticketDetailModel.ticketExclusions.forEach(ex => {
              if (ex.from) {
                this.ticketService.formateDate(ex.from);
              }
              if (ex.to) {
                this.ticketService.formateDate(ex.to);
              }
            });
          }
          this.exclusionsLoading = false;
        },
        error: e => {
          this.exclusionsLoading = false;
        }
      })
    );
  }

  accordionChange(e, val: string) {
    this[val] = e ? false : true;
  }

  downloadTicketPDF() {
    this.loading = true;
    this.ticketService.downloadTicketPDF(this.ticketNumber).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, 'application/pdf');
          link.download = this.ticketDetailModel.ticketNumber;
          link.click();
          this.loading = false;
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  removeIsPerviousPageTicketList(): void {
    if (this.storageService.get(AppConstants.isPerviousPageTicketCreate)) {
      this.storageService.clear(AppConstants.isPerviousPageTicketCreate);
    }
  }

  linkNewTicket() {
    if (this.isEdit && this.ticketDetailModel.customerId !== this.ticketModel.customerId) {
      this.alertService.showWarningToast('please save changes of new customer to link further tickets.');
    } else {
      if (this.isCreate) {
        const newLink = new ReletedTicketsMapDto();
        newLink.customerId = this.ticketModel.customerId;

        this.ticketModel.reletedTickets.push(newLink);
        this.loadtickets();
      } else {
        const ngModalOptions: ModalOptions = {
          backdrop: 'static',
          keyboard: false,
          animated: true,
          class: 'modal-lg modal-dialog-right',
          initialState: {
            ticketId: this.ticketId,
            customerId: this.ticketModel.customerId
          }
        };
        this.modalRef = this.modalService.show(LinkIssueComponent, ngModalOptions);
        this.modalRef.content.onClose.subscribe(res => {
          if (res) {
            this.loading = false;
            if (this.isEdit || this.isDetail) {
              this.alertService.showSuccessToast('Linked issue successfuly.');
              this.getTicketDetailByTicketNumber(this.ticketModel.ticketNumber);
              this.showIssueBlock = true;
            }
          }
        });
      }
    }
  }

  removeLinkTicket(id: number, index: number, childIndex: number = 0) {
    if (id) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to remove link issue?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(
        result => {
          if (result) {
            this.loading = true;
            this.subscription.add(
              this.ticketService.deleteticketrelated(id).subscribe({
                next: res => {
                  this.alertService.showSuccessToast('Removed link issue.');
                  this.ticketModel.reletedTicketsDetails[index].tickets.splice(childIndex, 1);
                  this.getTicketDetailByTicketNumber(this.ticketModel.ticketNumber);
                },
                error: e => {
                  this.loading = false;
                }
              })
            );
          }
        },
        err => {
          this.loading = false;
        }
      );
    } else {
      this.ticketModel.reletedTickets.splice(index, 1);
    }
  }

  trackByFn(item) {
    return item.id;
  }

  private loadtickets() {
    if (this.ticketId === undefined) {
      this.ticketId = 0;
    }
    this.customerId = this.ticketModel.customerId;
    this.ticketList = concat(
      of([]), // default items
      this.ticketInput.pipe(
        distinctUntilChanged(),
        tap(() => (this.ticketLoading = true)),
        switchMap(term =>
          this.ticketService.getAllTickets(term, this.customerId, this.ticketId).pipe(
            catchError(() => of([])), // empty list on error
            tap(() => (this.ticketLoading = false))
          )
        )
      )
    );
  }

  checkRelatedTicket(id, index) {
    const count = this.ticketModel.reletedTickets.filter(x => x.relatedTicketId === id).length;
    if (count > 1) {
      setTimeout(() => {
        this.ticketModel.reletedTickets[index].relatedTicketId = null;
      }, 0);
      this.alertService.showErrorToast('Ticket already linked');
    }
  }

  getJhaList() {
    this.loading = true;
    this.ticketService.getJhaReport(this.ticketNumber).subscribe({
      next: (data: any) => {
        if (data) {
          this.jhaDetail = data;
          this.loading = false;
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  selectDeselectAll() {
    this.alertsList.forEach(alert => {
      alert.isSelected = this.isMasterSel;
    });
  }

  getSelectedValue() {
    return this.alertsList.length ? this.alertsList.every(alert => alert.isSelected) : false;
  }

  submitAlertsForTickets() {
    this.loading = true;
    let data = {
      ticketId: this.ticketId,
      checkTicketDeviceMaps: JSON.parse(JSON.stringify(this.alertsList.filter(ticket => ticket.isSelected)))
    };
    data.checkTicketDeviceMaps.forEach(alert => delete alert.isSelected);

    this.ticketService.addOutageTicket(data).subscribe({
      next: () => this.onCancel(),
      error: () => this.onCancel()
    });
  }
  createItem(item): FormGroup {
    return this.fb.group({
      ...item
    });
  }
  // TODO Remove once completed and tested
  // addItem(): void {
  //   let randomGUID = Guid.create();
  //   const item = {
  //     ticketId: 0,
  //     ticketEstId: 0,
  //     estNumber: 1,
  //     additionalHours: 1,
  //     estimateTotal: 1,
  //     isDeleted: false,
  //     ticketEstimateAttachments: [],
  //     randomGUID: randomGUID['value']
  //   };
  //   this.items.push(this.createItem(item));
  // }
  onClickRemoveRecord(item, index) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          if (this.isCreate) {
            this.estTableData[index].isDeleted = true;
            this.alertService.showSuccessToast('Ticket estimate deleted successfully');
          } else {
            this.deleteTicketEstimates(item);
          }
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  deleteTicketEstimates(deleteObj) {
    deleteObj.isDeleted = true;
    this.subscription.add(
      this.ticketService.addUpdateTicketEstimate(deleteObj).subscribe({
        next: res => {
          this.alertService.showSuccessToast('Ticket estimate deleted successfully');
        },
        error: e => {
          this.loading = false;
        }
      })
    );
    err => {
      this.loading = false;
    };
  }

  onClickEditEstimateRecord(index) {
    this.isEstimateAdded = true;
    this.isEstimateEnable = true;
  }
  onCancel() {
    this.modalRef.hide();
    this.ticketAction(this.ticketData.ticketNumber, !this.ticketData.isCreated);
  }
  onAddEstimate(estDetails, mode, index = null) {
    this.isEstimateEnableIndex = index;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        deviceItem: 1,
        ticketId: this.ticketId,
        ticketNumber: this.ticketDetailModel.ticketNumber,
        siteId: this.ticketModel.siteId,
        ticketdetail: this.ticketModel,
        estDetails: estDetails,
        lineIndex: index,
        userRole: this.user[0],
        isTicketCreate: this.isCreate,
        mode: mode
      }
    };
    this.modalRef = this.modalService.show(EstimateModelComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      if (res) {
        this.loading = false;
        let findData = this.estTableData.filter(x => x.randomGUID === res.randomGUID);
        if (!this.isCreate) {
          this.getTicketEstimatesByNumber();
        } else if (findData.length) {
          this.estTableData[this.isEstimateEnableIndex] = res;
        } else {
          this.estTableData.push(res);
        }
      }
    });
  }

  getTicketEstimatesByNumber() {
    this.activityLoading = true;
    this.subscription.add(
      this.ticketService.getTicketEstimatesByTicketNumber(this.ticketDetailModel.ticketNumber).subscribe({
        next: res => {
          this.estTableData = res;
          this.activityLoading = false;
        },
        error: e => {
          this.activityLoading = false;
        }
      })
    );
  }

  getStatusLabel(statusId: number) {
    const statusObj = this.statusDropDownList.find(item => item.id === statusId);
    return statusObj ? statusObj.name : '';
  }

  goToLink(url: string) {
    window.open(url, '_blank');
  }
  getTotalData(item) {
    let estData = this.estTableData.filter(x => x.isDeleted === false);
    if (item === 'additionalHour') {
      const sumofAdditionalHours = estData.reduce((accumulator, object) => {
        return accumulator + object.additionalHours;
      }, 0);
      return sumofAdditionalHours;
    } else {
      const sumOfEstimateTotal = estData.reduce((accumulator, object) => {
        return accumulator + object.estimateTotal;
      }, 0);
      return sumOfEstimateTotal;
    }
  }
  removeComma(item) {
    return item.toString().replace(/,/g, '');
  }

  getTypeBaseFile(files: TicketAttachment[], isImage = false) {
    if (isImage) {
      return files.filter(file => file.fileType === 'image');
    } else {
      return files.filter(file => file.fileType !== 'image');
    }
  }

  // below code is for 2710 gallery dropbox related changes
  onAttachmentTabChange(tabTitle, params = null) {
    this.currentAttachmentTab = tabTitle ? tabTitle : 'Files';
    if (this.ticketDetailModel.id) {
      const fileType = this.currentAttachmentTab === 'Files' ? 'document' : this.currentAttachmentTab === 'Images' ? 'image' : 'video';
      const requestParams = {
        fileType: fileType,
        page: params ? params.page : 0,
        itemsCount: params ? params.itemsCount : 10
      };
      if (!params) {
        this.filesPaginationParams = new FileListPaginationParams();
        this.imagesPaginationParams = new FileListPaginationParams();
        this.videoPaginationParams = new FileListPaginationParams();
      }
      if (this.currentAttachmentTab === 'Files') {
        this.getTicketAttachmentsList(requestParams, this.currentAttachmentTab);
      } else if (this.currentAttachmentTab === 'Images') {
        this.getTicketAttachmentsList(requestParams, this.currentAttachmentTab);
      } else {
        this.getTicketAttachmentsList(requestParams, this.currentAttachmentTab);
      }
    }
  }

  getTicketAttachmentsList(requestParams, fileType: string) {
    this.attachmentsLoading = true;
    const getListingParams = {
      siteId: this.ticketDetailModel.siteId,
      customerId: this.ticketDetailModel.customerId,
      portfolioId: this.ticketDetailModel.portfolioId,
      entityId: this.ticketId,
      entityNumber: this.ticketDetailModel.ticketNumber,
      parentId: this.ticketId,
      fileType: requestParams.fileType,
      imagePreviewId: 0,
      isCustomerFacing: false,
      moduleType: 3,
      page: requestParams.page,
      sortBy: '',
      direction: '',
      itemsCount: requestParams.itemsCount
    };
    this.subscription.add(
      this.dropBoxService.getGalleryImageFiles(getListingParams).subscribe({
        next: (res: AttachmentListResponse) => {
          if (fileType === 'Files') {
            this.fileAttachments = res;
          } else if (fileType === 'Images') {
            this.imageAttachments = res;
          } else {
            this.videoAttachments = res;
          }
          this.attachmentsLoading = false;
        },
        error: err => {
          this.attachmentsLoading = false;
        }
      })
    );
  }

  onChangeSize(changeFor) {
    if (changeFor === 'Files') {
      this.filesPaginationParams.itemsCount = Number(this.filesPaginationParams.pageSize);
      const params = {
        page: 0,
        itemsCount: this.filesPaginationParams.itemsCount
      };
      this.onAttachmentTabChange('Files', params);
    } else if (changeFor === 'Images') {
      this.imagesPaginationParams.itemsCount = Number(this.imagesPaginationParams.pageSize);
      const params = {
        page: 0,
        itemsCount: this.imagesPaginationParams.itemsCount
      };
      this.onAttachmentTabChange('Images', params);
    } else {
      this.videoPaginationParams.itemsCount = Number(this.videoPaginationParams.pageSize);
      const params = {
        page: 0,
        itemsCount: this.videoPaginationParams.itemsCount
      };
      this.onAttachmentTabChange('Videos', params);
    }
  }

  onPageChange(obj, changeFor) {
    if (changeFor === 'Files') {
      this.filesPaginationParams.currentPage = obj;
      const params = {
        page: obj - 1,
        itemsCount: this.filesPaginationParams.itemsCount
      };
      this.onAttachmentTabChange('Files', params);
    } else if (changeFor === 'Images') {
      this.imagesPaginationParams.currentPage = obj;
      const params = {
        page: obj - 1,
        itemsCount: this.imagesPaginationParams.itemsCount
      };
      this.onAttachmentTabChange('Images', params);
    } else {
      this.videoPaginationParams.currentPage = obj;
      const params = {
        page: obj - 1,
        itemsCount: this.videoPaginationParams.itemsCount
      };
      this.onAttachmentTabChange('Videos', params);
    }
  }

  openFileUploadSidePanel(mode, fileItem) {
    const entityDetails = {
      customerId: this.ticketDetailModel.customerId,
      portfolioId: this.ticketDetailModel.portfolioId,
      siteId: this.ticketDetailModel.siteId,
      entityId: this.ticketDetailModel.id,
      entityNumber: this.ticketDetailModel.ticketNumber,
      moduleType: 3
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        isFileEditMode: mode,
        fileItemObj: fileItem,
        entityDetails: entityDetails,
        parentModuleName: 'CMticket',
        isParentCreateMode: this.isCreate ? true : false
      }
    };
    this.modalRef = this.modalService.show(CommonDropboxFileUploadComponent, ngModalOptions);
    if (this.isCreate) {
      this.modalRef.content.fileUploadList.subscribe(res => {
        if (res && res.length) {
          this.mergeFileLists(res);
        }
      });
    } else {
      this.modalRef.content.isParentRefresh.subscribe(res => {
        if (res) {
          this.getTicketDetailByTicketNumber(this.ticketModel.ticketNumber);
        }
      });
    }
  }

  mergeFileLists(fileTypeList) {
    if (this.createFileUploadList.length === 0) {
      this.createFileUploadList = fileTypeList.slice();
    } else {
      this.createFileUploadList.push(...fileTypeList);
    }
  }

  openDropBoxImageGallery(activityLog: TicketActivityLogs) {
    const requestParamsConfig = {
      id: 0,
      customerId: this.ticketDetailModel.customerId,
      portfolioId: this.ticketDetailModel.portfolioId,
      siteId: this.ticketDetailModel.siteId,
      entityId: activityLog ? activityLog.id : this.ticketId,
      parentId: this.ticketId,
      entityNumber: this.ticketDetailModel.ticketNumber,
      activityDate: activityLog ? activityLog.createdDate : '',
      isCustomerFacing: false,
      imagePreviewId: 0,
      moduleType: activityLog ? 4 : 3,
      sortBy: '',
      direction: '',
      page: 0,
      itemsCount: 15
    };
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-dialog image-gallery-modal',
      initialState: {
        requestParamsConfig: requestParamsConfig,
        activityDate: activityLog ? activityLog.date : ''
      }
    };
    this.modalRef = this.modalService.show(ImageDropboxGalleryComponent, ngModalOptions);
    this.modalRef.content.isParentRefresh.subscribe(res => {
      if (res) {
        this.getTicketDetailByTicketNumber(this.ticketModel.ticketNumber);
      }
    });
  }

  removeSelectedErrors(form: NgForm): void {
    Object.entries(form.form.controls).forEach(([key, control]) => {
      if (key.includes('endDateTime') || key.includes('startDateTime')) {
        const errors = (control as FormControl).errors;

        if (errors) {
          const filteredErrors = Object.fromEntries(
            Object.entries(errors).filter(([errKey]) => ['minDateTimeError', 'maxDateTimeError'].includes(errKey))
          );

          (control as FormControl).setErrors(Object.keys(filteredErrors).length > 0 ? filteredErrors : null);
        }
      }
    });
  }

  getUserName() {
    const userInfo = this.storageService.get('user');
    return `${userInfo.firstName} ${userInfo.lastName}`;
  }

  prepareFilesObjForTicketCreate(file, fileType, fileItemDetails = null) {
    const fileObj = {
      file: file,
      id: this.generateUniqueId(),
      fileName: file.name,
      fileTag: fileItemDetails ? fileItemDetails.fileTag : [],
      fileTagTxt: fileItemDetails ? fileItemDetails.fileTagTxt : [],
      notes: fileItemDetails ? fileItemDetails.notes : null,
      fileType: fileType,
      createdBy: this.getUserName(),
      createdDate: file.lastModifiedDate
    };
    this.createFileUploadList.push(fileObj);
  }

  generateUniqueId() {
    const prefix = 'QE_' + Date.now();
    const randomSuffix = Math.random().toString(36).substr(2, 9);
    return prefix + '_' + randomSuffix;
  }

  uploadFilesToGallery(event: any, fileType: string) {
    const files: FileList = event.target.files;
    if (files.length === 0) {
      this.imageInput.nativeElement.value = '';
      this.videoInput.nativeElement.value = '';
      return;
    }
    const fileTypeText = fileType === 'image' ? 'Image' : 'Video';
    const allowedExtensions = {
      image: AppConstants.allowedDropboxImages,
      video: AppConstants.allowedDropboxVideos
    };
    const formData: FormData = new FormData();
    for (let i = 0; i < files.length; i++) {
      const file: File = files[i];
      // Check if the file type is allowed
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions[fileType].includes(fileExtension)) {
        this.alertService.showErrorToast(`Unsupported file type`);
        continue;
      }

      if (this.ticketDetailModel.id) {
        formData.append('files', file);
      } else {
        this.prepareFilesObjForTicketCreate(file, fileType);
      }
    }
    if (formData.getAll('files').length === 0) {
      return;
    }

    if (this.ticketDetailModel.id) {
      // check the number of files which are > 300 mbs and which are < 200 mbs and prepare two arrays for the same
      // now for the files which are less than 200 mb will be proccessed as belows and
      // for the file greater than 200 will require to be uploaded as chunk
      const bufferChunkSize = 200 * (1024 * 1024);

      if (event.target.files.length) {
        this.chunkUploadInProgress = true;
        this.commonService.isChunkUploadInProgress$.next(true);
        let totalChunksCount = 0; // Track total chunks across all files

        for (const file of event.target.files) {
          const fileUploadTimeStamp = new Date().getTime();
          const totalChunks = Math.ceil(file.size / bufferChunkSize);
          totalChunksCount += totalChunks; // Add to the global count
          this.startChunkUpload(file, bufferChunkSize, fileUploadTimeStamp, totalChunks, fileType);
        }

        this.remainingChunks$.next(totalChunksCount); // Initialize the counter
      }
    }
  }

  startChunkUpload(file: File, chunkSize: number, fileUploadTimeStamp: number, totalChunks: number, fileType) {
    let chunkUpload$: Observable<void> = of(undefined);

    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      chunkUpload$ = chunkUpload$.pipe(
        concatMap(() =>
          this.uploadChunk(chunk, chunkIndex, totalChunks, file, fileUploadTimeStamp, fileType).pipe(
            finalize(() => {
              // Decrease the global counter after each chunk
              this.remainingChunks$.next(this.remainingChunks$.value - 1);
              // Check if it's the last chunk of the file and remove from chunkDetails
              if (chunkIndex + 1 === totalChunks) {
                this.removeFileFromChunkDetails(fileUploadTimeStamp);
              }
            })
          )
        )
      );
    }

    chunkUpload$.subscribe({
      next: () => {
        console.log(`All chunks uploaded successfully for file: ${file.name}`);
      },
      error: err => console.error(`Error during chunk upload for file: ${file.name}`, err)
    });

    // Monitor when uploads are fully complete
    this.remainingChunks$.subscribe(remaining => {
      if (remaining === 0) {
        console.log('All files and chunks uploaded.');
        this.chunkUploadInProgress = false;
        this.commonService.isChunkUploadInProgress$.next(false);
        this.commonService.setAutoLogoutValue(false);

        setTimeout(() => {
          if (!this.chunkUploadInProgress) {
            this.alertService.showSuccessToast('Files upload completed.');
            const requestParams = {
              fileType: fileType,
              page: 0,
              itemsCount: 10
            };
            if (this.currentAttachmentTab === 'Files') {
              this.getTicketAttachmentsList(requestParams, this.currentAttachmentTab);
            } else if (this.currentAttachmentTab === 'Images') {
              this.getTicketAttachmentsList(requestParams, this.currentAttachmentTab);
            } else {
              this.getTicketAttachmentsList(requestParams, this.currentAttachmentTab);
            }
            this.commonService.commonChunkUploadDetails = [];
            this.commonService.chunkUploadDetails$.next(null);
          }
        }, 1000);
      } else {
        this.chunkUploadInProgress = true;
        this.commonService.isChunkUploadInProgress$.next(true);
        this.commonService.setAutoLogoutValue(true);
      }
    });
  }

  uploadChunk(chunk: Blob, chunkIndex: number, totalChunks: number, file: File, fileUploadTimeStamp: number, fileType): Observable<void> {
    const bufferChunkSize = 200 * (1024 * 1024);
    const chunkDetails: ChunkUploadProgressDetails = {
      fileName: file.name,
      currentChunk: chunkIndex + 1,
      totalChunks: totalChunks,
      fileUploadTimeStamp: fileUploadTimeStamp
    };
    let filePartName = '';
    if (file.size > bufferChunkSize) {
      filePartName = `${file.name}.part_${chunkIndex + 1}.${totalChunks}`;
    } else {
      filePartName = `${file.name}`;
    }
    let isChunkUpload = true;
    const formData = new FormData();

    // original form data
    formData.append('customerId', `${this.ticketDetailModel.customerId}`);
    formData.append('id', '0');
    formData.append('portfolioId', `${this.ticketDetailModel.portfolioId}`);
    formData.append('siteId', `${this.ticketDetailModel.siteId}`);
    formData.append('entityId', `${this.ticketDetailModel.id}`);
    formData.append('entityNumber', `${this.ticketDetailModel.ticketNumber}`);
    formData.append('moduleType', '3');
    formData.append('fileType', fileType);
    formData.append('fileTagIds', null);
    formData.append('notes', '');

    formData.append('files', chunk, filePartName);
    formData.append('fileName', filePartName);
    // form data for chunk upload
    if (file.size > bufferChunkSize) {
      formData.append('totalPart', `${totalChunks}`);
      formData.append('currentPart', `${chunkIndex + 1}`);
      formData.append('fileUploadTimeStamp', `${fileUploadTimeStamp}`);
      formData.append('IsLargeFile', `${isChunkUpload}`);
    } else {
      formData.append('IsLargeFile', `false`);
    }
    return this.commonService.uploadChunk(formData, chunkDetails).pipe(
      takeUntil(this.destroy$),
      tap(() => {
        console.log(`Chunk ${chunkIndex + 1}/${totalChunks} uploaded successfully.`);
      }),
      catchError(err => {
        console.error(`Error uploading chunk ${chunkIndex + 1}/${totalChunks}:`, err);
        throw err; // Propagate error for retry or higher-level handling
      })
    );
  }

  removeFileFromChunkDetails(fileUploadTimeStamp: number) {
    // Filter out the completed file based on fileUploadTimeStamp
    const updatedChunkDetails = this.commonService.chunkUploadDetails$.value.filter(
      details => details.fileUploadTimeStamp !== fileUploadTimeStamp
    );
    setTimeout(() => {
      this.commonService.chunkUploadDetails$.next(updatedChunkDetails);
    }, 500);
  }

  isPlPlusUser(userRole: string): boolean {
    return this.checkAuthorisationsFn([this.roleType.PORTFOLIOMANAGER, this.roleType.ADMIN, this.roleType.MANAGER, this.roleType.SUPPORT]);
  }

  downloadDropBoxFile(fileId, fileName) {
    this.loading = true;
    this.dropBoxService.downloadPreviewedImage(fileId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, data.type);
          link.download = fileName;
          link.click();
          this.loading = false;
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  deleteDropBoxFile(fileId, fileType, isCreateMode = false) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (isCreateMode) {
          this.createFileUploadList = this.createFileUploadList.filter(item => item.id !== fileId);
        } else {
          this.subscription.add(
            this.dropBoxService.deleteImageGalleryFiles(fileId).subscribe({
              next: data => {
                this.alertService.showSuccessToast(`${fileType} deleted Successfully.`);
                this.onAttachmentTabChange(this.currentAttachmentTab);
                this.loading = false;
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      }
    });
  }

  getTicketBillingStatuses() {
    this.ticketService.getTicketBillingStatuses().subscribe((res: TicketBillingStatusesResponse[]) => {
      this.ticketBillingStatuses = res;
    });
  }

  getTicketTypeList() {
    this.subscription.add(
      this.ticketService.getTicketTypeList().subscribe({
        next: res => {
          this.ticketTypeList = res.map(item => ({ id: item.ticketTypeId, name: item.ticketTypeName }));
          const defaultTicketType = this.ticketTypeList.find(item => item.name === 'CM');
          if (this.isCreate && defaultTicketType) {
            if (this.ticketModel && !this.ticketModel.ticketTypeId) {
              this.ticketModel.ticketTypeId = defaultTicketType.id;
              this.ticketModel.ticketTypeName = defaultTicketType.name;
            }
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getTicketStatusValue(ticketBillingStatusID: number, list: TicketBillingStatusesResponse[], val = 'description') {
    const obj = list.filter(item => item.ticketBillingStatusID === ticketBillingStatusID);
    if (obj.length) {
      return obj[0][val];
    }
  }

  updateTicketBillingStatus(statusId: number) {
    const billingStatus = this.ticketBillingStatuses.find(status => status.ticketBillingStatusID === statusId);
    this.ticketModel.ticketBillingStatusDescription = billingStatus ? billingStatus.description : null;
  }

  openBulkBulkCloseReopenModal() {
    const ticketDetails = this.isDetail ? this.ticketDetailModel : this.ticketModel;
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-view-dialog',
      initialState: {
        isSingleTicketAction: true,
        ticketDetails: {
          ...ticketDetails,
          customerPortfolio: `${ticketDetails.customerName} (${ticketDetails.portfolioName})`
        }
      }
    };

    this.modalRef = this.modalService.show(BulkTicketCloseReOpenActionComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.modalRef ? this.modalRef.hide() : null;
        this.getTicketDetailByTicketNumber(this.ticketNumber);
      }
    });
  }

  contractedHoursToggleChange(activityId = null, isActivityContractedHours = false) {
    this.loading = true;
    const reqObj: any = {
      ticketId: this.ticketId,
      isContractedHours: this.ticketModel.isContractedHours,
      activityId: activityId,
      isActivityContractedHours: isActivityContractedHours
    };
    this.subscription.add(
      this.ticketService.updateContractedHoursToggle(reqObj).subscribe({
        next: res => {
          this.loading = false;
          this.ticketModel.contractedHours = res.contractedHours;
          this.alertService.showSuccessToast(res.message);
          if (!activityId) {
            this.getTicketActivityLogsById();
          }
        },
        error: e => {
          this.loading = false;
          this.ticketModel.isContractedHours = !this.ticketModel.isContractedHours;
        }
      })
    );
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.isEdit || this.isDetail) {
      this.storageService.clear(AppConstants.isPerviousPageTicketCreate);
    }
  }
}
