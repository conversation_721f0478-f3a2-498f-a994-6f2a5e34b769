import { ListOfUnit } from '../../entities/site-device/site-device.model';
import { AppConstants } from '../constants/app.constant';

export class Site {
  public id: number;
  public portfolioId: number;
  public siteName: string;
  public site: string;
  public dummy: any;
  public googleMapLink: string;
  public automationSite: string;
  public automationSiteName: string;
  public siteAutomationDetails: SiteAutomationDetails = new SiteAutomationDetails();
  public siteLayoutDetail: SiteLayout = new SiteLayout();
  public siteLocation: SiteLocation = new SiteLocation();
  public customerId: number;
  public customerName: string;
  public listOfDeviceWeatherStation: any = [];
  public listOfDeviceMeter: any = [];
  public siteNumber: string;
  public qeSiteId: string;
  public openWorkOrderCount: number;
  public closeWorkOrderCount: number;
  public workOrderCount: number;
  public portfolioName: string;
  public siteImages: SiteImages[];
  public siteZoneImages: SiteImages[];
  public customerPortfolio: string;
  public siteDeviceCount: number;
  public isActive: boolean;
  public isArchive: boolean;
  public isGADSReporting: boolean;
  public lockBoxCode: string;
  public customerContactEmails: CustomerContact[] = [];
  public portfolioCustomerContactId: number;
  public sitePortfolioCustomerContactMap: SitePortfolioCustomerContactMap[] = [];
  public contractStartDate: Date | string;
  public siteTimeZoneOffset: number;
  public isNERC = false;
  public outageSetting?: SitesOutageResponse;
  public siteInfoImageURL: string;
  public archiveUpdatedData = [];
  public siteAccess: SiteAccess = new SiteAccess();
  public contractId: number;
  public contractName: string;
  public totalXfmr?: number;
  public xfmr: number;
  public dryXFMR: number;
  public bessxfmr: number;
  public utilityOwned: boolean;
  public isUtilityBessXFMR: boolean;
  public isUtilityDryXFMR: boolean;
  public zones: Zones[] = [];
  public contractEndDate: Date | string;
  public isSelected? = false;
  public nercSiteTypeId: number;
  public nercSiteTypeStr: string;
  public cost: number;
  public isSiteSelectedForVGTWO: boolean = false;
  public qeServiceTypes: number[] = [];
  public qeServiceTypeStr: string;
  public isBankofHours: boolean = false;
  public pendingContractHours: number;
  public remainingContractHours: number;
  public contractedHoursPercent: number;
  public fiscalDateRange: { start: Date | string; end: Date | string };
  public contractFiscalEndDate: Date | string;
  public contractedHours: number;
  public replenishmentTypeId: number;
}

export class Zones {
  id: number;
  zoneName: string;
  isSelected: boolean;
  zoneWOName: string;
  isDeleted: boolean;
  customZoneAddEditId: string;
  hasError: boolean;
  constructor(
    id: number = 0,
    zoneName: string = '',
    zoneWOName: string = '',
    isSelected: boolean = false,
    isDeleted: boolean = false,
    customZoneAddEditId: string = '',
    hasError: boolean = false
  ) {
    this.id = id;
    this.zoneName = zoneName;
    this.zoneWOName = zoneWOName;
    this.isSelected = isSelected;
    this.isDeleted = isDeleted;
    this.customZoneAddEditId = customZoneAddEditId;
    this.hasError = hasError;
  }
}

export class SiteAccess {
  public emergencyPhone: string;
  public liftRequired: boolean;
  public liftDetails: string;
  public ladderRequired: boolean;
  public ladderDetails: string;
  public noticeRequired: boolean;
  public noticeDetails: string;
  public weekendWorkAllowed: boolean;
  public pmRequiresTwoTechnicians: boolean;
  public siteNotes: string;
}

export class SitesOutageResponse {
  public customerId: number;
  public isParentSetting: boolean;
  public outageId: number;
  public parentSettingId: number;
  public portfolioId: number;
  public siteId: number;
  public powerThreshold: number;
  public settingType: number;
  public zeroGeneration: boolean;
  public triggerCount: number;
  public timeSetting: SitesOutageTimeSetting[];
  public portfoliosSites: OutagePortfolioSitesSetting[];
}

export class OutagePortfolioSitesSetting {
  public id: number;
  public isParentSetting: boolean;
  public name: string;
  public zeroGeneration: boolean;
  public sites: OutageSitesSetting[];
}
export class OutageSitesSetting {
  public id: number;
  public isParentSetting: boolean;
  public siteName: string;
  public zeroGeneration: boolean;
}

export enum MonthFullName {
  JANUARY = 'January',
  FEBRUARY = 'February',
  MARCH = 'March',
  APRIL = 'April',
  MAY = 'May',
  JUNE = 'June',
  JULY = 'July',
  AUGUST = 'August',
  SEPTEMBER = 'September',
  OCTOBER = 'October',
  NOVEMBER = 'November',
  DECEMBER = 'December'
}

export class SitesOutageTimeSetting {
  public month: number;
  public startTime: string;
  public endTime: string;
}

export class PrimaryDevice {
  public listOfDeviceMeter;
  public listOfDeviceWeatherStation;
}
export class ListOfAutomationSitePerformance {
  public automationSitePerformanceId: number = null;
  public automationSiteDetailId: number = null;
  public month: number;
  public monthName: string;
  public expectedProductionValue: number;
  public expectedInsolationValue: number;
}
export class DataSource {
  public listOfAutomationDataSource: ListOfAutomationDataSource[] = [];
  public listOfAutomationDataSourcePartner: ListOfAutomationDataSourcePartner[] = [];
}

export class PMFilter {
  public year: number = null;
  public assessmentType: number[] = [];
  public siteId: number;
}
export class SitePhotoPMTableModal {
  public id: number;
  public workorderId: number;
  public customerName: string;
  public customerPortfolio: string;
  public siteName: string;
  public workorderName: string;
  public portflioName: string;
  public reportType: string;
  public year: number;
  public uploadedDate: string;
  public giImages: CmPmImagesModal[];
}
export class SitePhotoCMTableModal {
  public id: number;
  public customerPortfolio: string;
  public imageCount: number;
  public truckRollCount: number;
  public issue: string;
  public open: string;
  public siteDeviceName: string;
  public deviceLabel: string;
  public ticketNumber: string;
  public siteName: string;
  public status: string;
  public ticketImages: CmPmImagesModal[];
}

export class CmPmImagesModal {
  public imgId: number;
  public imgUrl: string;
}

export class CMFilter {
  public year: number = null;
  public priority: number[] = [];
  public siteId: number;
}

export class SiteAutomationDetails {
  public automationSiteDetail: AutomationSiteDetail[] = [];
  public automationSitePerformanceDetails: AutomationSitePerformanceDetails = new AutomationSitePerformanceDetails();
  public listOfAutomationSitePerformanceModelTable: ListOfAutomationSitePerformance[] = [];
}

export class AutomationSiteDetail {
  public automationDataSourceId: number;
  public automationDataPartnerName: string;
  public automationDataSourceName: string;
  public automationSiteDetailId: string;
  public automationSiteName: string;
  public isActive = true;
  public isPrimarySite: boolean;
  public partnerNumber: string;
  public siteId: number;
  public siteNumber: number;
  public siteDeviceCount: number;
  public ipAddress: string;
  public token: number;
  public automationPassword: string;
  public automationUserName: string;
}

export class AutomationSitePerformanceDetails {
  public inverterEfficiency: number;
  public isStringInverterSite = false;
  public moduleDegradation: any;
  public primaryMeterDevice: number[] = [];
  public primaryMeterDeviceName: string;
  public primaryWeatherSensorDeviceName: string;
  public primaryWeatherSensorDevice: number = null;
  public partnerId: number;
  public siteId: number;
  public startMonth: number;
  public startYear: number;
  public systemLosses: number;
  public temperatureCoefficient: number;
  public sitePerformanceDetailId: string;
  public aggregateMeterDevice: number[] = [];
  public aggregateMeterDeviceName: string;
  public subMeterDevice: number[] = [];
  public subMeterDeviceName: string;
}
export class SiteLayout {
  public inv: number;
  public siteType: number[] = [];
  public siteTypeStr: string;
  public inverterTypes: number[] = [];
  public inverterType: number;
  public xfmr: number;
  public dryXFMR: number;
  public bessxfmr: number;
  public inverterTypeStr: string;
  public utility: string;
  public utilityContactInfo: string;
  public numberofCombiners: string;
  public tilt: string;
  public numberofPanelboards: number;
  public numberofModules: number;
  public siteLayout: any;
  public asBuiltDrawings: string;
  public dcSize: number;
  public acSize: number;
  public poIkV: number;
  public siteAz: string;
  public utilityNumber: string;
  public poleNumber: string;
  public circuitNumber: string;
  public utilityOwned: boolean;
  public isUtilityBessXFMR: boolean;
  public isUtilityDryXFMR: boolean;
}
export class SiteLocation {
  public address: string;
  public city: string;
  public state: string;
  public stateAbb: string;
  public zipCode: string;
  public latitude: number;
  public logitude: number;
  public googleMapLink: string;
  public siteAccess: any;
  public lockBoxCode: number;
  public countyId: number;
  public countyName: number;
  public countyFIPSCode: number;
}
export class SiteFilterData {
  public totalSite: number;
  public sites: Site[];
}

export class SiteExport {
  public totalSite: number;
  public sites: SiteExportData[];
}
export class SiteExportData {
  public acSize: number;
  public address: any;
  public city: string;
  public closeWorkOrderCount: number;
  public customerContactEmails: any[];
  public customerId: 1;
  public customerName: string;
  public customerPortfolio: string;
  public dcSize: number;
  public googleMapLink: any;
  public id: number;
  public inv: number;
  public inverterType: number;
  public inverterTypeStr: string;
  public inverterTypes: any[];
  public isActive: boolean;
  public isNERC: boolean;
  public latitude: number;
  public lockBoxCode: string;
  public logitude: number;
  public numberofCombiners: string;
  public numberofModules: number;
  public numberofPanelboards: number;
  public openWorkOrderCount: 15;
  public portfolioCustomerContactId: number;
  public portfolioId: number;
  public portfolioName: string;
  public siteAccess: string;
  public siteAz: string;
  public siteDeviceCount: number;
  public siteImages: [];
  public siteLayout: any;
  public siteName: string;
  public siteNumber: string;
  public qeSiteId: string;
  public sitePortfolioCustomerContactMap: any[];
  public siteType: any[];
  public siteTypeStr: string;
  public state: string;
  public stateAbb: string;
  public tilt: any;
  public utility: string;
  public utilityContactInfo: any;
  public utilityOwned: boolean;
  public xfmr: number;
  public dryXFMR: number;
  public bessxfmr: number;
  public zipCode: number;
  public contractStartDate: string;
  public totalXfmr: number;
  public isUtilityBessXFMR: boolean;
  public isUtilityDryXFMR: boolean;
  public nercSiteTypeStr: string;
  public poIkV: number;
  public utilityNumber: number;
  public circuitNumber: number;
  public poleNumber: number;
}
export class SiteImages {
  public image: File;
  public imageURL: string;
  public siteId: string;
  public siteImageId: string;
  public order: number;
}

export class PhotoGalleryImages {
  public id: number;
  public imageUrl: string;
  public keyPhotosForSiteinfo: boolean;
  public keyPhotosSiteDetails: boolean;
  public siteId: number;
  public sectionType: number;
  public isSelected: boolean = false;
}

export const SITEFILTERLIST = {
  customer: 'Customer',
  portfolio: 'Portfolio',
  search: 'Search',
  state: 'State'
};

export const Month = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December'
];

export const MonthID = [
  { id: 1, value: 'January' },
  { id: 2, value: 'February' },
  { id: 3, value: 'March' },
  { id: 4, value: 'April' },
  { id: 5, value: 'May' },
  { id: 6, value: 'June' },
  { id: 7, value: 'July' },
  { id: 8, value: 'August' },
  { id: 9, value: 'September' },
  { id: 10, value: 'October' },
  { id: 11, value: 'November' },
  { id: 12, value: 'December' }
];
export class SiteFilter {
  public customerId: number;
  public portfolioId: number;
  public state = null;
  public search: string;
  public page = 0;
  public sortBy = 'SiteName';
  public direction = 'asc';
  public itemsCount = +AppConstants.rowsPerPage;
}

export const ArrayTypesList = [
  { id: 1, name: 'Roof' },
  { id: 2, name: 'Carport' },
  { id: 3, name: 'Ground (Fixed)' },
  { id: 4, name: 'Ground (Single Axis Tracker)' },
  { id: 6, name: 'Ground (Dual Axis Tracker)' },
  { id: 7, name: 'Floating' }
];

export class CustomerContact {
  public id: number;
  public contactName: string;
  public customerTitle: string;
  public email: string;
  public phoneNumber: number;
  public useAsJHAContact: boolean;
  public useAsTicketContact: boolean;
  public utilityContact: boolean;
  public sendEmailOnPMComplete: boolean;
  public siteId: number;
  public portfolioCustomerContactId: number;
  public defaultUseAsTicketOption: boolean;
}

export class SitePortfolioCustomerContactMap {
  public id = 0;
  public siteId: number;
  public portfolioCustomerContactId: number;
  public useAsTicketContact: boolean;
}

export class ListOfAutomationDataSourcePartner {
  public id: number;
  public partnerNumber: string;
  public partnerName: string;
  public automationDataSourceId: number;
  public displayName: string;
}

export class ListOfAutomationDataSource {
  public id: number;
  public dataSourceName: string;
}

export class AutomationDeviceList {
  public id: number;
  public hardwareId: number;
  public deviceName: string;
  public deviceType: string;
  public deviceTypeId: number;
  public deviceModel: string;
  public qeDeviceId: string;
  public qeDeviceName: string;
  public mfg: string;
  public size: string;
  public dcLoad: string;
  public serialNumber: string;
  public plottingUnit: string;
  public reportingUnit: string;
  public plottingUnitId: number;
  public reportingUnitId: number;
  public rank: number;
  public datasource: string;
  public partnerName: string;
  public automationSiteDetailId: number;
  public disableDCLoad: boolean;
  public disableRank: boolean;
  public disableSerialNumber: boolean;
  public acNameplateKW: number;
  public unitModel: SiteAutomationDevicePlottingReporting;
  public binValue: string;
  public refetchError?: string;
  public isDeviceAlert: string;
}

export class SiteAutomationDevicesList {
  public siteDevices: AutomationDeviceList[];
  public invDCNullCount: number;
  public meterDCNullCount: number;
}

export class SiteAutomationDevicePlottingReporting {
  public listOfPlottingUnit: ListOfUnit[];
  public listOfReportingUnit: ListOfUnit[];
  public listOfSiteLevelUnit: ListOfUnit[];
  public defaultPlottingUnit: number;
  public defaultReportingUnit: number;
  public defaultSiteLevelUnit: number;
}

export class ProductionExpectation {
  public year: number;
  public siteProdBreak: SiteProdBreak[];
}
export class SiteProdBreak {
  public month: number;
  public expIsolation: number;
  public expProduction: number;
  public isStartYear: boolean;
  public yearDifference: number;
  public yearDifferenceColor: string;
}
export class SiteAuditHistory {
  public action: string;
  public auditId: number;
  public auditLogDetails: SiteProdBreak[];
  public customerId: number;
  public entityId: number;
  public logDate: string;
  public portfolioId: number;
  public siteId: number;
  public userName: number;
}
export class AuditLogDetails {
  public fieldName: string;
  public newValue: string;
  public oldValue: string;
}

export enum SiteImageTypeAPIEnum {
  SITE_LAYOUT_IMAGE = 1,
  ZONE_MAP_IMAGE = 2,
  MASTER_IMAGE = 3
}

export enum SiteImageTypes {
  SITE_LAYOUT_IMAGE = 'SiteLayout',
  ZONE_MAP_IMAGE = 'ZoneMap',
  MASTER_IMAGE = 'Master'
}

export const SitePageFilterKeys = [
  'customerIds',
  'portfolioIds',
  'states',
  'regionIds',
  'subregionIds',
  'search',
  'isActive',
  'nercSiteTypeId',
  'automationSiteIds',
  'automationPartnerIds',
  'isArchive',
  'qeServiceTypes'
];

export enum BULK_SITE_ACTIONS_ENUMS {
  DOWNLOAD_TEMPLATE = 1,
  UPLOAD_SITES = 2,
  EXPORT_SITE_AUTHOMATION_DEVICES = 3,
  UPLOAD_SITE_AUTHOMATION_DEVICE_FOR_UPDATES = 4,
  EXPORT_SITES = 5,
  UPLOAD_SITES_UPDATES = 6
}

export const BULK_SITE_ACTIONS_NAMES = {
  [BULK_SITE_ACTIONS_ENUMS.DOWNLOAD_TEMPLATE]: 'Download Site Template',
  [BULK_SITE_ACTIONS_ENUMS.UPLOAD_SITES]: 'Upload Site(s)',
  [BULK_SITE_ACTIONS_ENUMS.EXPORT_SITE_AUTHOMATION_DEVICES]: 'Export Devices',
  [BULK_SITE_ACTIONS_ENUMS.UPLOAD_SITE_AUTHOMATION_DEVICE_FOR_UPDATES]: 'Upload Device Updates',
  [BULK_SITE_ACTIONS_ENUMS.EXPORT_SITES]: 'Export Sites',
  [BULK_SITE_ACTIONS_ENUMS.UPLOAD_SITES_UPDATES]: 'Upload Site Updates'
};

export class BulkSiteAction {
  public id: number;
  public title: string;

  constructor(id: BULK_SITE_ACTIONS_ENUMS) {
    this.id = id;
    this.title = BULK_SITE_ACTIONS_NAMES[id];
  }
}

export enum SITE_ADD_EDIT_SCREEN_TABS_ENUM {
  SITE_INFO = 'siteInfoTab',
  SITE_PHOTO_LIBRARY = 'sitePhotoLibraryTab',
  PERFORMANCE_INFO = 'performanceInfoTab',
  AUTOMATION_SITE_DEVICE_LIST = 'automationSiteDeviceListTab'
}

export const SITE_ADD_EDIT_SCREEN_TABS_NAME = {
  [SITE_ADD_EDIT_SCREEN_TABS_ENUM.SITE_INFO]: 'Site Information',
  [SITE_ADD_EDIT_SCREEN_TABS_ENUM.SITE_PHOTO_LIBRARY]: 'Site Photo Library',
  [SITE_ADD_EDIT_SCREEN_TABS_ENUM.PERFORMANCE_INFO]: 'Performance Information',
  [SITE_ADD_EDIT_SCREEN_TABS_ENUM.AUTOMATION_SITE_DEVICE_LIST]: 'Device List'
};

export class QECategoryType {
  public id: number | string;
  public name: string;
  public groupName: string;
  public groupId: number;
  public parentGroupId: number;
  public parentGroupName: string;
  public disabled: boolean;
  public isSingleSelectable: boolean;
  public isRequired: boolean;
}

export class QEServiceCategoryTypeList {
  public id: number | string;
  public name: string;
  public groupName: string;
  public groupId: number;
  public disabled: boolean;
  public isSingleSelectable: boolean;
  public isRequired: boolean;
  public categoryList: QECategoryType[];
}

export const QEServiceTypeGroupInfo = {
  requiredGroupNames: ['Scale of Site', 'Physical Nature', 'Billing Type'],
  requiredGroupIds: [1, 2, 4],
  singleSelectableGroupNames: ['Scale of Site', 'Physical Nature', 'Billing Type'],
  singleSelectableGroupIds: [1, 2, 4],
  multiSelectableGroupNames: ['Services Provided'],
  multiSelectableGroupIds: [3]
};
