import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot, UrlTree, Route, Router } from '@angular/router';
import { BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { delay, map, Observable, of, pipe, switchMap } from 'rxjs';
import { QEAnalyticsPasswordComponent } from '../qe-analytics-password/qe-analytics-password.component';
import { StorageService } from '../../../@shared/services/storage.service';
import { QEAnalyticsPasswordAuthEnum } from '../models/qe-analytics.enum';
import { AppConstants } from '../../../@shared/constants';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsPasswordGuard implements CanActivate {
  constructor(private readonly router: Router, private readonly storageService: StorageService) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean | UrlTree {
    const permission = this.storageService.get(AppConstants.qeAnalyticsPasswordAuthKey);

    if (environment.env !== 'local') {
      if (!permission || permission === QEAnalyticsPasswordAuthEnum.NOT_OPENED) {
        this.router.navigate(['entities/admin/analytics/password']);
        return false;
      }

      if (permission === QEAnalyticsPasswordAuthEnum.USER_NOT_AUTHENTICATED) {
        return this.router.createUrlTree(['entities/dashboard']);
      }
    }

    return true;
  }
}
