.side-report-menu {
  height: calc(100vh - 204px);
  @media only screen and (max-width: 1200px) {
    height: 100%;
    flex-direction: row;
  }

  .list-group {
    max-height: 300px;
    overflow-y: auto;
    max-width: 100%;
    overflow: auto;
  }
  ul {
    display: flex;
    flex-direction: column;
    // li {
    //   padding-left: 0 !important;
    // }
    @media only screen and (max-width: 1200px) {
      flex-direction: row;
      // flex-wrap: wrap;
    }
    .list-group-item {
      background-color: transparent;
      border: 0;
      border-radius: 5px;
      cursor: pointer;
      &.active-report {
        background-color: #36f;
      }
      span {
        font-weight: bolder;
      }

      @media only screen and (max-width: 1200px) {
        margin: 0px 10px 10px 0px;
      }
    }
  }
}
@media only screen and (min-width: 1200px) {
  .main-content {
    max-height: calc(100vh - 164px);
    overflow-y: scroll;
    overflow-x: hidden;
  }
}

.side-panel-heading {
  color: darkgrey;
  font-size: 12px;
  padding-left: 16px;
  margin-bottom: 0;
}

.custom-dashboard-button {
  // position: absolute !important;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
}

.custom-dashboard-panel-height {
  // max-height: calc( - 50px) !important;
  // overflow-y: scroll;
}
