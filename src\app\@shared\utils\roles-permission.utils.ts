import { StorageService } from '../services/storage.service';
import { ROLE_TYPE } from '../enums';
import { AppConstants } from '../constants';
import { UserAuthorisation } from '../models/user.model';

const storageService: StorageService = new StorageService();
let userAuthorisations: UserAuthorisation;

const hasValidAuthData = (): boolean => {
  return (
    Array.isArray(userAuthorisations?.authorities) &&
    Array.isArray(userAuthorisations?.authorisationIds) &&
    userAuthorisations.authorities.length > 0 &&
    userAuthorisations.authorisationIds.length > 0 &&
    userAuthorisations.authorities.length === userAuthorisations.authorisationIds.length
  );
};

const setUserAuthorisations = (): void => {
  if (!hasValidAuthData()) {
    userAuthorisations = storageService.get(AppConstants.userAuthorisations) ?? new UserAuthorisation([]);
  }
};

export const checkAuthorisations = (allowedRoles: ROLE_TYPE[]): boolean => {
  setUserAuthorisations();
  return allowedRoles.some(allowedRoleItem => new Set(userAuthorisations.authorisationIds).has(allowedRoleItem));
};
