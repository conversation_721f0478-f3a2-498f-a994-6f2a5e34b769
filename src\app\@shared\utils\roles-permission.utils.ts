import { StorageService } from '../services/storage.service';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../enums';

const storageService: StorageService = new StorageService();

export const checkAuthorisations = (allowedRoles: ROLE_TYPE[]): boolean => {
  const authorities: string[] = storageService.get('user')?.authorities ?? [];
  const allowedAuthorities = allowedRoles.map(item => AUTHORITY_ROLE_STRING[item]);
  return allowedAuthorities.some(authority => authorities.includes(authority));
};
