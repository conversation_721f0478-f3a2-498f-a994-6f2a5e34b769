import { DatePipe, Location } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { APP_ROUTES, AppConstants } from '../../../../@shared/constants';
import { AlertService } from '../../../../@shared/services';
import { ContractService } from '../contract.service';
import { ContractObject, ContractRequest, Types } from '../contracts.model';
import { ROLE_TYPE } from '../../../../@shared/enums';

@Component({
  selector: 'sfl-add-edit-contract',
  templateUrl: './add-edit-contract.component.html',
  styleUrls: ['./add-edit-contract.component.scss']
})
export class AddEditContractComponent implements OnInit {
  loading = false;
  contractsData: ContractObject = new ContractObject();
  contractRequest: ContractRequest = new ContractRequest();
  showRates: boolean;
  recalculate: boolean;
  @ViewChild('contractForm') contractForm: NgForm;
  isEdit: boolean;
  contractId: number;
  title: string;
  subscription: Subscription = new Subscription();
  customerId: number;
  roleType = ROLE_TYPE;

  constructor(
    private readonly router: Router,
    private readonly _location: Location,
    public datePipe: DatePipe,
    private readonly route: ActivatedRoute,
    private readonly contractService: ContractService,
    private readonly alertService: AlertService
  ) {}
  types = Object.keys(Types).map(key => ({ key, name: Types[key] }));
  selectedType = 'ENGINEERING';
  technicianBaseRatesByFinancialYears = [];
  electricianBaseRatesByFinancialYears = [];
  mvTechnicianBaseRatesByFinancialYears = [];
  bessTechnicianBaseRatesByFinancialYears = [];
  administrativeBaseRatesByFinancialYears = [];
  engineeringBaseRatesByFinancialYears = [];

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params && params.id) {
        this.isEdit = true;
        this.title = 'Update Contract';
        this.contractId = params.id;
        this.contractService.contractToOpen = this.contractId;
        // let's grab the contract
        this.getContract();
      } else {
        this.isEdit = false;
        this.title = 'Add Contract';
        this.getTheSelectedCustomer();
      }
    });
  }

  getTheSelectedCustomer() {
    this.subscription.add(
      this.contractService.getSelectedCustomer().subscribe(customerId => {
        if (!customerId) this.onBack();
        this.customerId = customerId;
      })
    );
  }
  getContract() {
    //
    this.loading = true;
    this.subscription.add(
      this.contractService.getContract(this.contractId).subscribe({
        next: contractsData => {
          this.contractsData = contractsData;
          this.contractsData.startDate = new Date(this.contractsData.startDate);
          this.contractsData.endDate = new Date(this.contractsData.endDate);
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  saveContract() {
    this.loading = true;
    if (this.contractForm.invalid) {
      this.loading = false;
      return;
    }
    console.log(this.contractForm.value);

    this.contractRequest.id = this.contractId;
    this.contractRequest.customerId = this.contractId ? this.contractsData.customerId : this.customerId;
    this.contractRequest.isActive = this.contractId ? this.contractsData.isActive : true;
    this.contractRequest.contractName = this.contractForm.value.contractName;
    this.contractRequest.administrativeRate = this.contractForm.value.administrativeRate;
    this.contractRequest.bessTechnicianRate = this.contractForm.value.bessTechnicianRate;
    this.contractRequest.dispatchMinimumHours = this.contractForm.value.dispatchMinimumHours;
    this.contractRequest.electricianRate = this.contractForm.value.electricianRate;
    this.contractRequest.endDate = this.datePipe.transform(this.contractForm.value.endDate, AppConstants.fullDateFormat);
    this.contractRequest.engineeringRate = this.contractForm.value.engineeringRate;
    this.contractRequest.escalationRate = this.contractForm.value.escalationRate;
    this.contractRequest.holidayRate = this.contractForm.value.holidayRate;
    this.contractRequest.materialRate1 = this.contractForm.value.materialRate1;
    this.contractRequest.materialRate2 = this.contractForm.value.materialRate2;
    this.contractRequest.mvTechnicianRate = this.contractForm.value.mvTechnicianRate;
    this.contractRequest.overtimeRate = this.contractForm.value.overtimeRate;
    this.contractRequest.startDate = this.datePipe.transform(this.contractForm.value.startDate, AppConstants.fullDateFormat);
    this.contractRequest.technicianRate = this.contractForm.value.technicianRate;
    this.contractRequest.materialThreshold = this.contractForm.value.materialThreshold;
    this.contractRequest.weekendRate = this.contractForm.value.weekendRate;
    this.contractRequest.contractLinkUrl = this.contractForm.value.contractLink;
    this.subscription.add(
      this.contractService.addUpdateContract(this.contractRequest).subscribe({
        next: res => {
          this.alertService.showSuccessToast(res.message);
          this.contractService.contractToOpen = res.entryid;
          this.loading = false;
          this.contractService.showRateChangeAlert = true;
          this.onBack();
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  onChangeStartDate() {
    this.contractsData.endDate = null;
  }

  onBack(): void {
    this.contractService.showRateChangeAlert = this.contractService.showRateChangeAlert ? this.contractService.showRateChangeAlert : false;
    if (window.history.length > 1) {
      this._location.back();
    } else {
      this.router.navigateByUrl(APP_ROUTES.CONTRACTS);
    }
  }
}
