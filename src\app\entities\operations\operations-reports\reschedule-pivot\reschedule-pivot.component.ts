import { Component, OnInit } from '@angular/core';
import { Subscription, forkJoin } from 'rxjs';
import { AppConstants } from '../../../../@shared/constants';
import { Dropdown } from '../../../../@shared/models/dropdown.model';
import { CommonService } from '../../../../@shared/services/common.service';
import { StorageService } from '../../../../@shared/services/storage.service';
import { CustomerService } from '../../../customer-management/customer.service';
import { PortfolioService } from '../../../portfolio-management/portfolio.service';
import { ReportService } from '../../../report/report.service';
import { SiteService } from '../../../site-management/site.service';
import { RegionMappingService } from '../../region-mapping/region-mapping.service';
import {
  AllReportDropdown,
  ForecastApiResponseModal,
  PivotApiRequestModal,
  PivotTableData,
  ReportType,
  ReschedulePivotResponseModal
} from '../operation-reports.model';
import { OperationsReportsService } from '../operations-reports.service';

@Component({
  selector: 'sfl-reschedule-pivot',
  templateUrl: './reschedule-pivot.component.html',
  styleUrls: ['./reschedule-pivot.component.scss']
})
export class ReschedulePivotComponent implements OnInit {
  loading = false;
  subscription: Subscription = new Subscription();
  customerList: Dropdown[];
  portfolioList: Dropdown[];
  siteList: Dropdown[];
  regionList: Dropdown[];
  subRegionList: Dropdown[];
  reportTypeData: ReportType[];
  filteredCustomersIds: number[] = [];
  filteredPortfolioIds: number[] = [];
  filteredSiteIds: number[] = [];
  filteredRegionIds: number[] = [];
  filteredSubregionIds: number[] = [];
  years = [];
  monthsList = [];
  quarterList = [
    { id: 1, name: 'Q1' },
    { id: 2, name: 'Q2' },
    { id: 3, name: 'Q3' },
    { id: 4, name: 'Q4' }
  ];
  months = [
    { id: 1, name: 'Jan' },
    { id: 2, name: 'Feb' },
    { id: 3, name: 'Mar' },
    { id: 4, name: 'Apr' },
    { id: 5, name: 'May' },
    { id: 6, name: 'Jun' },
    { id: 7, name: 'Jul' },
    { id: 8, name: 'Aug' },
    { id: 9, name: 'Sep' },
    { id: 10, name: 'Oct' },
    { id: 11, name: 'Nov' },
    { id: 12, name: 'Dec' }
  ];
  secondaryMetricList = [
    { id: 2, name: 'Half' },
    { id: 3, name: 'Quarter' },
    { id: 4, name: 'Month' },
    { id: 5, name: 'WeekDay' },
    { id: 6, name: 'Reschedule Main Category' },
    { id: 7, name: 'Assessment Type' }
  ];
  filterModel: PivotApiRequestModal = new PivotApiRequestModal();
  reschedulePivot: ReschedulePivotResponseModal;
  byCategoryChartData: ForecastApiResponseModal;
  pivotTableData: PivotTableData;
  chartLabel: string = '';
  isViewChart = false;
  siteLoading = false;
  customerLoading = false;
  portfolioLoading = false;
  sortBy = 'customerPortfolio';
  viewPage = 'ReschedulePivotReportPage';
  interval: ReturnType<typeof setInterval>;
  constructor(
    private readonly customerService: CustomerService,
    private readonly portfolioService: PortfolioService,
    private readonly siteService: SiteService,
    private readonly storageService: StorageService,
    private readonly reportService: ReportService,
    private readonly commonService: CommonService,
    private readonly operationsReportsService: OperationsReportsService,
    private readonly regionMappingService: RegionMappingService
  ) {}

  ngOnInit(): void {
    this.years = this.commonService.getYear(false, true, true);
    let filter = this.storageService.get(this.viewPage);
    this.filterModel = filter ? filter : this.filterModel;
    const localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      sharedFilter = this.storageService.get(AppConstants.SHARED_FILTER_KEY);

    this.filterModel.customerIds = (sharedFilter?.customerIds.length ? sharedFilter.customerIds : this.filterModel.customerIds) || [];
    this.filterModel.portfolioIds = sharedFilter?.portfolioIds.length
      ? sharedFilter?.portfolioIds
      : (localFilterData || defaultFilterData).portfolioIds || [];
    this.filterModel.siteIds = sharedFilter?.siteIds.length ? sharedFilter?.siteIds : (localFilterData || defaultFilterData).siteIds || [];
    this.filterModel.regionIds = (localFilterData || defaultFilterData).regionIds || [];
    this.filterModel.subregionIds = (localFilterData || defaultFilterData).subRegionIds || [];

    const apiArray = [this.customerService.getAllCustomer()];
    const tempObj = ['customerList'];
    apiArray.push(this.portfolioService.getAllReportPortfoliosByCustomerId(null));
    tempObj.push('portfolioList');
    apiArray.push(this.siteService.getAllReportSitesByPortfolioId(null));
    tempObj.push('siteList');
    apiArray.push(this.regionMappingService.getRegionDropdownList());
    tempObj.push('regionList');
    apiArray.push(this.regionMappingService.getSubRegionDropdownList());
    tempObj.push('subRegionList');
    this.getReportType();
    this.loading = true;

    if (filter) {
      if (this.filterModel.customerIds) {
        const data: AllReportDropdown = new AllReportDropdown();
        data.ids = this.filterModel.customerIds;

        apiArray.push(this.portfolioService.getAllReportPortfoliosByCustomerId(data));
        tempObj.push('portfolioList');
      }
      if (this.filterModel.portfolioIds) {
        const data: AllReportDropdown = new AllReportDropdown();
        data.customerIds = this.filterModel.customerIds;
        data.ids = this.filterModel.portfolioIds;
        apiArray.push(this.siteService.getAllReportSitesByPortfolioId(data));
        tempObj.push('siteList');
      }
      this.getAllLists(apiArray, tempObj);
    } else {
      this.getAllLists(apiArray, tempObj);
    }
  }

  getAllLists(apiArray: any, mapResultList: string[]) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          if (value === 'customerList' || value === 'portfolioList' || value === 'siteList') {
            this[value] = res[index].filter(item => item.isActive);
          } else {
            this[value] = res[index];
          }
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  onFilter(event: any, filterListType: string) {
    if (event.term) {
      this[filterListType] = event.items?.map(element => element.id);
    } else {
      this[filterListType] = [];
    }
  }

  selectUnselectAllCustomers(isSelect = false) {
    if (isSelect) {
      if (this.filteredCustomersIds.length) {
        if (!Array.isArray(this.filterModel.customerIds)) {
          this.filterModel.customerIds = [];
        }
        this.filterModel.customerIds = [
          ...new Set([...this.filterModel.customerIds, ...JSON.parse(JSON.stringify(this.filteredCustomersIds))])
        ];
      } else {
        this.filterModel.customerIds = this.customerList.map(customer => customer.id);
      }
    } else {
      if (this.filteredCustomersIds.length) {
        this.filterModel.customerIds = this.filterModel.customerIds.filter(x => !this.filteredCustomersIds.includes(x));
      } else {
        this.filterModel.customerIds = [];
      }
    }
    this.onCustomerSelectDeSelect();
  }

  selectUnselectAllPortfolio(isSelect = false) {
    if (isSelect) {
      if (this.filteredPortfolioIds.length) {
        if (!Array.isArray(this.filterModel.portfolioIds)) {
          this.filterModel.portfolioIds = [];
        }
        this.filterModel.portfolioIds = [
          ...new Set([...this.filterModel.portfolioIds, ...JSON.parse(JSON.stringify(this.filteredPortfolioIds))])
        ];
      } else {
        this.filterModel.portfolioIds = this.portfolioList.map(portfolio => portfolio.id);
      }
    } else {
      if (this.filteredPortfolioIds.length) {
        this.filterModel.portfolioIds = this.filterModel.portfolioIds.filter(x => !this.filteredPortfolioIds.includes(x));
      } else {
        this.filterModel.portfolioIds = [];
      }
    }
    this.onPortfolioSelectDeSelect();
  }

  selectUnselectAllSite(isSelect = false) {
    if (isSelect) {
      if (!this.filteredSiteIds.length) {
        this.filterModel.siteIds = this.siteList.map(site => site.id);
      } else {
        if (!Array.isArray(this.filterModel.siteIds)) {
          this.filterModel.siteIds = [];
        }
        this.filterModel.siteIds = [...new Set([...this.filterModel.siteIds, ...JSON.parse(JSON.stringify(this.filteredSiteIds))])];
      }
    } else {
      if (this.filteredSiteIds.length) {
        this.filterModel.siteIds = this.filterModel.siteIds.filter(x => !this.filteredSiteIds.includes(x));
      } else {
        this.filterModel.siteIds = [];
      }
    }
  }

  selectUnselectAllRegion(isSelect = false) {
    if (isSelect) {
      if (!this.filteredRegionIds.length) {
        this.filterModel.regionIds = this.regionList.map(site => site.id);
      } else {
        if (!Array.isArray(this.filterModel.regionIds)) {
          this.filterModel.regionIds = [];
        }
        this.filterModel.regionIds = [...new Set([...this.filterModel.regionIds, ...JSON.parse(JSON.stringify(this.filteredRegionIds))])];
      }
    } else {
      if (this.filteredRegionIds.length) {
        this.filterModel.regionIds = this.filterModel.regionIds.filter(x => !this.filteredRegionIds.includes(x));
      } else {
        this.filterModel.regionIds = [];
      }
    }
  }

  selectUnselectAllSubRegion(isSelect = false) {
    if (isSelect) {
      if (!this.filteredSubregionIds.length) {
        this.filterModel.subregionIds = this.subRegionList.map(site => site.id);
      } else {
        if (!Array.isArray(this.filterModel.subregionIds)) {
          this.filterModel.subregionIds = [];
        }
        this.filterModel.subregionIds = [
          ...new Set([...this.filterModel.subregionIds, ...JSON.parse(JSON.stringify(this.filteredSubregionIds))])
        ];
      }
    } else {
      if (this.filteredSubregionIds.length) {
        this.filterModel.subregionIds = this.filterModel.subregionIds.filter(x => !this.filteredSubregionIds.includes(x));
      } else {
        this.filterModel.subregionIds = [];
      }
    }
  }

  selectAndDeselectAll(array: Dropdown[], forValue: string, isSelect = false, addValue = 'id') {
    const tempData: number | string[] = [];
    if (isSelect) {
      for (const i of array) {
        tempData.push(i[addValue]);
      }
      this.filterModel[forValue] = tempData;
      if (forValue === 'quarter') {
        this.monthsList = [];
        this.getMonthsBasedOnQuarter(tempData);
      }
    }
  }

  onCustomerSelectDeSelect() {
    this.portfolioList = [];
    this.siteList = [];
    this.filterModel.portfolioIds = [];
    this.filterModel.siteIds = [];
    this.getAllPortfolioByCustomer();
  }

  getAllPortfolioByCustomer() {
    this.portfolioLoading = true;

    const data: AllReportDropdown = new AllReportDropdown();
    if (this.filterModel.customerIds) {
      data.ids = this.filterModel.customerIds;
    }

    this.subscription.add(
      this.portfolioService.getAllReportPortfoliosByCustomerId(data).subscribe({
        next: (res: Dropdown[]) => {
          this.portfolioList = res.filter(item => item.isActive);
          this.portfolioLoading = false;
        },
        error: e => {
          this.portfolioLoading = false;
        }
      })
    );
  }

  getAllSiteByPortfolio() {
    this.siteLoading = true;

    const data: AllReportDropdown = new AllReportDropdown();
    data.customerIds = this.filterModel.customerIds;
    data.ids = this.filterModel.portfolioIds;

    this.subscription.add(
      this.siteService.getAllReportSitesByPortfolioId(data).subscribe({
        next: (res: Dropdown[]) => {
          this.siteList = res.filter(item => item.isActive);
          this.siteLoading = false;
        },
        error: e => {
          this.siteLoading = false;
        }
      })
    );
  }

  clearSingleFilter(clearFor: string, removeFilter = true) {
    if (clearFor === 'customer') {
      this.filterModel.customerIds = [];
      this.onCustomerSelectDeSelect();
    } else if (clearFor === 'portfolio') {
      this.filterModel.portfolioIds = [];
      if (removeFilter) {
        this.onPortfolioSelectDeSelect();
      }
    } else if (clearFor === 'site') {
      this.filterModel.siteIds = [];
    } else if (clearFor === 'Assessment Type') {
      this.filterModel.assessmentTypes = [];
    } else if (clearFor === 'year') {
      this.filterModel.year = new Date().getFullYear();
    } else if (clearFor === 'quarter') {
      this.monthsList = [];
      this.filterModel.month = [];
      this.filterModel.quarter = [];
    } else if (clearFor === 'month') {
      this.filterModel.month = [];
    } else if (clearFor === 'subregionIds') {
      this.filterModel.subregionIds = [];
    } else if (clearFor === 'regionIds') {
      this.filterModel.regionIds = [];
    }
  }

  onPortfolioSelectDeSelect() {
    this.siteList = [];
    this.filterModel.siteIds = null;
    this.getAllSiteByPortfolio();
  }

  getReportType() {
    this.subscription.add(
      this.reportService.getReportTypeAll().subscribe({
        next: res => {
          const data = [];
          res.forEach((element: ReportType) => {
            if (
              element.abbreviation.toLowerCase() !== 'jha' &&
              element.abbreviation.toLowerCase() !== 'mnt' &&
              element.abbreviation.toLowerCase() !== 'sar' &&
              element.abbreviation.toLowerCase() !== 'pr'
            ) {
              data.push({ id: element.id, name: element.name, abbreviation: element.abbreviation });
            }
          });
          this.reportTypeData = data;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onFilterChange() {
    if (this.interval) this.clearRefreshInterval();
  }

  clearRefreshInterval() {
    clearInterval(this.interval);
    this.interval = null;
  }

  clearFilters() {
    this.filterModel = new PivotApiRequestModal();
    this.isViewChart = false;
    this.storageService.set(this.viewPage, null);
    if (this.interval) this.clearRefreshInterval();
  }

  getMonthsBasedOnQuarter(selectedQuarter: string[]) {
    this.filterModel.month = null;
    this.monthsList = [];
    selectedQuarter.forEach(selectedQuarter => {
      const quarterNumber = this.quarterList.find(q => q.name === selectedQuarter)?.id;

      if (quarterNumber !== undefined) {
        let startMonth = (quarterNumber - 1) * 3;
        let endMonth = startMonth + 2;
        this.monthsList.push(...this.months.slice(startMonth, endMonth + 1));
      }
    });
    this.monthsList.sort((a, b) => a.id - b.id);
  }

  viewData() {
    this.loading = true;
    const modal = {
      ...this.filterModel,
      primaryMetric: this.filterModel.primaryMetric ? Number(this.filterModel.primaryMetric) : null,
      isAllAssessmentTypeSelected: this.filterModel.assessmentTypes?.length
        ? this.reportTypeData.length === this.filterModel.assessmentTypes?.length
        : true,
      isAllSiteSelected: this.filterModel.siteIds?.length ? this.siteList.length === this.filterModel.siteIds?.length : true,
      isAllPortfolioSelected: this.filterModel.portfolioIds?.length
        ? this.portfolioList.length === this.filterModel.portfolioIds?.length
        : true,
      isAllCustomerSelected: this.filterModel.customerIds?.length
        ? this.customerList.length === this.filterModel.customerIds?.length
        : true,
      isAllRegionSelected: this.filterModel.regionIds?.length ? this.regionList.length === this.filterModel.regionIds?.length : true,
      isAllSubregionSelected: this.filterModel.subregionIds?.length
        ? this.subRegionList.length === this.filterModel.subregionIds?.length
        : true
    };
    this.subscription.add(
      this.operationsReportsService.viewReschedulePivotCharts(modal).subscribe({
        next: (res: ReschedulePivotResponseModal) => {
          this.reschedulePivot = res;
          this.byCategoryChartData = res.byCategoryChartData;
          this.chartLabel = res.chartLabel;
          this.pivotTableData = res.pivotTableData;
          this.loading = false;
          this.isViewChart = true;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  exportData() {
    this.loading = true;
    const modal = {
      ...this.filterModel,
      primaryMetric: this.filterModel.primaryMetric ? Number(this.filterModel.primaryMetric) : null,
      isAllAssessmentTypeSelected: this.filterModel.assessmentTypes?.length
        ? this.reportTypeData.length === this.filterModel.assessmentTypes?.length
        : true,
      isAllSiteSelected: this.filterModel.siteIds?.length ? this.siteList.length === this.filterModel.siteIds?.length : true,
      isAllPortfolioSelected: this.filterModel.portfolioIds?.length
        ? this.portfolioList.length === this.filterModel.portfolioIds?.length
        : true,
      isAllCustomerSelected: this.filterModel.customerIds?.length
        ? this.customerList.length === this.filterModel.customerIds?.length
        : true,
      isAllRegionSelected: this.filterModel.regionIds?.length ? this.regionList.length === this.filterModel.regionIds?.length : true,
      isAllSubregionSelected: this.filterModel.subregionIds?.length
        ? this.subRegionList.length === this.filterModel.subregionIds?.length
        : true
    };
    this.subscription.add(
      this.operationsReportsService.exportReschedulePivotPDF(modal).subscribe({
        next: res => {
          if (res) {
            const link = this.commonService.createObject(res, 'application/pdf');
            link.download = 'Reschedule Pivot';
            link.click();
            this.loading = false;
          } else {
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }
}
