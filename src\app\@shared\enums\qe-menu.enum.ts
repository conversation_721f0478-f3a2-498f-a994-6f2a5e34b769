export enum QE_MENU_MODULE_ENUM {
  SITE_INFO = 1, // Site Info
  PREVENTIVE_MAINTENANCE = 2, // Preventive Maintenance
  CORRECTIVE_MAINTENANCE = 3, // Corrective Maintenance
  AVAILABILITY = 4, // Availability
  PERFORMANCE = 5, // Performance
  SAFETY = 6, // Safety
  OPERATIONS = 7, // Operations
  ADMIN = 8, // Admin
  OTHERS = 9, // Placeholder for any future modules

  SI_DASHBOARD = 10, // Site Info Dashboard
  SI_CUSTOMERS = 11, // Site Info Customers
  SI_PORTFOLIOS = 12, // Site Info Portfolios
  SI_SITES = 13, // Site Info Sites
  SI_DEVICES = 14, // Site Info Devices
  SI_EQUIPMENT = 15, // Site Info Equipment

  PM_DASHBOARD = 16, // Preventive Maintenance Dashboard
  PM_SCOPE = 17, // Preventive Maintenance Scope
  PM_WORK_ORDERS = 18, // Preventive Maintenance Work Orders
  PM_REPORTS = 19, // Preventive Maintenance Reports
  PM_SITE_AUDIT = 20, // Preventive Maintenance Site Audit
  PM_NON_CONFORMANCE = 21, // Preventive Maintenance Non-Conformance

  CM_DASHBOARD = 22, // Corrective Maintenance Dashboard
  CM_ALL_TICKETS = 23, // Corrective Maintenance All Tickets
  CM_TICKET_AUDIT_REPORT = 24, // Corrective Maintenance Ticket Audit Report
  CM_EXCLUSION_REPORT = 25, // Corrective Maintenance Exclusion Report
  CM_BILLING_REPORT = 26, // Corrective Maintenance Billing Report
  CM_TRUCK_ROLL_REPORT = 27, // Corrective Maintenance Truck Roll Report
  CM_MAP_REPORT = 28, // Corrective Maintenance Map Report
  CM_RMA_REPORT = 29, // Corrective Maintenance RMA Report

  AVB_DASHBOARD = 30, // Availability Dashboard
  AVB_REPORTS = 31, // Availability Reports
  AVB_DATA_TABLE = 32, // Availability Data Table
  AVB_EXCLUSIONS = 33, // Availability Exclusions

  PER_DASHBOARD = 34, // Performance Dashboard
  PER_POWER_CHARTS = 35, // Performance Power Charts
  PER_POWER_CARDS = 36, // Performance Power Cards
  PER_REPORTS = 37, // Performance Reports
  PER_DATA_TABLE = 38, // Performance Data Table
  PER_ALERTS = 39, // Performance Alerts

  SF_JHA = 40, // Safety JHA
  SF_SITE_CHECK_IN = 41, // Safety Site Check In
  SF_SITE_AUDIT_JHA = 42, // Safety Site Audit JHA
  SF_SETTINGS = 43, // Safety Settings
  SF_SET_GENERAL_INFO = 44, // Safety Set General Info
  SF_SET_WORK_TYPE = 45, // Safety Set Work Type
  SF_SET_WORK_STEP = 46, // Safety Set Work Step
  SF_SET_HAZARD = 47, // Safety Set Hazard
  SF_SET_BARRIER = 48, // Safety Set Barrier
  SF_SET_JHA = 49, // Safety Set JHA
  SF_SET_LOTO = 50, // Safety Set LOTO

  OP_REPORTS = 51, // Operations Reports
  OP_REGION_MAPPING = 52, // Operations Region Mapping
  OP_SERVICES = 53, // Operations Services
  OP_CONTRACTS = 54, // Operations Contracts
  OP_CUSTOM_FORMS = 55, // Operations Custom Forms

  AD_USERS = 56, // Admin Users
  AD_DATA_SOURCES_MAPPING = 57, // Admin Data Sources Mapping
  AD_API_ERROR_LOG = 58, // Admin API Error Log
  AD_REPORT_SCHEDULER = 59, // Admin Report Scheduler
  AD_CUSTOMER_API_GATEWAY = 60, // Admin Customer API Gateway
  AD_API_GATEWAY_DASHBOARD = 61, // Admin API Gateway Dashboard
  AD_RE_FETCH_SCHEDULER = 62, // Admin Re-Fetch Scheduler
  AD_EMAIL_LOG = 63, // Admin Email Log
  AD_ANALYTICS = 64, // Admin Analytics

  SITE_CHECK_IN = 65, // Site Check In
  USER_PROFILE = 66, // User Profile,
  USER_CHANGE_PASSWORD = 67, // User Change Password

  // Custom Menu
  SI_CUSTOM_SITES_SITE_DETAILS = 100001, // Site Info (Custom) -> Sites -> Site Details
  SI_CUSTOM_SITES_PERFORMANCE_INFORMATION = 100002, // Site Info (Custom) -> Sites -> Performance Information
  SI_CUSTOM_SITES_DEVICE_LIST = 100003 // Site Info (Custom) -> Sites -> Device List
}

export const QE_MENU_MODULE_NAME_ENUM = {
  // Site Info
  [QE_MENU_MODULE_ENUM.SITE_INFO]: 'Site Info',
  [QE_MENU_MODULE_ENUM.SI_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.SI_CUSTOMERS]: 'Customers',
  [QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]: 'Portfolios',
  [QE_MENU_MODULE_ENUM.SI_SITES]: 'Sites',
  [QE_MENU_MODULE_ENUM.SI_DEVICES]: 'Devices',
  [QE_MENU_MODULE_ENUM.SI_EQUIPMENT]: 'Equipment',

  // Preventive Maintenance
  [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: 'PM',
  [QE_MENU_MODULE_ENUM.PM_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.PM_SCOPE]: 'Scope',
  [QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]: 'Work Orders',
  [QE_MENU_MODULE_ENUM.PM_REPORTS]: 'Reports',
  [QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]: 'Site Audit',
  [QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]: 'Non-Conformance',

  // Corrective Maintenance
  [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: 'CM',
  [QE_MENU_MODULE_ENUM.CM_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]: 'All Tickets',
  [QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]: 'Ticket Audit Report',
  [QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]: 'Exclusion Report',
  [QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]: 'Billing Report',
  [QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]: 'Truck Roll Report',
  [QE_MENU_MODULE_ENUM.CM_MAP_REPORT]: 'Map Report',
  [QE_MENU_MODULE_ENUM.CM_RMA_REPORT]: 'RMA Report',

  // Availability
  [QE_MENU_MODULE_ENUM.AVAILABILITY]: 'Availability',
  [QE_MENU_MODULE_ENUM.AVB_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.AVB_REPORTS]: 'Reports',
  [QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]: 'Data Table',
  [QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]: 'Exclusions',

  // Performance
  [QE_MENU_MODULE_ENUM.PERFORMANCE]: 'Performance',
  [QE_MENU_MODULE_ENUM.PER_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]: 'Power Charts',
  [QE_MENU_MODULE_ENUM.PER_POWER_CARDS]: 'Power Cards',
  [QE_MENU_MODULE_ENUM.PER_REPORTS]: 'Reports',
  [QE_MENU_MODULE_ENUM.PER_DATA_TABLE]: 'Data Table',
  [QE_MENU_MODULE_ENUM.PER_ALERTS]: 'Alerts',

  // Safety
  [QE_MENU_MODULE_ENUM.SAFETY]: 'Safety',
  [QE_MENU_MODULE_ENUM.SF_JHA]: 'JHA',
  [QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]: 'Site Check-In',
  [QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]: 'Site Audit-JHA',
  // Safety Settings
  [QE_MENU_MODULE_ENUM.SF_SETTINGS]: 'Settings',
  [QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]: 'General Info',
  [QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]: 'Work Type',
  [QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]: 'Work Step',
  [QE_MENU_MODULE_ENUM.SF_SET_HAZARD]: 'Hazard',
  [QE_MENU_MODULE_ENUM.SF_SET_BARRIER]: 'Barrier',
  [QE_MENU_MODULE_ENUM.SF_SET_JHA]: 'JHA',
  [QE_MENU_MODULE_ENUM.SF_SET_LOTO]: 'LOTO',

  // Operations
  [QE_MENU_MODULE_ENUM.OPERATIONS]: 'Operations',
  [QE_MENU_MODULE_ENUM.OP_REPORTS]: 'Reports',
  [QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]: 'Region Mapping',
  [QE_MENU_MODULE_ENUM.OP_SERVICES]: 'Services',
  [QE_MENU_MODULE_ENUM.OP_CONTRACTS]: 'Contracts',
  [QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]: 'Custom Forms',

  // Admin
  [QE_MENU_MODULE_ENUM.ADMIN]: 'Admin',
  [QE_MENU_MODULE_ENUM.AD_USERS]: 'Users',
  [QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]: 'Data Sources Mapping',
  [QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]: 'API Error Log',
  [QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]: 'Report Scheduler',
  [QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]: 'Customer API Gateway',
  [QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]: 'API Gateway Dashboard',
  [QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]: 'Re-Fetch Scheduler',
  [QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]: 'Email Log',
  [QE_MENU_MODULE_ENUM.AD_ANALYTICS]: 'Analytics',

  // Placeholder for any future modules
  [QE_MENU_MODULE_ENUM.OTHERS]: 'Others',
  [QE_MENU_MODULE_ENUM.SITE_CHECK_IN]: 'Site Check-In',
  [QE_MENU_MODULE_ENUM.USER_PROFILE]: 'User Profile',
  [QE_MENU_MODULE_ENUM.USER_CHANGE_PASSWORD]: 'Change Password',

  // Custom Menu
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS]: 'Site Details',
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION]: 'Performance Information',
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST]: 'Device List'
};

export const QE_MENU_MODULE_NAME_ENUM_LIST = {
  [QE_MENU_MODULE_ENUM.SITE_INFO]: {
    [QE_MENU_MODULE_ENUM.SI_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.SI_CUSTOMERS]: 'Customers',
    [QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]: 'Portfolios',
    [QE_MENU_MODULE_ENUM.SI_SITES]: 'Sites',
    [QE_MENU_MODULE_ENUM.SI_DEVICES]: 'Devices',
    [QE_MENU_MODULE_ENUM.SI_EQUIPMENT]: 'Equipment'
  },
  [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: {
    [QE_MENU_MODULE_ENUM.PM_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.PM_SCOPE]: 'Scope',
    [QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]: 'Work Orders',
    [QE_MENU_MODULE_ENUM.PM_REPORTS]: 'Reports',
    [QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]: 'Site Audit',
    [QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]: 'Non-Conformance'
  },
  [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: {
    [QE_MENU_MODULE_ENUM.CM_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]: 'All Tickets',
    [QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]: 'Ticket Audit Report',
    [QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]: 'Exclusion Report',
    [QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]: 'Billing Report',
    [QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]: 'Truck Roll Report',
    [QE_MENU_MODULE_ENUM.CM_MAP_REPORT]: 'Map Report',
    [QE_MENU_MODULE_ENUM.CM_RMA_REPORT]: 'RMA Report'
  },
  [QE_MENU_MODULE_ENUM.AVAILABILITY]: {
    [QE_MENU_MODULE_ENUM.AVB_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.AVB_REPORTS]: 'Reports',
    [QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]: 'Data Table',
    [QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]: 'Exclusions'
  },
  [QE_MENU_MODULE_ENUM.PERFORMANCE]: {
    [QE_MENU_MODULE_ENUM.PER_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]: 'Power Charts',
    [QE_MENU_MODULE_ENUM.PER_POWER_CARDS]: 'Power Cards',
    [QE_MENU_MODULE_ENUM.PER_REPORTS]: 'Reports',
    [QE_MENU_MODULE_ENUM.PER_DATA_TABLE]: 'Data Table',
    [QE_MENU_MODULE_ENUM.PER_ALERTS]: 'Alerts'
  },
  [QE_MENU_MODULE_ENUM.SAFETY]: {
    [QE_MENU_MODULE_ENUM.SF_JHA]: 'JHA',
    [QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]: 'Site Check-In',
    [QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]: 'Site Audit-JHA',
    [QE_MENU_MODULE_ENUM.SF_SETTINGS]: 'Settings'
  },
  [QE_MENU_MODULE_ENUM.SF_SETTINGS]: {
    [QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]: 'General Info',
    [QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]: 'Work Type',
    [QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]: 'Work Step',
    [QE_MENU_MODULE_ENUM.SF_SET_HAZARD]: 'Hazard',
    [QE_MENU_MODULE_ENUM.SF_SET_BARRIER]: 'Barrier',
    [QE_MENU_MODULE_ENUM.SF_SET_JHA]: 'JHA',
    [QE_MENU_MODULE_ENUM.SF_SET_LOTO]: 'LOTO'
  },
  [QE_MENU_MODULE_ENUM.OPERATIONS]: {
    [QE_MENU_MODULE_ENUM.OP_REPORTS]: 'Reports',
    [QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]: 'Region Mapping',
    [QE_MENU_MODULE_ENUM.OP_SERVICES]: 'Services',
    [QE_MENU_MODULE_ENUM.OP_CONTRACTS]: 'Contracts',
    [QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]: 'Custom Forms'
  },
  [QE_MENU_MODULE_ENUM.ADMIN]: {
    [QE_MENU_MODULE_ENUM.AD_USERS]: 'Users',
    [QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]: 'Data Sources Mapping',
    [QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]: 'API Error Log',
    [QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]: 'Report Scheduler',
    [QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]: 'Customer API Gateway',
    [QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]: 'API Gateway Dashboard',
    [QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]: 'Re-Fetch Scheduler',
    [QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]: 'Email Log',
    [QE_MENU_MODULE_ENUM.AD_ANALYTICS]: 'Analytics'
  },
  [QE_MENU_MODULE_ENUM.OTHERS]: {
    [QE_MENU_MODULE_ENUM.SITE_CHECK_IN]: 'Site Check-In',
    [QE_MENU_MODULE_ENUM.USER_PROFILE]: 'User Profile',
    [QE_MENU_MODULE_ENUM.USER_CHANGE_PASSWORD]: 'Change Password'
  }
};

export type FrontEndMenuOrderType = {
  [key: string]: number | FrontEndMenuOrderType;
};

export const QE_MENU_MODULE_ORDER_LIST: FrontEndMenuOrderType = {
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: 1,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: 2,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: 3,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: 4,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: 5,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: 6
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: 7,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: 8,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: 9,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: 10,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: 11,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: 12
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: 13,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: 14,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: 15,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: 16,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: 17,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: 18,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: 19,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: 20
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: 21,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: 22,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: 23,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: 24
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: 25,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: 26,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: 27,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: 28,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: 29,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: 30
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: 31,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: 32,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: 33,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: {
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: 34,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: 35,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: 36,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: 37,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: 38,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_JHA]]: 39,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: 40
    }
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: 41,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: 42,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: 43,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: 44,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: 45
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: 46,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: 47,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: 48,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: 49,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: 50,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: 51,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: 52,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: 53,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: 54
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OTHERS]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SITE_CHECK_IN]]: 55,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.USER_PROFILE]]: 56,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.USER_CHANGE_PASSWORD]]: 57
  }
};
