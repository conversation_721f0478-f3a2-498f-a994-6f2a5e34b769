import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, CanDeactivate, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { QEAnalyticsGatheringService } from './qe-analytics-gathering.service';
import { QE_MENU_MODULE_ENUM } from '../../../@shared/enums/qe-menu.enum';
import { QE_MENU_INTERACTION_EVENT_TYPE_ENUM } from '../models/qe-analytics.enum';
import * as uuid from 'uuid';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsGatheringGuard implements CanActivate, CanDeactivate<any> {
  private activatedRoute: ActivatedRouteSnapshot = null;
  private randomUniqueKey: string = uuid.v4();

  constructor(private readonly qeAnalyticsGatheringService: QEAnalyticsGatheringService) {
    window.addEventListener('beforeunload', event => this.onBeforeUnload(event));
  }

  private onBeforeUnload(event): void {
    if (this.activatedRoute) {
      this.onCaptureQEAnalyticsItemEmit(this.activatedRoute, QE_MENU_INTERACTION_EVENT_TYPE_ENUM.QE_MENU_VISIT_START_END);
      this.activatedRoute = null;
    }
  }

  onCaptureQEAnalyticsItemEmit(route: ActivatedRouteSnapshot, qeMenuInteractionEventType: QE_MENU_INTERACTION_EVENT_TYPE_ENUM): void {
    const qeAnalyticsMenuItemEnum = route.data['qeAnalyticsMenuItem'] || QE_MENU_MODULE_ENUM.PM_DASHBOARD;
    const qeAnalyticsParentItemEnum = route.data['qeAnalyticsParentItem'] || QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE;
    this.qeAnalyticsGatheringService.captureQEAnalyticsItemEnum(
      qeAnalyticsMenuItemEnum,
      qeAnalyticsParentItemEnum,
      qeMenuInteractionEventType,
      this.randomUniqueKey
    );
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    this.activatedRoute = route;
    this.randomUniqueKey = uuid.v4();
    this.onCaptureQEAnalyticsItemEmit(route, QE_MENU_INTERACTION_EVENT_TYPE_ENUM.QE_MENU_CLICK);
    return true;
  }

  canDeactivate(
    component: any,
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    this.onCaptureQEAnalyticsItemEmit(route, QE_MENU_INTERACTION_EVENT_TYPE_ENUM.QE_MENU_VISIT_START_END);
    return true;
  }

  ngOnDestroy(): void {
    window.removeEventListener('beforeunload', event => {});
  }
}
