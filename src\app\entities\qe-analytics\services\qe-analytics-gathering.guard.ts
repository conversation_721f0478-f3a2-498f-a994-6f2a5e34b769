import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { QEAnalyticsGatheringService } from './qe-analytics-gathering.service';
import { QE_MENU_MODULE_ENUM } from '../../../@shared/enums/qe-menu.enum';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsGatheringGuard implements CanActivate {
  constructor(private readonly qeAnalyticsGatheringService: QEAnalyticsGatheringService) {}
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const qeAnalyticsMenuItemEnum = route.data['qeAnalyticsMenuItem'] || QE_MENU_MODULE_ENUM.PM_DASHBOARD;
    const qeAnalyticsParentItemEnum = route.data['qeAnalyticsParentItem'] || QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE;
    this.qeAnalyticsGatheringService.captureQEAnalyticsItemEnum(qeAnalyticsMenuItemEnum, qeAnalyticsParentItemEnum);
    return true;
  }
}
