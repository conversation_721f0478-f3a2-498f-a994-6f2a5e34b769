import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { debounceTime, Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AlertService } from '../../@shared/services';
import { NcActionAddEditComponent } from './nc-action-add-edit/nc-action-add-edit.component';
import { NcComponentAddEditComponent } from './nc-component-add-edit/nc-component-add-edit.component';
import { NcIssueAddEditComponent } from './nc-issue-add-edit/nc-issue-add-edit.component';
import {
  ACCORDION_ITEMS,
  ActionFilterModel,
  ADD_EDIT_COPY_MODE,
  CmpFilterModel,
  IssueFilterModel,
  NcComponentListing,
  PmActionRecommendationListResponse,
  PmIssueActionNCItems,
  PmIssueObservationListResponse
} from './pm-non-conformance.model';
import { PmNonConformanceService } from './pm-non-conformance.service';

@Component({
  selector: 'app-pm-non-conformance',
  templateUrl: './pm-non-conformance.component.html',
  styleUrls: ['./pm-non-conformance.component.scss']
})
export class PmNonConformanceComponent implements OnInit {
  issueForm: FormGroup;
  actionForm: FormGroup;
  ncComponentList: NcComponentListing[] = [];
  issueObservationList: PmIssueObservationListResponse[] = [];
  ncComponentSearchText = '';
  issueSearchText = '';
  actionSearchText = '';
  actionRecommendationList: PmActionRecommendationListResponse[] = [];
  issueModalRef: BsModalRef;
  actionModalRef: BsModalRef;
  modalRef: BsModalRef;
  loading = false;
  subscription: Subscription = new Subscription();
  ACCORDION_ITEMS = ACCORDION_ITEMS;
  ADD_EDIT_COPY_MODE = ADD_EDIT_COPY_MODE;
  componentFilterModel: CmpFilterModel = new CmpFilterModel();
  issueFilterModel: IssueFilterModel = new IssueFilterModel();
  actionsFilterModel: ActionFilterModel = new ActionFilterModel();
  sortCmpOptionList = {
    name: 'desc'
  };
  sortIssueOptionList = {
    componentStr: 'desc',
    issueObservation: 'desc'
  };
  sortActionOptionList = {
    componentStr: 'desc',
    issueObservationStr: 'desc',
    actionRecommendation: 'desc'
  };

  constructor(
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService,
    private readonly pmNonConformanceService: PmNonConformanceService
  ) {}

  ngOnInit(): void {
    this.getIssueObservationList();
    this.getActionRecommendationList();
    this.getNcComponentList();

    this.subscription.add(
      this.pmNonConformanceService.searchNcComponentSubject.pipe(debounceTime(1000)).subscribe(ncComponentSearchText => {
        const reqParams = {
          ...this.issueFilterModel,
          searchBy: ncComponentSearchText
        };
        this.getNcComponentList(reqParams, false);
      })
    );
    this.subscription.add(
      this.pmNonConformanceService.searchIssueSubject.pipe(debounceTime(1000)).subscribe(issueSearchText => {
        const reqParams = {
          ...this.issueFilterModel,
          searchBy: issueSearchText
        };
        this.getIssueObservationList(reqParams, false);
      })
    );
    this.subscription.add(
      this.pmNonConformanceService.searchActionSubject.pipe(debounceTime(1000)).subscribe(actionSearchText => {
        const reqParams = {
          ...this.issueFilterModel,
          searchBy: actionSearchText
        };
        this.getActionRecommendationList(reqParams, false);
      })
    );
  }

  getActionRecommendationList(params = null, isSearchClear = true) {
    const reqParam = params ? params : new ActionFilterModel();
    this.loading = true;
    this.subscription.add(
      this.pmNonConformanceService.getNcActionRecommendations(reqParam).subscribe({
        next: (res: PmActionRecommendationListResponse[]) => {
          this.actionRecommendationList = res;
          this.actionSearchText = isSearchClear ? '' : this.actionSearchText;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getNcComponentList(params = null, isSearchClear = true) {
    const reqParam = params ? params : new CmpFilterModel();
    this.loading = true;
    this.subscription.add(
      this.pmNonConformanceService.getNcComponentsList(reqParam).subscribe({
        next: (res: NcComponentListing[]) => {
          this.ncComponentList = res.map(item => {
            return {
              ...item,
              componentName: item.name
            };
          });
          this.ncComponentSearchText = isSearchClear ? '' : this.ncComponentSearchText;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getIssueObservationList(params = null, isSearchClear = true) {
    const reqParam = params ? params : new IssueFilterModel();
    this.loading = true;
    this.subscription.add(
      this.pmNonConformanceService.getNcIssueObservationList(reqParam).subscribe({
        next: (res: PmIssueObservationListResponse[]) => {
          this.issueObservationList = res;
          this.issueSearchText = isSearchClear ? '' : this.issueSearchText;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onNcComponentSearchChange(searchText: string) {
    this.subscription.add(this.pmNonConformanceService.searchNcComponentSubject.next(searchText));
  }

  onIssueSearchChange(searchText: string) {
    this.subscription.add(this.pmNonConformanceService.searchIssueSubject.next(searchText));
  }

  onActionSearchChange(searchText: string) {
    this.subscription.add(this.pmNonConformanceService.searchActionSubject.next(searchText));
  }

  openNcComponentPanel(ncComponentItem, mode) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        ncComponentItem: ncComponentItem ? JSON.parse(JSON.stringify(ncComponentItem)) : new PmIssueActionNCItems(),
        ncComponentMode: mode
      }
    };
    this.issueModalRef = this.modalService.show(NcComponentAddEditComponent, ngModalOptions);
    this.issueModalRef.content.event.subscribe(res => {
      if (res) {
        this.loading = false;

        if (mode === ADD_EDIT_COPY_MODE.EDIT) {
          const reqParams = {
            ...this.issueFilterModel,
            searchBy: this.ncComponentSearchText
          };
          this.getNcComponentList(reqParams, false);
          this.getIssueObservationList();
          this.getActionRecommendationList();
        } else {
          this.getNcComponentList();
        }
      }
    });
  }

  openIssuePanel(issueItem, mode) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        issueItem: issueItem ? JSON.parse(JSON.stringify(issueItem)) : new PmIssueActionNCItems(),
        issueMode: mode
      }
    };
    this.issueModalRef = this.modalService.show(NcIssueAddEditComponent, ngModalOptions);
    this.issueModalRef.content.event.subscribe(res => {
      if (res) {
        this.loading = false;

        if (mode === ADD_EDIT_COPY_MODE.EDIT) {
          const reqParams = {
            ...this.issueFilterModel,
            searchBy: this.issueSearchText
          };
          this.getIssueObservationList(reqParams, false);
          this.getActionRecommendationList();
        } else {
          this.getIssueObservationList();
        }
      }
    });
  }

  openActionPanel(actionItem, mode) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        actionItem: actionItem ? JSON.parse(JSON.stringify(actionItem)) : new PmIssueActionNCItems(),
        actionMode: mode,
        issueObservationList: mode === 'EDIT' ? this.issueObservationList : this.issueObservationList.filter(item => item.isActive),
        isIssueSearched: this.issueSearchText ? true : false
      }
    };
    this.actionModalRef = this.modalService.show(NcActionAddEditComponent, ngModalOptions);
    this.actionModalRef.content.event.subscribe(res => {
      if (res) {
        this.loading = false;
        if (mode === ADD_EDIT_COPY_MODE.EDIT) {
          const reqParams = {
            ...this.issueFilterModel,
            searchBy: this.actionSearchText
          };
          this.getActionRecommendationList(reqParams, false);
        } else {
          this.getActionRecommendationList();
        }
      }
    });
  }

  closeIssuePanel(): void {
    const issueSidebar = document.querySelector('[tag="issue-sidebar"]');
    if (issueSidebar) {
      issueSidebar.setAttribute('state', 'collapsed');
    }
  }

  onDelete(id, type) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: this.getWarningMassageBasedOnType(type)
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (type === ACCORDION_ITEMS.ISSUE) {
          this.loading = true;
          this.subscription.add(
            this.pmNonConformanceService.deleteNcIssueObservationById(id).subscribe({
              next: (res: any) => {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                this.getIssueObservationList();
                this.getActionRecommendationList();
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else if (type === ACCORDION_ITEMS.ACTION) {
          this.loading = true;
          this.subscription.add(
            this.pmNonConformanceService.deleteNcActionRecommendationById(id).subscribe({
              next: (res: any) => {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                this.getActionRecommendationList();
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.loading = true;
          this.subscription.add(
            this.pmNonConformanceService.deleteNcComponentById(id).subscribe({
              next: (res: any) => {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                this.getNcComponentList();
                this.getIssueObservationList();
                this.getActionRecommendationList();
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      }
    });
  }

  getWarningMassageBasedOnType(type, isActive = true, isDeleteMassage = true) {
    if (isDeleteMassage) {
      if (type === ACCORDION_ITEMS.ISSUE) {
        return `All related actions will also be deleted. Are you sure you want to delete this ${type}?`;
      } else if (type === ACCORDION_ITEMS.ACTION) {
        return `Are you sure you want to delete this ${type}?`;
      } else {
        return `All related issues and actions will also be deleted. Are you sure you want to delete this ${type}?`;
      }
    } else {
      const action = isActive ? 'deactivate' : 'activate';
      if (type === ACCORDION_ITEMS.ISSUE) {
        const cascadeMessage = isActive ? `All related actions will also be deactivated. ` : '';
        return `${cascadeMessage}Are you sure you want to ${action} this ${type}?`;
      } else if (type === ACCORDION_ITEMS.ACTION) {
        return `Are you sure you want to ${action} this ${type}?`;
      } else {
        const cascadeMessage = isActive ? `All related issues and actions will also be deactivated. ` : '';
        return `${cascadeMessage}Are you sure you want to ${action} this ${type}?`;
      }
    }
  }

  onActiveInactive(event, item, type) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: this.getWarningMassageBasedOnType(type, item.isActive, false)
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        if (type === ACCORDION_ITEMS.ISSUE) {
          this.loading = true;
          this.subscription.add(
            this.pmNonConformanceService.activeInactiveNcIssueObservationById(item.id).subscribe({
              next: (res: any) => {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                const reqParams = {
                  ...this.issueFilterModel,
                  searchBy: this.issueSearchText
                };
                this.getIssueObservationList(reqParams, false);
                this.getActionRecommendationList();
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else if (type === ACCORDION_ITEMS.ACTION) {
          this.loading = true;
          this.subscription.add(
            this.pmNonConformanceService.activeInactiveNcActionRecommendationById(item.id).subscribe({
              next: (res: any) => {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                const reqParams = {
                  ...this.issueFilterModel,
                  searchBy: this.actionSearchText
                };
                this.getActionRecommendationList(reqParams, false);
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        } else {
          this.loading = true;
          this.subscription.add(
            this.pmNonConformanceService.activeInactiveNcComponentById(item.evId).subscribe({
              next: (res: any) => {
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
                const reqCmpParams = {
                  ...this.issueFilterModel,
                  searchBy: this.ncComponentSearchText
                };
                this.getNcComponentList(reqCmpParams, false);
                this.getIssueObservationList();
                this.getActionRecommendationList();
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      } else {
        setTimeout(() => {
          item.isActive = !event;
        });
      }
    });
  }

  sortComponent(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortCmpOptionList[sortBy] = changeSort;
    this.componentFilterModel.sortBy = sortBy;
    this.componentFilterModel.direction = changeSort;
    const reqParams = {
      ...this.componentFilterModel,
      direction: changeSort,
      page: 0,
      itemsCount: 0,
      sortBy: sortBy,
      searchBy: this.ncComponentSearchText
    };
    this.getNcComponentList(reqParams, false);
  }
  sortActions(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortActionOptionList[sortBy] = changeSort;
    this.actionsFilterModel.sortBy = sortBy;
    this.actionsFilterModel.direction = changeSort;
    const reqParams = {
      ...this.actionsFilterModel,
      direction: changeSort,
      page: 0,
      itemsCount: 0,
      sortBy: sortBy,
      searchBy: this.actionSearchText
    };
    this.getActionRecommendationList(reqParams, false);
  }

  sortIssue(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortIssueOptionList[sortBy] = changeSort;
    this.issueFilterModel.sortBy = sortBy;
    this.issueFilterModel.direction = changeSort;
    const reqParams = {
      ...this.issueFilterModel,
      direction: changeSort,
      page: 0,
      itemsCount: 0,
      sortBy: sortBy,
      searchBy: this.issueSearchText
    };
    this.getIssueObservationList(reqParams, false);
  }
}
