export class OperationServicesList {
  public id: number = 0;
  public class: string;
  public category: string;
  public mfgSpecific: boolean = false;
  public mfgCertTraining: boolean = false;
  public prevailingWage: boolean = false;
  public service: string;
  public rateTypeId: string;
}
export class ServicesList {
  public total: number;
  public servicesListing: OperationServicesList[];
}

export class ServicesRateDropDownList {
  public id: number | null = null;
  public name: string = '';
  public isActive: boolean;
  public abbreviation: string = '';
  public siteNumber: string = '';
  public customerId: number | null = null;
  public portfolioId: number | null = null;
  public isAutomationSite: boolean;
  public isArchive: boolean;
}

export class ServiceProdBreak {
  public month: number;
  public expIsolation: number;
  public expProduction: number;
  public isStartYear: boolean;
  public yearDifference: number;
  public yearDifferenceColor: string;
}
export class ServiceAuditHistory {
  public action: string;
  public auditId: number;
  public auditLogDetails: ServiceProdBreak[];
  public customerId: number;
  public entityId: number;
  public logDate: string;
  public portfolioId: number;
  public siteId: number;
  public userName: number;
}
