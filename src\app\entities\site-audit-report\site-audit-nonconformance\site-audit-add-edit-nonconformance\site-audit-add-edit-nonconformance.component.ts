import { CdkDrag, CdkDragEnter, CdkDragMove, CdkDropList, CdkDropListGroup, moveItemInArray } from '@angular/cdk/drag-drop';
import { ViewportRuler } from '@angular/cdk/scrolling';
import { Component, EventEmitter, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription, forkJoin } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { ImageCropperComponent } from '../../../../@shared/components/image-cropper/image-cropper.component';
import { ErrorMessages } from '../../../../@shared/constants';
import {
  AllReportModel,
  MasterGeneralImagesModel,
  MasterNonConformanceDto,
  MasterReportModel
} from '../../../../@shared/models/report.model';
import { AlertService } from '../../../../@shared/services';
import { ModelComponent } from '../../../report/model/model.component';
import { ReportService } from '../../../report/report.service';

@Component({
  selector: 'sfl-site-audit-add-edit-nonconformance',
  templateUrl: './site-audit-add-edit-nonconformance.component.html',
  styleUrls: ['./site-audit-add-edit-nonconformance.component.scss']
})
export class SiteAuditAddEditNonconformanceComponent implements OnInit {
  @ViewChild(CdkDropListGroup) listGroup: CdkDropListGroup<CdkDropList>;
  @ViewChild(CdkDropList) placeholder: CdkDropList;

  public onClose: Subject<boolean>;
  @Input() editNC;
  @Input() selectedisFinal: boolean;
  @Input() isAdd: boolean;
  @Input() reportGuid: string;
  @Input() componentList;
  public event: EventEmitter<any> = new EventEmitter();
  modalRef: BsModalRef;
  loading = false;
  subscription: Subscription = new Subscription();
  generalImagesGroupId = '4';
  ncGuid: string;
  isFinal: boolean;
  reportMasterList: AllReportModel;
  nonconformanceList: MasterReportModel[];
  selectedItem;
  dir;
  isEdit = false;
  selectedComponent;
  @Input() viewdeletetedbutton;
  filesList: any = [];
  pendingImages: any[] = []; // Store images for new NC before creation

  public target: CdkDropList;
  public targetIndex: number;
  public source: CdkDropList;
  public sourceIndex: number;
  public dragIndex: number;
  public activeContainer;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly reportService: ReportService,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly viewportRuler: ViewportRuler,
    public readonly _bsModalRef: BsModalRef
  ) {
    this.target = null;
    this.source = null;
  }

  public ngOnInit(): void {
    this.onClose = new Subject();
    if (this.editNC.ncGuid) {
      this.getById(false);
      this.isEdit = true;
    } else {
      this.isEdit = false;
      this.editNC.component = this.componentList[0].id;
    }
  }

  public onConfirm(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  // Get By id
  getById(formComponent) {
    this.loading = true;
    this.subscription.add(
      this.reportService.getByNonConfId(this.editNC.ncGuid).subscribe({
        next: (res: MasterNonConformanceDto) => {
          if (!formComponent) {
            this.editNC.images = res.images;
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  // Upload General Images
  getUploadedFiles(event, ncGuid, reportId, order, isIncludeinReport) {
    this.filesList = [];
    if (event.target.files !== 0 && event.target.files !== null && event.target.files !== undefined) {
      for (let i = 0; i < event.target.files.length; i++) {
        const mimeType = event.target.files[i].type;
        if (mimeType.match(/image\/*/) === null) {
          this.alertService.showErrorToast('The image is invalid, or not supported. Allowed types: png, jpg, jpeg');
          this.loading = false;
          event.target.value = '';
          return;
        }
      }
      const data =
        event.target.files.length > 1
          ? { imageFile: null, imageFiles: event.target.files }
          : { imageFile: event.target.files[0], imageFiles: [] };
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: data,
        class: 'modal-xl'
      };
      this.modalRef = this.modalService.show(ImageCropperComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(
        result => {
          this.loading = true;
          if (result) {
            if (Array.isArray(result)) {
              for (let i = 0; i < result.length; i++) {
                const imageBlob = this.reportService.getDataURItoBlob(result[i]);
                const imageName = event.target.files[i].name;
                const imageFile = new File([imageBlob], imageName, { type: result[i].type });
                this.filesList.push(imageFile);
              }
            } else {
              const imageBlob = this.reportService.getDataURItoBlob(result);
              const imageName = event.target.files[0].name;
              const imageFile = new File([imageBlob], imageName, { type: result.type });
              this.filesList.push(imageFile);
            }

            // If editing existing NC (has ncGuid), upload immediately
            if (ncGuid) {
              this.uploadImagesToServer(ncGuid, reportId, order, isIncludeinReport);
            } else {
              for (const file of this.filesList) {
                const reader = new FileReader();
                reader.onload = (e: any) => {
                  const imageData = {
                    file: file,
                    name: file.name,
                    preview: e.target.result
                  };
                  this.pendingImages.push(imageData);
                };
                reader.readAsDataURL(file);
              }
              this.filesList = [];
              this.loading = false;
            }
          } else {
            this.loading = false;
          }
        },
        err => {
          this.loading = false;
        }
      );
    }
  }

  private uploadImagesToServer(ncGuid: string, reportId: string, order: number, isIncludeinReport: boolean) {
    const tempArray: any = [];
    for (const i of this.filesList) {
      const formData: FormData = new FormData();
      formData.append('fileImage', i as File);
      formData.append('ReportId', reportId);
      formData.append('NCIId', '');
      formData.append('IMAGEID', '');
      formData.append('NCID', ncGuid);
      formData.append('GroupId', this.generalImagesGroupId);
      formData.append('Order', (order + 1).toString());
      formData.append('IsIncludeInReport', isIncludeinReport.toString());
      formData.append('OriginalImageGuid', '');
      formData.append('ImageURL', '');
      tempArray.push(this.reportService.getUploadedNonConfImage(formData));
    }
    forkJoin(tempArray).subscribe({
      next: (res: any) => {
        if (res) {
          if (this.editNC.ncGuid) {
            this.alertService.showSuccessToast('Images uploaded successfully.');
            this.getById(false);
          } else {
            this.onClose.next(true);
            this._bsModalRef.hide();
          }
          this.filesList = [];
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  // Save Changes
  createEditNC() {
    this.loading = true;
    if (this.editNC.ncGuid) {
      this.subscription.add(
        this.reportService.editNonConfReport(this.editNC).subscribe({
          next: res => {
            this.alertService.showSuccessToast(res.message);
            this.loading = false;
            this.onClose.next(true);
            this._bsModalRef.hide();
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    } else {
      this.loading = true;
      this.editNC.reportGuid = this.reportGuid;
      this.editNC.order = 0;
      this.editNC.ncGuid = null;
      this.editNC.images = null;
      this.editNC.componentList = null;
      this.subscription.add(
        this.reportService.createNC(this.editNC).subscribe({
          next: res => {
            this.alertService.showSuccessToast(res.message);
            if (this.pendingImages.length > 0) {
              this.filesList = this.pendingImages.map(img => img.file);
              this.uploadImagesToServer(res.id, this.reportGuid, this.editNC.order || 0, true);
              this.pendingImages = [];
            }
            this.loading = false;
            this.onClose.next(true);
            this._bsModalRef.hide();
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    }
  }

  removePendingImage(index: number): void {
    if (index >= 0 && index < this.pendingImages.length) {
      const imageToRemove = this.pendingImages[index];
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: `Are you sure you want to remove "${imageToRemove.name}"?`
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.subscription.add(
        this.modalRef.content.onClose.subscribe(result => {
          if (result) {
            this.pendingImages.splice(index, 1);
          }
        })
      );
    }
  }

  trackByFunction(index: number, item: any) {
    return index;
  }

  getDataFilter() {
    let i = 0;
    this.dir = this.dir === 'desc' ? 'asc' : 'desc';
    this.loading = true;
    this.subscription.add(
      this.reportService.getDatabySortby(this.editNC.reportGuid, 'ComponentStr', this.dir).subscribe((res: MasterNonConformanceDto) => {
        if (res) {
          this.editNC = res;
          for (const item of this.editNC) {
            item.order = i++;
          }
          this.loading = false;
        }
      })
    );
  }

  // After view init
  ngAfterViewInit() {
    if (this.placeholder && this.placeholder.element) {
      const phElement = this.placeholder.element.nativeElement;
      phElement.style.display = 'none';
      phElement.parentElement.removeChild(phElement);
    }
  }

  // Move images
  dragMoved(e: CdkDragMove) {
    const point = this.getPointerPositionOnPage(e.event);
    if (this.listGroup) {
      this.listGroup._items.forEach(dropList => {
        if (__isInsideDropListClientRect(dropList, point.x, point.y)) {
          this.activeContainer = dropList;
          return;
        }
      });
    }
  }

  // drop image
  dropListDropped() {
    let i = 0;
    if (!this.target) {
      return;
    }

    const phElement = this.placeholder.element.nativeElement;
    const parent = phElement.parentElement;

    phElement.style.display = 'none';

    parent.removeChild(phElement);
    parent.appendChild(phElement);
    parent.insertBefore(this.source.element.nativeElement, parent.children[this.sourceIndex]);

    this.target = null;
    this.source = null;

    if (this.sourceIndex !== this.targetIndex) {
      moveItemInArray(this.editNC.images, this.sourceIndex, this.targetIndex);
    }
    for (const item of this.editNC.images) {
      item.order = i++;
    }
  }

  // Drag and drop image
  dropListEnterPredicate = (drag: CdkDrag, drop: CdkDropList) => {
    if (drop === this.placeholder) {
      return true;
    }

    if (drop !== this.activeContainer) {
      return false;
    }

    const phElement = this.placeholder.element.nativeElement;
    const sourceElement = drag.dropContainer.element.nativeElement;
    const dropElement = drop.element.nativeElement;

    const dragIndex = __indexOf(dropElement.parentElement.children, this.source ? phElement : sourceElement);
    const dropIndex = __indexOf(dropElement.parentElement.children, dropElement);

    if (!this.source) {
      this.sourceIndex = dragIndex;
      this.source = drag.dropContainer;

      phElement.style.width = sourceElement.clientWidth + 'px';
      phElement.style.height = sourceElement.clientHeight + 'px';

      sourceElement.parentElement.removeChild(sourceElement);
    }

    this.targetIndex = dropIndex;
    this.target = drop;

    phElement.style.display = '';
    dropElement.parentElement.insertBefore(phElement, dropIndex > dragIndex ? dropElement.nextSibling : dropElement);

    this.placeholder._dropListRef.enter(drag._dragRef, drag.element.nativeElement.offsetLeft, drag.element.nativeElement.offsetTop);
    return false;
  };

  /** Determines the point of the page that was touched by the user. */
  getPointerPositionOnPage(event: MouseEvent | TouchEvent) {
    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.
    const point = __isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : event;
    const scrollPosition = this.viewportRuler.getViewportScrollPosition();

    return {
      x: point.pageX - scrollPosition.left,
      y: point.pageY - scrollPosition.top
    };
  }

  // Image delete in General Images section
  deleteNonConflImages(imageGuid) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: ErrorMessages.deleteMessage
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.subscription.add(
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.loading = true;
          this.subscription.add(
            this.reportService.deleteGeneralImages(imageGuid).subscribe({
              next: res => {
                this.loading = false;
                if (res) {
                  this.alertService.showSuccessToast(res.message);
                  this.getById(false);
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      })
    );
  }

  // Image Pop Up
  imagePopup(index) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: {
        imageList: this.editNC.images,
        selectedImageIndex: index
      }
    };
    this.modalRef = this.modalService.show(ModelComponent, ngModalOptions);
  }

  // JHA select/deseclect
  includeReport(imageGuid) {
    this.loading = true;
    this.subscription.add(
      this.reportService.updateIncludeImage(imageGuid).subscribe({
        next: res => {
          this.alertService.showSuccessToast(res.message);
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  dragEntered(event: CdkDragEnter<number>, generalImages: MasterGeneralImagesModel[]) {
    let i = 0;
    const drag = event.item;
    const dropList = event.container;
    const dragIndex = drag.data;
    const dropIndex = dropList.data;

    const phContainer = dropList.element.nativeElement;
    const phElement = phContainer.querySelector('.cdk-drag-placeholder');
    phContainer.removeChild(phElement);
    phContainer.parentElement.insertBefore(phElement, phContainer);

    moveItemInArray(generalImages, dragIndex, dropIndex);
    for (const item of this.editNC.images) {
      item.order = i++;
    }
  }

  // Destroy
  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.subscription.unsubscribe();
  }
}
function __indexOf(collection, node) {
  return Array.prototype.indexOf.call(collection, node);
}

/** Determines whether an event is a touch event. */
function __isTouchEvent(event: MouseEvent | TouchEvent): event is TouchEvent {
  return event.type.startsWith('touch');
}

function __isInsideDropListClientRect(dropList: CdkDropList, x: number, y: number) {
  const { top, bottom, left, right } = dropList.element.nativeElement.getBoundingClientRect();
  return y >= top && y <= bottom && x >= left && x <= right;
}
