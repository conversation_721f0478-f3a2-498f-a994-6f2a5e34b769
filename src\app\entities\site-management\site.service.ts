import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { from, map, Observable } from 'rxjs';
import { ApiUrl } from '../../@shared/constants';
import { Dropdown } from '../../@shared/models/dropdown.model';
import { MessageVM } from '../../@shared/models/messageVM.model';
import { ReportType } from '../../@shared/models/report.model';
import { QECategoryType, QEServiceCategoryTypeList, QEServiceTypeGroupInfo, Site, SiteAuditHistory } from '../../@shared/models/site.model';
import { AutomationSitePortfolio } from '../availability/reports/report.model';
import * as uuid from 'uuid';

@Injectable({
  providedIn: 'root'
})
export class SiteService {
  siteList = [];
  automationSiteList = [];
  constructor(private readonly http: HttpClient) {}

  createSite(site: Site): Observable<any> {
    this.siteList = [];
    this.automationSiteList = [];
    return this.http.post(ApiUrl.CREATE_SITE, site);
  }

  getAllSitesByfilter(obj): Observable<any> {
    return this.http.post(ApiUrl.SITES_BY_FILTER, obj);
  }

  getAllDataSource() {
    return this.http.get(ApiUrl.GET_DATASOURCE);
  }

  getPrimaryDevices(siteid) {
    return this.http.get(`${ApiUrl.GET_PRIMARYDEVICES}?siteId=${siteid}`);
  }

  isVGTScopeAvailableForSite(customerId: number, siteId: number): Observable<boolean> {
    return this.http.get<boolean>(`${ApiUrl.IS_VGT_SCOPE_AVAILBLE_FOR_SITE}?customerId=${customerId}&siteId=${siteId}`);
  }

  getAllSiteList(dataSource, partner, siteNumber) {
    return this.http.get(
      `${ApiUrl.GET_SITELIST}?automationDataSourceId=${dataSource}&automationDataSourcePartnerId=${partner}&atomationSiteId=${siteNumber}`
    );
  }

  activeSite(object: Site): Observable<any> {
    this.siteList = [];
    this.automationSiteList = [];
    return this.http.put(ApiUrl.ACTIVE_SITE, object);
  }

  updateSite(site: Site): Observable<any> {
    this.siteList = [];
    this.automationSiteList = [];
    return this.http.put(ApiUrl.CREATE_SITE, site);
  }

  getById(id) {
    return this.http.get(`${ApiUrl.GET_BY_ID_SITE}${id}`);
  }

  getPhotoImageById(id) {
    return this.http.get(`${ApiUrl.GET_PHOTO_GALLERY_PHOTO_IMAGES}?siteId=${id}`);
  }

  deleteSite(id): Observable<any> {
    this.siteList = [];
    this.automationSiteList = [];
    return this.http.delete(`${ApiUrl.DELETE_SITES}${id}`);
  }

  deleteDataSource(dataSource) {
    return this.http.put(ApiUrl.DELETE_DATASOURCE, dataSource);
  }

  clearSite() {
    this.siteList = [];
    this.automationSiteList = [];
  }

  getAllSite(): Observable<any> {
    return this.http.get(ApiUrl.GET_ALL_SITE);
  }

  getAllSitesByPortfolioId(isActive, portfolioId, customerId?, includeArchived = false): Observable<any> {
    const response = new Promise((resolve, reject) => {
      if (this.siteList.length > 0) {
        resolve(this.getValueByPortfolio(portfolioId, customerId, false, includeArchived));
      } else {
        this.http.get(`${ApiUrl.GET_ALL_SITE_BY_PORTFOLIOID}${isActive}&portfolioId=${null}`).subscribe({
          next: (res: any) => {
            this.siteList = res;
            resolve(this.getValueByPortfolio(portfolioId, customerId, false, includeArchived));
          },
          error: e => {
            reject(e);
          }
        });
      }
    });
    return from(response);
  }

  getSiteUplodedImage(file: FormData): Observable<MessageVM> {
    return this.http.post<MessageVM>(ApiUrl.SITE_IMAGE_UPLOAD_IMAGES, file);
  }

  getSitePhotoGalleryUplodedImage(file: FormData): Observable<MessageVM> {
    return this.http.post<MessageVM>(ApiUrl.PHOTO_GALLERY_UPLOAD_IMAGES, file);
  }

  // Delete Image
  deleteSiteImage(siteImageId): Observable<MessageVM> {
    return this.http.delete<MessageVM>(`${ApiUrl.SITE_IMAGE_DELETE_IMAGES}${siteImageId}`);
  }

  deletePhotoGalleryImage(photogalleryImageId): Observable<MessageVM> {
    return this.http.delete<MessageVM>(`${ApiUrl.PHOTO_GALLERY_DELETE_IMAGES}${photogalleryImageId}`);
  }

  selectKeyPhoto(data) {
    return this.http.post<MessageVM>(ApiUrl.SELECT_PHOTO_AS_A_KEY_PHOTO, data);
  }

  getPMSitePhotoImages(data): Observable<any> {
    return this.http.post(ApiUrl.GET_PM_PHOTO_IMAGES, data);
  }
  getCMSitePhotoImages(data): Observable<any> {
    return this.http.post(ApiUrl.GET_CM_PHOTO_IMAGES, data);
  }

  copyPMPhotoToMaster(data): Observable<MessageVM> {
    return this.http.post<MessageVM>(ApiUrl.MOVE_PM_PHOTO_IMAGES_TO_MASTER, data);
  }

  copyCMPhotoToMaster(data): Observable<MessageVM> {
    return this.http.post<MessageVM>(ApiUrl.MOVE_CM_PHOTO_IMAGES_TO_MASTER, data);
  }

  getAllReportSitesByPortfolioId(obj, includeArchived = false): Observable<any> {
    const response = new Promise((resolve, reject) => {
      if (this.siteList.length > 0) {
        resolve(this.getValueByPortfolio(obj, null, true, includeArchived));
      } else {
        const ddOption = { ids: [], isActive: true };
        this.http.post(ApiUrl.GET_REPORT_SITE, ddOption).subscribe({
          next: (res: ReportType[]) => {
            this.siteList = res;
            resolve(this.getValueByPortfolio(obj, null, true, includeArchived));
          },
          error: e => {
            reject(e);
          }
        });
      }
    });
    return from(response);
  }

  getValueByPortfolio(portfolioId, customerId, isMultipleIds = false, includeArchived) {
    if (isMultipleIds) {
      if (portfolioId && portfolioId.ids && portfolioId.ids.length) {
        let tempObj = [];
        for (const i of portfolioId.ids) {
          const data = this.filterSites(i, null, includeArchived);
          tempObj = tempObj.concat(data);
        }
        return tempObj;
      } else if (portfolioId && portfolioId.customerIds && portfolioId.customerIds.length) {
        let tempObj = [];
        for (const i of portfolioId.customerIds) {
          const data = this.filterSites(null, i, includeArchived);
          tempObj = tempObj.concat(data);
        }
        return tempObj;
      } else {
        return this.filterSites(null, null, includeArchived);
      }
    } else {
      if (portfolioId) {
        return this.filterSites(portfolioId, null, includeArchived);
      } else if (customerId) {
        return this.filterSites(null, customerId, includeArchived);
      } else {
        return this.filterSites(null, null, includeArchived);
      }
    }
  }

  filterSites(portfolioId: string | null, customerId: string | null, includeArchived: boolean): ReportType[] {
    if (portfolioId !== null) {
      if (includeArchived) {
        return this.siteList.filter(i => i.portfolioId === portfolioId);
      } else {
        return this.siteList.filter(i => i.portfolioId === portfolioId && !i.isArchive);
      }
    } else if (customerId !== null) {
      if (includeArchived) {
        return this.siteList.filter(i => i.customerId === customerId);
      } else {
        return this.siteList.filter(i => i.customerId === customerId && !i.isArchive);
      }
    } else {
      if (includeArchived) {
        return this.siteList;
      } else {
        return this.siteList.filter(i => !i.isArchive);
      }
    }
  }

  getAllSiteAuditJHASitesByPortfolioId(): Observable<any> {
    return this.http.get(`${ApiUrl.GET_SITE_AUDIT_SITE}?IsSiteAuditJHA=true`);
  }

  getAllSiteAuditReportSitesByPortfolioId(): Observable<any> {
    return this.http.get(`${ApiUrl.GET_SITE_AUDIT_REPORT_SITE}?IsSiteAuditJHA=false`);
  }

  deleteSiteContact(id): Observable<any> {
    return this.http.delete(`${ApiUrl.DELETE_SITE_CONTACT}?id=${id}`);
  }

  filterAutomationSitesByPortfolioIds(
    data: { customerIds: number[]; isAvailabilityCheck: boolean; portfolioIds: number[] },
    isPerformanceModule
  ) {
    if (data.portfolioIds.length === 0 && isPerformanceModule) {
      // Return all automation sites if no portfolioIds are provided
      return this.automationSiteList;
    }

    let filteredSites = [];
    for (const i of data.portfolioIds) {
      const sites = this.automationSiteList.filter(p => p.portfolioId === i);
      filteredSites = filteredSites.concat(sites);
    }
    return filteredSites;
  }

  getAllAutomationSiteById(data: AutomationSitePortfolio, fromCache = true, isPerformanceModule = false): Observable<any> {
    const response = new Promise((resolve, reject) => {
      if (this.automationSiteList.length > 0 && fromCache) {
        resolve(this.filterAutomationSitesByPortfolioIds(data, isPerformanceModule));
      } else {
        const requestData = {
          customerIds: data.customerIds,
          isAvailabilityCheck: data.isAvailabilityCheck,
          portfolioIds: [] // in case if user reloads the page, then first we need to get all the sites then will filter portfolio wise.
        };
        this.http.post(ApiUrl.GET_AVAILABILITY_SITE, requestData).subscribe({
          next: (res: ReportType[]) => {
            this.automationSiteList = res;

            resolve(this.filterAutomationSitesByPortfolioIds(data, isPerformanceModule));
          },
          error: e => {
            reject(e);
          }
        });
      }
    });
    return from(response);
  }

  getAllDataSourceList(isErrorPage): Observable<Dropdown[]> {
    return this.http.get<Dropdown[]>(!isErrorPage ? ApiUrl.GET_DATA_SOURCE_LIST : ApiUrl.GET_DATA_SOURCE_LIST_API_ERROR_PAGE);
  }

  getSiteTimeZoneBaseOnLongitudeLatitude(data: any): Observable<any> {
    return this.http.post(ApiUrl.GET_SITE_TIMEZONE_LONGITUDE_LATITUDE, data);
  }

  getFIPSCodeBaseOnLongitudeLatitude(data: any): Observable<any> {
    return this.http.post(ApiUrl.GET_FIPS_CODE_BY_LONGITUDE_LATITUDE, data);
  }

  getAllAutomationSitesByDataSource(ids: number[]): Observable<any> {
    return this.http.get<Dropdown[]>(ApiUrl.GET_AUTOMATION_SITE_LIST);
    // const response = new Promise((resolve, reject) => {
    //   if (this.automationSiteList.length > 0) {
    //      resolve(this.getValueByDataSource(ids, true));
    //   } else {
    //     this.http.get(ApiUrl.GET_AUTOMATION_SITE_LIST).subscribe({
    //       next: (res: Dropdown[]) => {
    //         this.automationSiteList = res;
    //          resolve(this.getValueByDataSource(ids, true));
    //       },
    //       error: e => {
    //         reject(e);
    //       }
    //     });
    //   }
    // });
    // return from(response);
  }

  getValueByDataSource(dataSourceIds, isMultipleIds = false) {
    if (isMultipleIds) {
      if (dataSourceIds && dataSourceIds.length) {
        let tempObj = [];
        for (const i of dataSourceIds) {
          const data = this.automationSiteList.filter(p => p.id === i);
          tempObj = tempObj.concat(data);
        }
        return tempObj;
      } else {
        this.http.get(ApiUrl.GET_AUTOMATION_SITE_LIST).subscribe({
          next: (res: Dropdown[]) => {
            this.automationSiteList = res;
          },
          error: e => {}
        });
        return this.automationSiteList;
      }
    } else {
      if (dataSourceIds) {
        return this.automationSiteList.filter(i => i.id === dataSourceIds);
      } else {
        return this.automationSiteList;
      }
    }
  }

  getExpectedPerformanceDataById(id: number): Observable<any> {
    return this.http.get(`${ApiUrl.GET_EXPECTED_PERFORMANCE_DATA}/${id}`);
  }

  generatePerformanceRecord(data): Observable<any> {
    return this.http.post(ApiUrl.GET_EXPECTED_PERFORMANCE_DATA, data);
  }
  getSiteHistoryData(siteId): Observable<SiteAuditHistory[]> {
    return this.http.get<SiteAuditHistory[]>(`${ApiUrl.GET_SITE_HISTORY_DATA}/${siteId}`);
  }

  getSiteArchiveData(id): Observable<any> {
    return this.http.get(`${ApiUrl.GET_SITE_ARCHIVE}/${id}`);
  }
  updateSitesArchiveStatus(UpdatedArchiveData: any): Observable<any> {
    return this.http.post(`${ApiUrl.UPDATE_SITE_ARCHIVE_STATUS}`, UpdatedArchiveData);
  }

  getSiteTypeNERCDropDown(): Observable<Dropdown[]> {
    return this.http.get<Dropdown[]>(ApiUrl.GET_NERC_SITE_TYPE);
  }

  getQEServiceTypeDropDown(): Observable<QEServiceCategoryTypeList[]> {
    const isRequired = (item: QECategoryType) =>
      QEServiceTypeGroupInfo.requiredGroupNames.includes(item.groupName) || QEServiceTypeGroupInfo.requiredGroupIds.includes(item.groupId);

    const isSingleSelectable = (item: QECategoryType) =>
      QEServiceTypeGroupInfo.singleSelectableGroupNames.includes(item.groupName) ||
      QEServiceTypeGroupInfo.singleSelectableGroupIds.includes(item.groupId);

    const modifiedCategoryTypeList = (category, item) => ({
      ...category,
      disabled: false,
      parentGroupId: item.groupId,
      parentGroupName: item.groupName,
      isSingleSelectable: isSingleSelectable(category),
      isRequired: isRequired(category)
    });

    const modifiedServiceCategoryTypeList = item => ({
      ...item,
      id: uuid.v4(),
      disabled: false,
      isSingleSelectable: isSingleSelectable(item),
      isRequired: isRequired(item),
      categoryList: item.categoryList.map(category => modifiedCategoryTypeList(category, item))
    });

    return this.http
      .get<Omit<QEServiceCategoryTypeList, 'disabled'>[]>(ApiUrl.GET_QE_SERVICE_TYPE_DROPDOWN)
      .pipe(map(res => res.map(modifiedServiceCategoryTypeList)));
  }

  downloadSiteTemplate(obj): Observable<any> {
    return this.http.post(ApiUrl.DOWNLOAD_SITE_TEMPLATE, obj, { responseType: 'blob' as 'json' });
  }

  uploadSites(formData): Observable<any> {
    return this.http.post(ApiUrl.UPLOAD_SITES, formData);
  }

  exportSitesTemplate(obj): Observable<any> {
    return this.http.post(ApiUrl.EXPORT_SITES_TEMPLATE, obj, { responseType: 'blob' as 'json' });
  }

  uploadSiteUpdateFile(data: FormData): Observable<any> {
    return this.http.post(ApiUrl.UPLOAD_SITES_UPDATES, data);
  }
}
