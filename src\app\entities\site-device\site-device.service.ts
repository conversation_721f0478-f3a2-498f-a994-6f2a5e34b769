import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { CommonFilter } from '../../@shared/components/filter/common-filter.model';
import { ApiUrl } from '../../@shared/constants';
import { CommonResponse, Dropdown } from '../../@shared/models/dropdown.model';
import { AutomationDeviceList, SiteAutomationDevicesList } from '../../@shared/models/site.model';
import {
  AddSiteDevice,
  DataSourceModel,
  DeviceModel,
  ImportSiteDevice,
  ListOfSites,
  SiteDevice,
  SiteDeviceModel,
  SiteDeviceType,
  SiteDevicesList
} from './site-device.model';

@Injectable({
  providedIn: 'root'
})
export class SiteDeviceService {
  addSiteDevice: AddSiteDevice = null;
  importSiteDevice: ImportSiteDevice = null;
  tabName: string = null;

  constructor(private readonly http: HttpClient) {}

  getAllSiteDeviceList(filter: CommonFilter): Observable<SiteDevicesList> {
    return this.http.post<SiteDevicesList>(ApiUrl.GET_DEVICE_LIST, filter);
  }

  getAllDeviceType(): Observable<Dropdown[]> {
    return this.http.get<Dropdown[]>(ApiUrl.GET_SITE_DEVICE_TYPE);
  }

  getDeviceModel(): Observable<Dropdown[]> {
    return this.http.get<Dropdown[]>(ApiUrl.GET_SITE_DEVICE_MODEL);
  }

  getDeviceMfg(): Observable<Dropdown[]> {
    return this.http.get<Dropdown[]>(ApiUrl.GET_SITE_DEVICE_MFG);
  }

  getDataSource(showQEDataSource = true) {
    return this.http.get(`${ApiUrl.GET_AUTOMATION_DATASOURCE}?showQEDatasource=${showQEDataSource}`);
  }

  getSite(dataSourceId): Observable<ListOfSites[]> {
    return this.http.get<ListOfSites[]>(`${ApiUrl.GET_SITE}?automationDataSourceId=${dataSourceId}`);
  }

  getDevicePlottingReporting(id, deviceType, automationSiteDetailId) {
    return this.http.get(
      `${ApiUrl.GET_DEVICE_PERFORMANCE}?siteId=${id}&deviceType=${deviceType}&automationSiteDetailId=${automationSiteDetailId}`
    );
  }

  getDeviceQeName(id, deviceType) {
    return this.http.get(`${ApiUrl.GET_DEVICE_QENAME}?siteId=${id}&deviceTypeId=${deviceType}`);
  }

  getDeviceCustomer(id) {
    return this.http.get(`${ApiUrl.GET_DEVICE_CUSTOMER}?automationSiteDetailId=${id}`);
  }

  getDevicePlotingReporting(id, deviceType) {
    return this.http.get(`${ApiUrl.GET_DEVICE_PERFORMANCE}?siteId=${id}&deviceType=${deviceType}`);
  }

  getDeviceQe(id, automationId, deviceType, automationSiteDetailId) {
    return this.http.get(
      `${ApiUrl.GET_DEVICE_QE}?siteId=${id}&automationSiteDetailId=${automationSiteDetailId}&automationDeviceId=${automationId}&deviceType=${deviceType}`
    );
  }

  getDeviceTypeDataById(id: number): Observable<SiteDeviceType[]> {
    return this.http.get<SiteDeviceType[]>(`${ApiUrl.GET_DEVICE_TYPE_DATA}?id=${id}`);
  }

  getAllDeviceModel(id: number): Observable<Dropdown[]> {
    return this.http.get<Dropdown[]>(`${ApiUrl.GET_DEVICE_MODEL}?id=${id}`);
  }

  getDeviceModelDetail(id: number): Observable<SiteDeviceModel> {
    return this.http.get<SiteDeviceModel>(`${ApiUrl.GET_DEVICE_MODEL_DETAIL}?id=${id}`);
  }

  getSiteDeviceById(id: number): Observable<SiteDevice> {
    return this.http.get<SiteDevice>(`${ApiUrl.GET_DEVICE_TYPE_BY_ID}?id=${id}`);
  }

  createSiteDevice(data: SiteDevice): Observable<SiteDevice> {
    return this.http.post<SiteDevice>(ApiUrl.GET_DEVICE_CREATE, data);
  }

  updateSiteDevice(id: number, data: SiteDevice): Observable<SiteDevice> {
    return this.http.put<SiteDevice>(ApiUrl.GET_DEVICE_EDIT, data);
  }

  deleteSiteDevice(id: number): Observable<CommonResponse> {
    return this.http.put<CommonResponse>(`${ApiUrl.DELETE_DEVICE}?id=${id}`, null);
  }
  getAllMVPMDeviceType(): Observable<Dropdown[]> {
    return this.http.get<Dropdown[]>(ApiUrl.GET_DEVICE_MVPM_TYPES);
  }
  deviceTypeName(id, siteId): Observable<Dropdown[]> {
    return this.http.get<Dropdown[]>(`${ApiUrl.GET_SITE_DEVICE_NAME}?deviceType=${id}&siteId=${siteId}`);
  }

  importDevices(obj: DeviceModel): Observable<any> {
    return this.http.post<any>(ApiUrl.IMPORT_DEVICES, obj);
  }

  getDataSourceList(id: number): Observable<DataSourceModel[]> {
    return this.http.get<DataSourceModel[]>(`${ApiUrl.GET_DATA_SOURCE}?siteId=${id}`);
  }

  getSiteAutomationDeviceList(id): Observable<SiteAutomationDevicesList> {
    return this.http.get<SiteAutomationDevicesList>(`${ApiUrl.GET_AUTOMATION_DEVICE_LIST}?siteid=${id}`).pipe(
      map(res => ({
        ...res,
        siteDevices: res.siteDevices.map(x => ({
          ...x,
          refetchError: x.refetchError === '-' ? '' : x.refetchError
        }))
      }))
    );
  }

  updateSiteAutomationDeviceList(data: AutomationDeviceList[]): Observable<AutomationDeviceList[]> {
    return this.http.post<AutomationDeviceList[]>(`${ApiUrl.GET_AUTOMATION_DEVICE_LIST_UPDATE}`, data);
  }

  downloadDeviceTemplate(obj): Observable<any> {
    return this.http.post(ApiUrl.DOWNLOAD_DEVICE_TEMPLATE, obj, { responseType: 'blob' as 'json' });
  }

  uploadDevices(formData): Observable<any> {
    return this.http.post(ApiUrl.IMPORT_BULK_DEVICES, formData);
  }

  setAddDevicePrePopulateData(data: AddSiteDevice) {
    this.addSiteDevice = data;
  }

  getAddDevicePrePopulateData() {
    if (this.addSiteDevice) {
      return this.addSiteDevice;
    }
  }

  exportSiteAutomationDevices(data): Observable<any> {
    return this.http.post(ApiUrl.EXPORT_SITE_AUTOMATION_DEVICES, data, { responseType: 'blob' as 'json' });
  }

  uploadSiteAutomationDevices(data: FormData): Observable<any> {
    return this.http.post(ApiUrl.UPLOAD_SITE_AUTOMATION_DEVICES, data);
  }

  setImportDevicePrePopulateData(data: ImportSiteDevice) {
    this.importSiteDevice = data;
  }

  getImportDevicePrePopulateData() {
    if (this.importSiteDevice) {
      return this.importSiteDevice;
    }
  }

  setOpenTabName(name: string) {
    this.tabName = name;
  }

  getOpenTabName() {
    return this.tabName;
  }

  getLatestDeviceValues(hardwareIds: number[], customerId: number, siteId: number): Observable<any> {
    const payload = {
      hardwareIds: hardwareIds,
      customerId: customerId,
      siteId: siteId
    };
    return this.http.post<any>(ApiUrl.GET_LATEST_DEVICE_VALUE, payload);
  }
}
