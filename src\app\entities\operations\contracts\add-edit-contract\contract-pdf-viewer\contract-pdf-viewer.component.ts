import { HttpClient } from '@angular/common/http';
import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';
import { lastValueFrom, Subject } from 'rxjs';

(pdfjsLib as any).GlobalWorkerOptions.workerSrc = pdfjsWorker;
@Component({
  selector: 'sfl-contract-pdf-viewer',
  templateUrl: './contract-pdf-viewer.component.html',
  styleUrls: ['./contract-pdf-viewer.component.scss']
})
export class ContractPdfViewerComponent implements OnInit, AfterViewInit {
  @Input() pdfUrl!: string;
  @ViewChild('pdfContainer', { static: true }) pdfContainer!: ElementRef<HTMLDivElement>;
  loading = false;
  public onClose: Subject<any>;
  constructor(private readonly http: HttpClient, public _bsModalRef: BsModalRef) {}

  ngOnInit() {
    this.onClose = new Subject();
  }

  ngAfterViewInit(): void {
    this.loadPdf();
    document.addEventListener('keydown', function (e) {
      if ((e.ctrlKey || e.metaKey) && (e.key === 'p' || e.key === 's')) {
        e.preventDefault();
      }
    });
  }

  async loadPdf() {
    const container = this.pdfContainer.nativeElement;

    if (!this.pdfUrl) {
      return;
    }

    try {
      const pdfData = await lastValueFrom(this.http.get(this.pdfUrl, { responseType: 'arraybuffer' }));

      pdfjsLib.GlobalWorkerOptions.workerSrc = '/assets/js/pdf.worker.js';

      const pdf = await pdfjsLib.getDocument({ data: pdfData }).promise;

      for (let pageNumber = 1; pageNumber <= pdf.numPages; pageNumber++) {
        const page = await pdf.getPage(pageNumber);
        const viewport = page.getViewport({ scale: 1.2 });

        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d')!;
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = { canvasContext: context, viewport };
        await page.render(renderContext).promise;

        container.appendChild(canvas);
      }
    } catch (err) {
      console.error('Failed to load or render PDF:', err);
    }
  }

  onModalClose() {
    this._bsModalRef.hide();
    this.onClose.next(true);
  }
}
